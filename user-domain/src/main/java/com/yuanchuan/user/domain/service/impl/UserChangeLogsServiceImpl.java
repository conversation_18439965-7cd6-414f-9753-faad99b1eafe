package com.yuanchuan.user.domain.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.yuanchuan.user.domain.model.UserChangeLogs;
import com.yuanchuan.user.domain.repository.UserChangeLogsRepository;
import com.yuanchuan.user.domain.service.UserChangeLogsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 用户变更日志服务实现类
 * 提供记录用户信息变更的功能
 *
 * <AUTHOR>
 * @date 2025-05-22
 */
@Slf4j
@Service
public class UserChangeLogsServiceImpl implements UserChangeLogsService {

    @Autowired
    private UserChangeLogsRepository userChangeLogsRepository;

    @Override
    public boolean createUserChangeLogs(Long userId, Object oldValue, Object newValue, String deviceId, String source) {
        try {
            if (userId == null) {
                log.warn("创建用户变更日志失败: 用户ID不能为空");
                return false;
            }

            UserChangeLogs changeLogs = new UserChangeLogs();
            changeLogs.setUserId(userId);
            changeLogs.setOldValue(JSONObject.toJSONString(oldValue));
            changeLogs.setNewValue(JSONObject.toJSONString(newValue));
            changeLogs.setDeviceId(deviceId);
            changeLogs.setSource(source);
            changeLogs.setCreatedBy("system");
            changeLogs.setUpdatedBy("system");
            changeLogs.setCreatedAt(new Date());
            changeLogs.setUpdatedAt(new Date());
            changeLogs.setActive(1);

            return userChangeLogsRepository.insert(changeLogs);
        } catch (Exception e) {
            log.error("创建用户变更日志失败: " + e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean createUserChangeLogs(Long userId, Object oldValue, Object newValue, String source) {
        // 调用完整版本的方法，设备ID为null
        return createUserChangeLogs(userId, oldValue, newValue, null, source);
    }

    @Override
    public boolean createFieldChangeLogs(Long userId, String fieldName, Object oldValue, Object newValue, String deviceId, String source) {
        try {
            if (userId == null) {
                log.warn("创建字段变更日志失败: 用户ID不能为空");
                return false;
            }

            if (fieldName == null || fieldName.isEmpty()) {
                log.warn("创建字段变更日志失败: 字段名不能为空");
                return false;
            }

            // 创建包含字段名和值的Map
            Map<String, Object> oldValueMap = null;
            if (oldValue != null) {
                oldValueMap = new HashMap<>();
                oldValueMap.put(fieldName, oldValue);
            }

            Map<String, Object> newValueMap = null;
            if (newValue != null) {
                newValueMap = new HashMap<>();
                newValueMap.put(fieldName, newValue);
            }

            // 调用基础方法创建日志
            return createUserChangeLogs(userId, oldValueMap, newValueMap, deviceId, source);
        } catch (Exception e) {
            log.error("创建字段变更日志失败: " + e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean createFieldChangeLogs(Long userId, String fieldName, Object oldValue, Object newValue, String source) {
        // 调用完整版本的方法，设备ID为null
        return createFieldChangeLogs(userId, fieldName, oldValue, newValue, null, source);
    }

    @Override
    public boolean createMultiFieldChangeLogs(Long userId, Map<String, Map<String, Object>> changedFields, String deviceId, String source) {
        try {
            if (userId == null) {
                log.warn("创建多字段变更日志失败: 用户ID不能为空");
                return false;
            }

            if (changedFields == null || changedFields.isEmpty()) {
                log.warn("创建多字段变更日志失败: 变更字段不能为空");
                return false;
            }

            // 创建旧值和新值的Map
            Map<String, Object> oldValueMap = new HashMap<>();
            Map<String, Object> newValueMap = new HashMap<>();

            // 遍历所有变更的字段
            for (Map.Entry<String, Map<String, Object>> entry : changedFields.entrySet()) {
                String fieldName = entry.getKey();
                Map<String, Object> valueMap = entry.getValue();

                if (valueMap.containsKey("oldValue")) {
                    oldValueMap.put(fieldName, valueMap.get("oldValue"));
                }

                if (valueMap.containsKey("newValue")) {
                    newValueMap.put(fieldName, valueMap.get("newValue"));
                }
            }

            // 调用基础方法创建日志
            return createUserChangeLogs(userId,
                    oldValueMap.isEmpty() ? null : oldValueMap,
                    newValueMap.isEmpty() ? null : newValueMap,
                    deviceId, source);
        } catch (Exception e) {
            log.error("创建多字段变更日志失败: " + e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean createMultiFieldChangeLogs(Long userId, Map<String, Map<String, Object>> changedFields, String source) {
        // 调用完整版本的方法，设备ID为null
        return createMultiFieldChangeLogs(userId, changedFields, null, source);
    }
}
