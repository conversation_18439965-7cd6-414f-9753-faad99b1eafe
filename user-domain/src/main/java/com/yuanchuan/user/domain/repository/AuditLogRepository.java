package com.yuanchuan.user.domain.repository;

import com.yuanchuan.user.domain.model.AuditLog;

import java.util.List;
import java.util.Optional;

/**
 * 审计日志仓储接口
 */
public interface AuditLogRepository {

    /**
     * 保存审计日志
     *
     * @param auditLog 审计日志
     * @return 保存后的审计日志
     */
    AuditLog save(AuditLog auditLog);

    /**
     * 根据ID查询审计日志
     *
     * @param id 日志ID
     * @return 审计日志
     */
    Optional<AuditLog> findById(Long id);

    /**
     * 根据业务类型和业务ID查询审计日志
     *
     * @param bizType 业务类型
     * @param bizId 业务ID
     * @return 审计日志列表
     */
    List<AuditLog> findByBizTypeAndBizId(String bizType, Long bizId);

    /**
     * 根据操作人ID查询审计日志
     *
     * @param operatorId 操作人ID
     * @return 审计日志列表
     */
    List<AuditLog> findByOperatorId(Long operatorId);

    /**
     * 分页查询审计日志
     *
     * @param bizType 业务类型
     * @param operatorId 操作人ID
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 审计日志列表
     */
    List<AuditLog> findByConditions(String bizType, Long operatorId, Integer offset, Integer limit);

    /**
     * 统计审计日志数量
     *
     * @param bizType 业务类型
     * @param operatorId 操作人ID
     * @return 日志数量
     */
    Long countByConditions(String bizType, Long operatorId);
}
