package com.yuanchuan.user.domain.service.impl;

import com.yuanchuan.user.domain.service.DeviceLimitService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.concurrent.TimeUnit;

/**
 * 设备限制服务实现类
 */
@Slf4j
@Service
public class DeviceLimitServiceImpl implements DeviceLimitService {
    
    /**
     * 设备换绑手机号次数限制前缀
     * 用途：记录设备每天换绑手机号的次数
     * 完整格式：user:device:phone:update:limit:[deviceId]
     * 值：换绑次数
     * 过期时间：当天结束
     */
    private static final String DEVICE_PHONE_UPDATE_LIMIT_PREFIX = "user:device:phone:update:limit:";

    /**
     * 每天每个设备最多换绑手机号次数
     */
    private static final int MAX_PHONE_UPDATE_PER_DAY = 3;
    
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    
    @Override
    public boolean checkPhoneUpdateLimit(String deviceId) {
        if (deviceId == null || deviceId.isEmpty()) {
            // 如果没有设备ID，默认不限制
            return true;
        }
        
        // 获取设备今天的换绑次数
        String key = DEVICE_PHONE_UPDATE_LIMIT_PREFIX + deviceId;
        String countStr = redisTemplate.opsForValue().get(key);
        int count = 0;
        if (countStr != null) {
            try {
                count = Integer.parseInt(countStr);
            } catch (NumberFormatException e) {
                log.error("解析设备换绑次数失败", e);
            }
        }
        
        // 检查是否超过限制
        return count < MAX_PHONE_UPDATE_PER_DAY;
    }
}
