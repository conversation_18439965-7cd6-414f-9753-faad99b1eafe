package com.yuanchuan.user.domain.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 用户第三方账号授权表
 * @TableName user_third_party_auth
 */
@Data
@Accessors(chain = true)
public class UserThirdPartyAuth {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 绑定ID，关联user_third_party_binding表
     */
    private Long bindingId;

    /**
     * 用户ID，关联user表
     */
    private Long customerAccountId;

    /**
     * 第三方平台：APPLE_ID-苹果ID ,FACE_BOOK-脸书 , GOOGLE-谷歌,LINK-link等
     */
    private String platform;

    /**
     * 访问令牌
     */
    private String accessToken;

    /**
     * 刷新令牌
     */
    private String refreshToken;

    /**
     * 令牌类型
     */
    private String tokenType;

    /**
     * 授权范围，多个范围用逗号分隔
     */
    private String scope;

    /**
     * 访问令牌过期时间
     */
    private Integer expiresAt;

    /**
     * 刷新令牌过期时间 单位秒或者时间戳
     */
    private Integer refreshExpiresAt;

    /**
     * 授权状态：ACTIVE-活跃，EXPIRED-过期，REVOKED-已撤销
     */
    private String status;

    /**
     * 最后刷新时间
     */
    private Date lastRefreshTime;

    /**
     * 刷新次数
     */
    private Integer refreshCount;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 更新时间
     */
    private Date updatedAt;

    /**
     * 是否启用 0-禁用(删除) 1-启用
     */
    private Integer active;
}