package com.yuanchuan.user.domain.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Random;

/**
 * 商户账户领域对象
 */
@Data
@Accessors(chain = true)
public class BusinessAccount implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 账号ID
     */
    private Long id;
    
    /**
     * 关联自然人ID
     */
    private Long userPersonId;
    
    /**
     * 账户名称
     */
    private String accountName;

    /**
     * 账号编码
     */
    private String accountCode;

    /**
     * 密码哈希
     */
    private String password;
    
    /**
     * 账户类型 SHOP-商户 、 OPERATION-运营
     */
    private Integer accountType;
    
    /**
     * 账户状态
     */
    private Integer accountStatus;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 创建人
     */
    private String createdBy;
    
    /**
     * 更新人
     */
    private String updatedBy;
    
    /**
     * 是否启用 0-禁用(删除) 1-启用
     */
    private Boolean active;

    /**
     * 组织ID
     */
    private Long orgId;




    
    /**
     * 生成商户账户名称
     * 规则：APP缩写+随机四位字符
     *
     * @return 商户账户名称
     */
    public static String generateMerchantAccountName() {
        // APP缩写，这里使用"QLQJ"作为示例
        String appPrefix = "QLQJ";
        
        // 生成随机四位字符（字母和数字的组合）
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        StringBuilder randomChars = new StringBuilder();
        Random random = new Random();
        
        for (int i = 0; i < 4; i++) {
            int index = random.nextInt(chars.length());
            randomChars.append(chars.charAt(index));
        }
        
        return appPrefix + randomChars.toString();
    }
}
