package com.yuanchuan.user.domain.service.impl;

import com.yuanchuan.common.exception.BusinessException;
import com.yuanchuan.common.exception.EntityNotFoundException;
import com.yuanchuan.user.context.enums.ContentType;
import com.yuanchuan.user.domain.model.ContentConfig;
import com.yuanchuan.user.domain.repository.ContentConfigRepository;
import com.yuanchuan.user.domain.service.ContentConfigDomainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 内容配置领域服务实现类
 */
@Slf4j
@Service
public class ContentConfigDomainServiceImpl implements ContentConfigDomainService {

    @Autowired
    private ContentConfigRepository contentConfigRepository;


    /**
     * 创建内容配置
     *
     * @param domain   内容配置DTO
     * @param operator 操作人
     * @return 创建后的内容配置ID
     */
    @Override
    @Transactional
    public Long createContentConfig(ContentConfig domain, String operator) {
        // 检查是否已存在相同类型和标题的内容配置
        ContentType type = domain.getType();
        contentConfigRepository.findByTypeAndTitle(type, domain.getTitle())
                .ifPresent(config -> {
                    throw new BusinessException("已存在相同類型與標題的內容設定");
                });

        // 转换为领域对象

        domain.setCreatedBy(operator);
        domain.setUpdatedBy(operator);
        domain.setCreatedAt(LocalDateTime.now());
        domain.setUpdatedAt(LocalDateTime.now());
        domain.setActive(true);

        // 如果未设置排序，则默认为0
        if (domain.getSortOrder() == null) {
            domain.setSortOrder(0);
        }

        // 如果未设置是否启用，则默认为启用
        if (domain.getIsEnabled() == null) {
            domain.setIsEnabled(true);
        }

        // 保存内容配置
        ContentConfig savedConfig = contentConfigRepository.save(domain);
        return savedConfig.getId();
    }

    /**
     * 更新内容配置
     *
     * @param id       内容配置ID
     * @param domain   内容配置DTO
     * @param operator 操作人
     * @return 更新后的内容配置
     */
    @Override
    @Transactional
    public ContentConfig updateContentConfig(Long id, ContentConfig domain, String operator) {
        // 查询内容配置
        ContentConfig contentConfig = contentConfigRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("内容配置不存在"));

        // 检查是否存在相同类型和标题的其他内容配置
        ContentType type = domain.getType();
        contentConfigRepository.findByTypeAndTitle(type, domain.getTitle())
                .ifPresent(config -> {
                    if (!config.getId().equals(id)) {
                        throw new BusinessException("已存在相同類型與標題的內容設定");
                    }
                });

        // 更新内容配置
        contentConfig.setUpdatedBy(operator);
        contentConfig.setUpdatedAt(LocalDateTime.now());

        // 保存内容配置
        return contentConfigRepository.save(contentConfig);
    }

    /**
     * 获取内容配置详情
     *
     * @param id 内容配置ID
     * @return 内容配置详情
     */
    @Override
    public ContentConfig getContentConfigDetail(Long id) {
        // 查询内容配置
        ContentConfig contentConfig = contentConfigRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("内容配置不存在"));

        List<ContentConfig> contentConfigs = new ArrayList<>();
        contentConfigs.add(contentConfig);
        // 如果是帮助类型的内容，构建子内容
        buildChildren(contentConfigs);
        return contentConfig;
    }


    /**
     * 根据条件动态查询内容配置列表
     *
     * @param contentConfig 查询条件，包含类型、启用状态等
     * @return 内容配置列表
     */
    @Override
    public List<ContentConfig> getContentConfigs(ContentConfig contentConfig) {
        // 如果没有传入查询条件，返回所有内容
        List<ContentConfig> contentConfigs = new ArrayList<>();
        if (contentConfig == null) {
            contentConfigs = contentConfigRepository.findAll();
            return contentConfigs;
        }
        // 直接传递整个对象进行动态查询
        contentConfigs = contentConfigRepository.findByCondition(contentConfig);
        buildChildren(contentConfigs);
        return contentConfigs;
    }

    /**
     * 根据类型获取启用的内容配置列表
     *
     * @param type 内容类型
     * @return 内容配置列表
     */
    @Override
    public List<ContentConfig> getEnabledContentConfigsByType(String type) {
        ContentType contentType = ContentType.valueOf(type);
        return contentConfigRepository.findEnabledByType(contentType);
    }


    /**
     * 获取所有启用的内容配置
     *
     * @return 内容配置列表
     */
    @Override
    public List<ContentConfig> getAllEnabledContentConfigs() {
        return contentConfigRepository.findAllEnabled();
    }

    /**
     * 获取所有内容配置（包括启用和禁用）
     *
     * @return 内容配置列表
     */
    @Override
    public List<ContentConfig> getAllContentConfigs() {
        return contentConfigRepository.findAll();
    }


    /**
     * 删除内容配置
     *
     * @param id 内容配置ID
     * @return 是否成功
     */
    @Override
    @Transactional
    public boolean deleteContentConfig(Long id) {
        return contentConfigRepository.deleteById(id);
    }


    private void buildChildren(List<ContentConfig> domainList) {

        if (CollectionUtils.isEmpty(domainList)) {
            return;
        }
        List<ContentConfig> collect = domainList.stream().filter(t -> t.getType() == ContentType.HELP).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            return;
        }
        List<Long> ids = collect.stream().map(t -> t.getId()).collect(Collectors.toList());
        List<ContentConfig> enabledByParentIds = contentConfigRepository.findEnabledByParentIds(ids);

        if (CollectionUtils.isEmpty(enabledByParentIds)) {
            return;
        }

        Map<Long, List<ContentConfig>> resultMap = enabledByParentIds.stream()
                .collect(Collectors.groupingBy(
                        ContentConfig::getParentId,
                        Collectors.toList()
                ));

        // 创建一个新的列表来存储所有的子内容
        List<ContentConfig> allChildren = new ArrayList<>();

        // 首先为每个内容设置子内容
        for (ContentConfig content : domainList) {
            List<ContentConfig> children = resultMap.get(content.getId());
            if (CollectionUtils.isEmpty(children)) {
                continue;
            }
            content.setChildren(children); // 设置子内容
            allChildren.addAll(children); // 收集所有子内容
        }

        // 在遍历完成后再添加所有子内容
        if (!allChildren.isEmpty()) {
            domainList.addAll(allChildren);
        }

    }


}
