package com.yuanchuan.user.domain.repository;

import com.yuanchuan.user.domain.model.UserThirdPartyAuth;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-05-13 16:17:16:17
 */
public interface UserThirdPartyAuthRespoitory {
    /**
     * 根据用户查询三方账号授权信息
     * @param userId
     * @return
     */
    List<UserThirdPartyAuth> findByUserId(Long userId);

    /**
     * 根据主键批量删除(逻辑）
     * @param partyAuthIds
     */
    boolean logicDeleteByIds(List<Long> partyAuthIds);

    /**
     * 查询绑定账号信息
     * @param bindingId
     * @return
     */
    UserThirdPartyAuth queryByBindingId(Long bindingId);

    UserThirdPartyAuth save(UserThirdPartyAuth userThirdPartyAuth);

    /**
     * 根据第三方平台的 accessToken 查询用户第三方绑定信息
     *
     * @param accessToken 第三方平台的 accessToken
     * @return 用户第三方绑定信息
     */
    UserThirdPartyAuth findByAccessToken(String accessToken);

}
