package com.yuanchuan.user.domain.model;

import lombok.Data;

import java.util.Date;

/**
 * 角色权限关联表
 * @TableName role_permission
 */
@Data
public class RolePermission {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 角色ID
     */
    private Long roleId;

    /**
     * 权限ID
     */
    private Long permissionId;

    /**
     * 授权类型：GRANT-授权，DENY-拒绝
     */
    private String grantType;

    /**
     * 是否启用 0-禁用(删除) 1-启用
     */
    private Integer active;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 更新时间
     */
    private Date updatedAt;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新人
     */
    private String updatedBy;
}