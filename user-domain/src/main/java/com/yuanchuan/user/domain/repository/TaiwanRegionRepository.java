package com.yuanchuan.user.domain.repository;

import com.yuanchuan.user.domain.model.TaiwanRegion;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * 台湾行政区划仓储接口
 * <AUTHOR>
 * @date 2025-05-19 16:32:16:32
 */
public interface TaiwanRegionRepository {

    /**
     * 根据经纬度查询最近的行政区划
     *
     * @param longitude 经度
     * @param latitude 纬度
     * @return 行政区划信息
     */
    Optional<TaiwanRegion> findByCoordinate(BigDecimal longitude, BigDecimal latitude);

    /**
     * 根据名称查询行政区划
     *
     * @param name 名称
     * @return 行政区划信息
     */
    Optional<List<TaiwanRegion>> findByName(String name);

    /**
     * 根据ID查询行政区划
     *
     * @param id 行政区划ID
     * @return 行政区划信息
     */
    Optional<TaiwanRegion> findById(Long id);

    /**
     * 根据行政区划代码查询行政区划
     *
     * @param code 行政区划代码
     * @return 行政区划信息
     */
    Optional<TaiwanRegion> findByCode(String code);

    /**
     * 根据ID查询父级行政区划
     *
     * @param id 行政区划ID
     * @return 父级行政区划信息
     */
    Optional<TaiwanRegion> findParentById(Long id);

}
