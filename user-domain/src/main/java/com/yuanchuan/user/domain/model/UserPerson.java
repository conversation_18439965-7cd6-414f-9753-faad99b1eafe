package com.yuanchuan.user.domain.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 自然人领域对象
 */
@Data
@Accessors(chain = true)
public class UserPerson {
    /**
     * 用户ID
     */
    private Long id;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 来源
     */
    private String source;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;


    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新人
     */
    private String updatedBy;


    /**
     * 是否启用 0-禁用(删除) 1-启用
     */
    private Boolean active;


    /**
     * 用户账号领域对象
     */
    private CustomerAccount customerAccount;

    /**
     * 商户领域对象
     */
    private BusinessAccount businessAccount;

    /**
     * 手机号是否注册标识 true 是
     */
    private boolean registerFlag;

    /**
     * 是否跨平台用户
     *
     */
    private Boolean isCrossPlatformUser  = Boolean.FALSE;

}
