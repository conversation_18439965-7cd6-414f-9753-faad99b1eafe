package com.yuanchuan.user.domain.service.impl;

import com.yuanchuan.common.enums.users.UsersErrorCode;
import com.yuanchuan.common.exception.BusinessException;
import com.yuanchuan.user.domain.model.CustomerAccount;
import com.yuanchuan.user.domain.model.UserPerson;
import com.yuanchuan.user.domain.model.UserThirdPartyAuth;
import com.yuanchuan.user.domain.model.UserThirdPartyBinding;
import com.yuanchuan.user.domain.repository.CustomerAccountRepository;
import com.yuanchuan.user.domain.repository.UserPersonRepository;
import com.yuanchuan.user.domain.repository.UserThirdPartyAuthRespoitory;
import com.yuanchuan.user.domain.repository.UserThirdPartyBindingRespoitory;
import com.yuanchuan.user.domain.service.ThirdPartyBindingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 第三方绑定服务实现类
 */
@Slf4j
@Service
public class ThirdPartyBindingServiceImpl implements ThirdPartyBindingService {

    @Autowired
    private UserThirdPartyBindingRespoitory userThirdPartyBindingRespoitory;

    @Autowired
    private UserThirdPartyAuthRespoitory userThirdPartyAuthRespoitory;

    @Autowired
    private CustomerAccountRepository customerAccountRepository;

    @Autowired
    private UserPersonRepository userPersonRepository;

    @Override
    public List<UserThirdPartyBinding> getBindingList(Long userId) {
        // 参数校验
        if (userId == null) {
            throw new IllegalArgumentException("使用者 ID 不可為空");
        }

        // 查询用户自然人信息
        UserPerson userPerson = userPersonRepository.findById(userId)
                .orElseThrow(() -> new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg()));

        // 查询用户账号信息
        CustomerAccount account = customerAccountRepository.findByUserPersonId(userPerson.getId())
                .orElseThrow(() -> new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg()));

        // 查询用户绑定的第三方账号列表
        return userThirdPartyBindingRespoitory.findByUserId(account.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean unbindThirdParty(Long userId, Long bindingId) {
        // 参数校验
        if (userId == null) {
            throw new IllegalArgumentException("使用者 ID 不可為空");
        }
        if (bindingId == null) {
            throw new IllegalArgumentException("綁定 ID 不可為空");
        }

        // 查询用户自然人信息
        UserPerson userPerson = userPersonRepository.findById(userId)
                .orElseThrow(() -> new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg()));

        // 查询用户账号信息
        CustomerAccount account = customerAccountRepository.findByUserPersonId(userPerson.getId())
                .orElseThrow(() -> new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg()));

        // 查询绑定信息
        UserThirdPartyBinding binding = userThirdPartyBindingRespoitory.findById(bindingId);
        if (binding == null) {
            throw new BusinessException(UsersErrorCode.BINDING_NOT_EXIST.getCode(), UsersErrorCode.BINDING_NOT_EXIST.getMsg());
        }

        // 查询第三方账号信息
        UserThirdPartyAuth userThirdPartyAuth = userThirdPartyAuthRespoitory.queryByBindingId(bindingId);
        if (userThirdPartyAuth == null) {
            throw new BusinessException(UsersErrorCode.BINDING_NOT_EXIST.getCode(), UsersErrorCode.BINDING_NOT_EXIST.getMsg());
        }

        // 检查绑定信息是否属于该用户
        if (!binding.getCustomerAccountId().equals(account.getId())) {
            throw new BusinessException(UsersErrorCode.BINDING_NOT_BELONG.getCode(), UsersErrorCode.BINDING_NOT_BELONG.getMsg());
        }
        if (!userThirdPartyAuth.getCustomerAccountId().equals(account.getId())) {
            throw new BusinessException(UsersErrorCode.BINDING_NOT_BELONG.getCode(), UsersErrorCode.BINDING_NOT_BELONG.getMsg());
        }

        // 解绑第三方账号
        userThirdPartyBindingRespoitory.logicDeleteById(bindingId);
        userThirdPartyAuthRespoitory.logicDeleteByIds(Arrays.asList(userThirdPartyAuth.getId()));

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean bindThirdParty(Long userId, String platform, String externalUserId,
                                  String nickName, String avatar, String gender,
                                  String accessToken, String refreshToken, Integer expiresIn,
                                  String tokenType,String scope,Integer refreshExpiresAt) {
        // 参数校验
        if (userId == null) {
            throw new IllegalArgumentException("使用者 ID 不可為空");
        }
        if (platform == null || platform.isEmpty()) {
            throw new IllegalArgumentException("平台不可為空");
        }
        if (externalUserId == null || externalUserId.isEmpty()) {
            throw new IllegalArgumentException("外部使用者 ID 不可為空");
        }

        // 查询用户账号信息

        // 查询用户基本信息
        UserPerson userPerson = userPersonRepository.findById(userId)
                .orElseThrow(() -> new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg()));


        // 查询用户账号信息
        CustomerAccount account = customerAccountRepository.findByUserPersonId(userPerson.getId())
                .orElseThrow(() -> new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg()));


        // 检查第三方账号是否已被绑定
        UserThirdPartyBinding existingBinding = userThirdPartyBindingRespoitory.findByPlatformAndExternalUserId(platform, externalUserId);
        if (existingBinding != null) {
            // 如果已被绑定，检查是否绑定到当前用户
            if (!existingBinding.getCustomerAccountId().equals(account.getId())) {
                throw new BusinessException(UsersErrorCode.THIRD_PARTY_ALREADY_BOUND.getCode(), UsersErrorCode.THIRD_PARTY_ALREADY_BOUND.getMsg());
            }
            // 如果已绑定到当前用户，直接返回成功
            return true;
        }

        // 创建新的绑定信息
        UserThirdPartyBinding binding = new UserThirdPartyBinding();
        binding.setCustomerAccountId(account.getId());
        binding.setPlatform(platform);
        binding.setExternalUserId(externalUserId);
        binding.setNickName(nickName);
        binding.setAvatar(avatar);
        binding.setGender(gender);
        binding.setBindingStatus("BINDING");
        binding.setBindTime(new Date());
        binding.setCreatedBy("system");
        binding.setUpdatedBy("system");
        binding.setCreatedAt(new Date());
        binding.setUpdatedAt(new Date());
        binding.setActive(1);

        // 保存绑定信息
        UserThirdPartyBinding bindingSave = userThirdPartyBindingRespoitory.save(binding);

        UserThirdPartyAuth userThirdPartyAuth = new UserThirdPartyAuth();
        userThirdPartyAuth.setPlatform(platform);
        userThirdPartyAuth.setCustomerAccountId(account.getId());
        userThirdPartyAuth.setBindingId(bindingSave.getId());
        userThirdPartyAuth.setAccessToken(accessToken);
        userThirdPartyAuth.setRefreshToken(refreshToken);
        userThirdPartyAuth.setTokenType(tokenType);
        userThirdPartyAuth.setScope(scope);
        userThirdPartyAuth.setExpiresAt(expiresIn);
        userThirdPartyAuth.setRefreshExpiresAt(refreshExpiresAt);
        //userThirdPartyAuth.setStatus(AccountStatus.ACTIVE.getDescription());
        userThirdPartyAuth.setLastRefreshTime(new Date());
        userThirdPartyAuth.setRefreshCount(1);
        userThirdPartyAuth.setCreatedBy("system");
        userThirdPartyAuth.setCreatedAt(new Date());
        userThirdPartyAuth.setUpdatedAt(new Date());
        userThirdPartyAuth.setUpdatedBy("system");
        userThirdPartyAuthRespoitory.save(userThirdPartyAuth);
        return true;
    }

    @Override
    public boolean isThirdPartyBound(String platform, String externalUserId) {
        // 参数校验
        if (platform == null || platform.isEmpty()) {
            throw new IllegalArgumentException("平台不可為空");
        }
        if (externalUserId == null || externalUserId.isEmpty()) {
            throw new IllegalArgumentException("外部使用者 ID 不可為空");
        }

        // 查询绑定信息
        UserThirdPartyBinding binding = userThirdPartyBindingRespoitory.findByPlatformAndExternalUserId(platform, externalUserId);

        return binding != null;
    }

    @Override
    public Long getBoundUserId(String platform, String externalUserId) {
        // 参数校验
        if (platform == null || platform.isEmpty()) {
            throw new IllegalArgumentException("平台不可為空");
        }
        if (externalUserId == null || externalUserId.isEmpty()) {
            throw new IllegalArgumentException("外部使用者 ID 不可為空");
        }

        // 查询绑定信息
        UserThirdPartyBinding binding = userThirdPartyBindingRespoitory.findByPlatformAndExternalUserId(platform, externalUserId);

        return binding != null ? binding.getCustomerAccountId() : null;
    }

    @Override
    public int updateThirdParty(Long customerAccountId, UserThirdPartyBinding userThirdPartyBinding, UserThirdPartyAuth userThirdPartyAuth) {
        // 先根据customerAccountId 查询是否绑定了第三方账号
        UserThirdPartyBinding binding = userThirdPartyBindingRespoitory.findByCustomerAccountIdAndExternalUserId(customerAccountId,userThirdPartyBinding.getExternalUserId());
        if(binding == null) {
            //绑定操作
            userThirdPartyBinding.setCustomerAccountId(customerAccountId);
            UserThirdPartyBinding bindingSave = userThirdPartyBindingRespoitory.save(userThirdPartyBinding);

            userThirdPartyAuth.setCustomerAccountId(customerAccountId);
            userThirdPartyAuth.setBindingId(bindingSave.getId());
            userThirdPartyAuthRespoitory.save(userThirdPartyAuth);
        }

        return 0;
    }
}
