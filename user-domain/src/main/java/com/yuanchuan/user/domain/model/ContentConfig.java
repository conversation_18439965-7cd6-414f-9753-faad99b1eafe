package com.yuanchuan.user.domain.model;

import com.yuanchuan.user.context.enums.ContentTitleEnum;
import com.yuanchuan.user.context.enums.ContentType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 内容配置领域模型
 * 用于存储协议、隐私政策、帮助等内容
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ContentConfig {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 内容类型
     */
    private ContentType type;

    /**
     * 标题枚举
     * 注意：帮助类型的内容标题不使用枚举，而是直接使用title字段
     */
    private ContentTitleEnum titleEnum;

    /**
     * 标题
     * 对于帮助类型的内容，直接使用此字段存储自定义的标题
     */
    private String title;

    /**
     * 内容
     */
    private String content;

    /**
     * 父级内容ID，用于构建层级关系
     * 如果为null，表示是一级内容
     */
    private Long parentId;

    /**
     * 跳转URL，用于帮助内容跳转到特定页面
     * 如果为null，表示不需要跳转
     */
    private String redirectUrl;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 是否启用
     */
    private Boolean isEnabled;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 是否删除
     */
    private Boolean active;

    /**
     * 子内容列表，用于构建层级结构
     * 不持久化到数据库
     */
    private List<ContentConfig> children;

    /**
     * 启用内容
     */
    public void enable() {
        this.isEnabled = true;
    }

    /**
     * 禁用内容
     */
    public void disable() {
        this.isEnabled = false;
    }

    /**
     * 更新内容
     *
     * @param titleEnum 标题枚举
     * @param content 内容
     * @param updatedBy 更新人
     */
    public void updateContent(ContentTitleEnum titleEnum, String content, String updatedBy) {
        this.titleEnum = titleEnum;
        this.title = titleEnum != null ? titleEnum.getTitle() : this.title;
        this.content = content;
        this.updatedBy = updatedBy;
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 更新内容
     *
     * @param title 标题
     * @param content 内容
     * @param updatedBy 更新人
     */
    public void updateContent(String title, String content, String updatedBy) {
        this.title = title;
        this.titleEnum = ContentTitleEnum.fromTitle(title);
        this.content = content;
        this.updatedBy = updatedBy;
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 更新帮助内容
     *
     * @param titleEnum 标题枚举
     * @param content 内容
     * @param parentId 父级内容ID
     * @param redirectUrl 跳转URL
     * @param updatedBy 更新人
     */
    public void updateHelpContent(ContentTitleEnum titleEnum, String content, Long parentId, String redirectUrl, String updatedBy) {
        this.titleEnum = titleEnum;
        this.title = titleEnum != null ? titleEnum.getTitle() : this.title;
        this.content = content;
        this.parentId = parentId;
        this.redirectUrl = redirectUrl;
        this.updatedBy = updatedBy;
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 更新排序
     *
     * @param sortOrder 排序
     * @param updatedBy 更新人
     */
    public void updateSortOrder(Integer sortOrder, String updatedBy) {
        this.sortOrder = sortOrder;
        this.updatedBy = updatedBy;
        this.updatedAt = LocalDateTime.now();
    }
}
