package com.yuanchuan.user.domain.repository;

import com.yuanchuan.user.domain.model.Role;
import com.yuanchuan.user.domain.model.RolePermission;

import java.util.List;
import java.util.Optional;

/**
 * 角色权限关联仓储接口
 */
public interface RolePermissionRepository {

    /**
     * 保存角色权限关联信息
     *
     * @param rolePermission 角色权限关联信息
     * @return 保存后的角色权限关联信息
     */
    RolePermission save(RolePermission rolePermission);

    /**
     * 根据ID查询角色权限关联
     *
     * @param id 关联ID
     * @return 角色权限关联信息
     */
    Optional<RolePermission> findById(Long id);

    /**
     * 根据角色ID查询权限关联
     *
     * @param roleId 角色ID
     * @return 角色权限关联列表
     */
    List<RolePermission> findByRoleId(Long roleId);

    /**
     * 根据权限ID查询角色关联
     *
     * @param permissionId 权限ID
     * @return 角色权限关联列表
     */
    List<RolePermission> findByPermissionId(Long permissionId);

    /**
     * 根据角色ID和权限ID查询关联
     *
     * @param roleId 角色ID
     * @param permissionId 权限ID
     * @return 角色权限关联信息
     */
    Optional<RolePermission> findByRoleIdAndPermissionId(Long roleId, Long permissionId);

    /**
     * 批量保存角色权限关联
     *
     * @param rolePermissions 角色权限关联列表
     * @return 是否成功
     */
    Boolean batchSave(List<RolePermission> rolePermissions);

    /**
     * 根据角色ID删除所有权限关联
     *
     * @param roleId 角色ID
     * @return 删除数量
     */
    Integer deleteByRoleId(Long roleId);

    /**
     * 根据权限ID删除所有角色关联
     *
     * @param permissionId 权限ID
     * @return 删除数量
     */
    Integer deleteByPermissionId(Long permissionId);

    /**
     * 根据角色ID和权限ID删除关联
     *
     * @param roleId 角色ID
     * @param permissionId 权限ID
     * @return 是否成功
     */
    Boolean deleteByRoleIdAndPermissionId(Long roleId, Long permissionId);
}
