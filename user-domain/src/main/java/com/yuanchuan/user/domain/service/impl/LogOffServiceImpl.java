package com.yuanchuan.user.domain.service.impl;

import com.yuanchuan.authentication.api.service.AuthenticationService;
import com.yuanchuan.common.enums.users.UsersErrorCode;
import com.yuanchuan.common.exception.BusinessException;
import com.yuanchuan.user.domain.model.*;
import com.yuanchuan.user.domain.repository.*;
import com.yuanchuan.user.domain.service.LogOffService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-05-14 11:31:11:31
 */
@Service
public class LogOffServiceImpl implements LogOffService {

    @Autowired
    private UserPersonRepository userPersonRepository;

    @Autowired
    private CustomerAccountRepository customerAccountRepository;;

    @Autowired
    private AccountDeviceRepository accountDeviceRepository;

    @Autowired
    private UserThirdPartyBindingRespoitory userThirdPartyBindingRespoitory;

    @Autowired
    private UserThirdPartyAuthRespoitory userThirdPartyAuthRespoitory;



    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean logOff(Long userId, String deviceId) {

        // 校验用户是否存在
        UserPerson userPerson = userPersonRepository.findById(userId)
                .orElseThrow(() ->  new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(),UsersErrorCode.USER_NOT_EXIST.getMsg()));

        // 校验账户信息
        CustomerAccount account = customerAccountRepository.findByUserPersonId(userPerson.getId())
                .orElseThrow(() ->  new BusinessException(UsersErrorCode.USER_ACCOUNT_NOT_EXIST.getCode(),UsersErrorCode.USER_ACCOUNT_NOT_EXIST.getMsg()));



        // 2. 校验用户设备信息
        List<AccountDevice> accountDeviceList = accountDeviceRepository.findByAccountId(account.getId());
        if(accountDeviceList.isEmpty()){
            throw new BusinessException(UsersErrorCode.USER_DEVICE_NOT_EXIST.getCode(),UsersErrorCode.USER_DEVICE_NOT_EXIST.getMsg());
        }
        List<Long> deviceIds  = accountDeviceList.stream().map(AccountDevice::getId).toList();



        // 3. 接触三方授权信息
        List<UserThirdPartyBinding> userThirdPartyBindingList = userThirdPartyBindingRespoitory.findByUserId(userId);
        List<UserThirdPartyAuth> userThirdPartyAuthList =  userThirdPartyAuthRespoitory.findByUserId(userId);


        // 解绑删除
        boolean userPersonFlg = userPersonRepository.logicDeleteById(userPerson.getId());
        boolean accountFlag =customerAccountRepository.logicDeleteById(account.getId());
        boolean deviceFlag = accountDeviceRepository.logicDeleteByIds(deviceIds);

        boolean bindingFlag = false;
        boolean authFlag = false;
        if(!CollectionUtils.isEmpty(userThirdPartyBindingList)){
            List<Long> bingdingIds  = userThirdPartyBindingList.stream().map(UserThirdPartyBinding::getId).toList();
            bindingFlag = userThirdPartyBindingRespoitory.logicDeleteByIds(bingdingIds);
        }

        if(!CollectionUtils.isEmpty(userThirdPartyAuthList)){
            List<Long> partyAuthIds  = userThirdPartyAuthList.stream().map(UserThirdPartyAuth::getId).toList();
            authFlag  = userThirdPartyAuthRespoitory.logicDeleteByIds(partyAuthIds);
        }

         if(userPersonFlg && accountFlag && deviceFlag && bindingFlag && authFlag){
             throw new BusinessException(UsersErrorCode.USER_CANCEL_ERROR.getCode(),UsersErrorCode.USER_CANCEL_ERROR.getMsg());
         }

         return userPersonFlg && accountFlag && deviceFlag && bindingFlag && authFlag;
    }
}
