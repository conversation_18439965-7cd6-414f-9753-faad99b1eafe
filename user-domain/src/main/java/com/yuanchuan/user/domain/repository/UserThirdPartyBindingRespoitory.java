package com.yuanchuan.user.domain.repository;

import com.yuanchuan.user.domain.model.UserThirdPartyBinding;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-05-13 16:17:16:17
 */
public interface UserThirdPartyBindingRespoitory {

    /**
     * 根据用户id 查询绑定三方账号
     * @param userId
     * @return
     */
    List<UserThirdPartyBinding> findByUserId(Long userId);

    /**
     * 主键id批量删除(逻辑）
     * @param bingdingIds
     * @return
     */
    boolean logicDeleteByIds(List<Long> bingdingIds);

    /**
     * 根据ID查询绑定信息
     *
     * @param id 绑定ID
     * @return 绑定信息
     */
    UserThirdPartyBinding findById(Long id);

    /**
     * 根据ID删除绑定信息（逻辑删除）
     *
     * @param id 绑定ID
     * @return 是否删除成功
     */
    boolean logicDeleteById(Long id);

    /**
     * 保存绑定信息
     *
     * @param binding 绑定信息
     * @return 保存后的绑定信息
     */
    UserThirdPartyBinding save(UserThirdPartyBinding binding);

    /**
     * 根据平台和外部用户ID查询绑定信息
     *
     * @param platform 平台
     * @param externalUserId 外部用户ID
     * @return 绑定信息
     */
    UserThirdPartyBinding findByPlatformAndExternalUserId(String platform, String externalUserId);

    /**
     * 根据用户ID和外部用户ID查询绑定信息
     *
     * @param customerAccountId 用户ID
     * @param externalUserId 外部用户ID
     * @return 绑定信息
     */
    UserThirdPartyBinding findByCustomerAccountIdAndExternalUserId(Long customerAccountId, String externalUserId);
}
