package com.yuanchuan.user.domain.service;

/**
 * 密码策略服务接口
 */
public interface PasswordPolicyService {
    
    /**
     * 验证密码是否符合策略
     * 密码长度8-32位，需包含数字、字母、符号至少2种或以上元素
     *
     * @param password 密码
     * @return 是否符合策略
     */
    boolean validatePassword(String password);
    
    /**
     * 获取密码不符合策略的原因
     *
     * @param password 密码
     * @return 不符合策略的原因，如果符合策略则返回null
     */
    String getInvalidReason(String password);
    
    /**
     * 加密密码
     *
     * @param password 明文密码
     * @return 加密后的密码
     */
    String encryptPassword(String password);
    
    /**
     * 验证密码是否匹配
     *
     * @param rawPassword 明文密码
     * @param encodedPassword 加密后的密码
     * @return 是否匹配
     */
    boolean matches(String rawPassword, String encodedPassword);
}
