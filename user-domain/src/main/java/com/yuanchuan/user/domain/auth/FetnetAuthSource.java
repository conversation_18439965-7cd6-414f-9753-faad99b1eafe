package com.yuanchuan.user.domain.auth;

import me.zhyd.oauth.config.AuthSource;
import me.zhyd.oauth.request.AuthDefaultRequest;

/**
 * <AUTHOR>
 * @date 2025-05-12 14:11:14:11
 */
public enum FetnetAuthSource implements AuthSource {
    /**
     * 测试
     */
    FETNET {
        /**
         * 授权的api
         *
         * @return url
         */
        @Override
        public String authorize() {
            return "https://login2-test.fetnet.net/mga/sps/oauth/oauth20/authorize";
        }

        /**
         * 获取accessToken的api
         *
         * @return url
         */
        @Override
        public String accessToken() {
            return "https://login2-test.fetnet.net/mga/sps/oauth/oauth20/token";
        }

        /**
         * 获取用户信息的api
         *
         * @return url
         */
        @Override
        public String userInfo() {
            return null;
        }

        /**
         * 取消授权的api
         *
         * @return url
         */
        @Override
        public String revoke() {
            return null;
        }

        /**
         * 刷新授权的api
         *
         * @return url
         */
        @Override
        public String refresh() {
            return null;
        }

        @Override
        public String getName() {
            return super.getName();
        }

        @Override
        public Class<? extends AuthDefaultRequest> getTargetClass() {
            return null;
        }
    }
}

