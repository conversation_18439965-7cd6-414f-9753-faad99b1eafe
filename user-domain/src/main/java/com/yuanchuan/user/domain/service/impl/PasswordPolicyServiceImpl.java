package com.yuanchuan.user.domain.service.impl;

import com.yuanchuan.user.domain.service.PasswordPolicyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

/**
 * 密码策略服务实现类
 */
@Slf4j
@Service
public class PasswordPolicyServiceImpl implements PasswordPolicyService {
    
    /**
     * 密码最小长度
     */
    private static final int MIN_PASSWORD_LENGTH = 8;
    
    /**
     * 密码最大长度
     */
    private static final int MAX_PASSWORD_LENGTH = 32;
    
    @Autowired
    private PasswordEncoder passwordEncoder;
    
    @Override
    public boolean validatePassword(String password) {
        return getInvalidReason(password) == null;
    }
    
    @Override
    public String getInvalidReason(String password) {
        // 检查密码是否为空
        if (password == null || password.isEmpty()) {
            return "密码不能为空";
        }
        
        // 检查密码长度
        if (password.length() < MIN_PASSWORD_LENGTH || password.length() > MAX_PASSWORD_LENGTH) {
            return "密码长度必须在8-32位之间";
        }
        
        // 检查密码复杂度
        boolean hasDigit = false;
        boolean hasLetter = false;
        boolean hasSymbol = false;
        
        for (char c : password.toCharArray()) {
            if (Character.isDigit(c)) {
                hasDigit = true;
            } else if (Character.isLetter(c)) {
                hasLetter = true;
            } else {
                hasSymbol = true;
            }
        }
        
        // 计算包含的元素种类数
        int typeCount = 0;
        if (hasDigit) typeCount++;
        if (hasLetter) typeCount++;
        if (hasSymbol) typeCount++;
        
        // 检查是否至少包含2种元素
        if (typeCount < 2) {
            return "密码需包含数字、字母、符号至少2种或以上元素";
        }
        
        return null;
    }
    
    @Override
    public String encryptPassword(String password) {
        return passwordEncoder.encode(password);
    }
    
    @Override
    public boolean matches(String rawPassword, String encodedPassword) {
        return passwordEncoder.matches(rawPassword, encodedPassword);
    }
}
