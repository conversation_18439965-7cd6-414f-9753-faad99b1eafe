package com.yuanchuan.user.domain.repository;

import com.yuanchuan.user.domain.model.AccountDevice;

import java.util.List;
import java.util.Optional;

/**
 * 账户设备仓储接口
 */
public interface AccountDeviceRepository {
    /**
     * 保存设备信息
     *
     * @param device 设备信息
     * @return 保存后的设备信息
     */
    AccountDevice save(AccountDevice device);

    /**
     * 根据ID查询设备
     *
     * @param id 设备ID
     * @return 设备信息
     */
    Optional<AccountDevice> findById(Long id);

    /**
     * 根据账户ID查询设备列表
     *
     * @param accountId 账户ID
     * @return 设备列表
     */
    List<AccountDevice> findByAccountId(Long accountId);

    /**
     * 根据账户ID和设备ID查询设备
     *
     * @param accountId 账户ID
     * @param deviceId 设备ID
     * @return 设备信息
     */
    Optional<AccountDevice> findByAccountIdAndDeviceId(Long accountId, String deviceId);

    /**
     * 更新设备活跃状态
     *
     * @param id 设备ID
     * @param isActive 是否活跃
     * @return 是否更新成功
     */
    boolean updateActiveStatus(Long id, Boolean isActive);

    /**
     * 更新设备验证状态
     *
     * @param id 设备ID
     * @param isVerified 是否已验证
     * @return 是否更新成功
     */
    boolean updateVerifiedStatus(Long id, Boolean isVerified);

    /**
     * 查询用户的活跃设备数量
     *
     * @param accountId 账户ID
     * @return 活跃设备数量
     */
    int countActiveDevicesByAccountId(Long accountId);

    /**
     * 删除设备
     *
     * @param id 设备ID
     * @return 是否删除成功
     */
    boolean deleteById(Long id);

    /**
     * id主键批量删除
     * @param deviceIds
     */
    boolean logicDeleteByIds(List<Long> deviceIds);
}
