package com.yuanchuan.user.domain.service;

import com.yuanchuan.common.enums.users.login.LoginType;
import com.yuanchuan.common.enums.users.login.PlatformType;
import com.yuanchuan.user.domain.model.LoginLogDomian;

/**
 * 登录日志领域服务接口
 */
public interface LoginLogDomainService {
    
    /**
     * 记录登录成功日志
     *
     * @param accountId 账户ID
     * @param deviceId 设备ID
     * @param loginType 登录类型
     * @param platform 平台类型
     * @param ipAddress IP地址
     * @param userAgent User-Agent
     * @param location 登录位置（可选）
     */
    void recordLoginSuccess(Long accountId, String deviceId, LoginType loginType, 
                           PlatformType platform, String ipAddress, String userAgent, String location);
    
    /**
     * 记录登录失败日志
     *
     * @param accountId 账户ID（可能为null，如果账户不存在）
     * @param deviceId 设备ID
     * @param loginType 登录类型
     * @param platform 平台类型
     * @param ipAddress IP地址
     * @param userAgent User-Agent
     * @param location 登录位置（可选）
     */
    void recordLoginFailure(Long accountId, String deviceId, LoginType loginType, 
                           PlatformType platform, String ipAddress, String userAgent, String location);
    
    /**
     * 记录登录日志（通用方法）
     *
     * @param loginLog 登录日志对象
     */
    void recordLoginLog(LoginLogDomian loginLog);
    
    /**
     * 查询账户最近登录记录
     *
     * @param accountId 账户ID
     * @return 最近登录记录
     */
    LoginLogDomian getLastLoginRecord(Long accountId);
    
    /**
     * 统计指定时间段内的登录失败次数
     *
     * @param accountId 账户ID
     * @param hours 时间段（小时）
     * @return 失败次数
     */
    int countLoginFailures(Long accountId, int hours);
}
