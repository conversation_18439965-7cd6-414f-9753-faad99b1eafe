package com.yuanchuan.user.domain.service.impl;

import com.yuanchuan.common.enums.users.login.LoginType;
import com.yuanchuan.common.enums.users.login.PlatformType;
import com.yuanchuan.user.domain.model.LoginLogDomian;
import com.yuanchuan.user.domain.repository.LoginLogRepository;
import com.yuanchuan.user.domain.service.LoginLogDomainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * 登录日志领域服务实现类
 */
@Slf4j
@Service
public class LoginLogDomainServiceImpl implements LoginLogDomainService {

    @Autowired
    private LoginLogRepository loginLogRepository;

    @Override
    public void recordLoginSuccess(Long accountId, String deviceId, LoginType loginType,
                                  PlatformType platform, String ipAddress, String userAgent, String location) {
        try {

            if (accountId == null) {
                log.error("accountId为null，无法记录登录成功日志: loginType={}, platform={}, ip={}", loginType, platform, ipAddress);
                return;
            }

            LoginLogDomian loginLog = createLoginLog(accountId, deviceId, loginType, platform,
                                             ipAddress, userAgent, location, LoginLogDomian.LoginStatus.SUCCESS);

            log.debug("创建的LoginLog对象: accountId={}, loginType={}, loginStatus={}",
                    loginLog.getAccountId(), loginLog.getLoginType(), loginLog.getLoginStatus());

            loginLogRepository.save(loginLog);
            log.info("记录登录成功日志完成: accountId={}, loginType={}, platform={}, ip={}",
                    accountId, loginType, platform, ipAddress);
        } catch (Exception e) {
            log.error("记录登录成功日志失败: accountId={}, error={}", accountId, e.getMessage(), e);
            throw e; // 重新抛出异常以便进一步调试
        }
    }

    @Override
    public void recordLoginFailure(Long accountId, String deviceId, LoginType loginType,
                                  PlatformType platform, String ipAddress, String userAgent, String location) {
        try {
            LoginLogDomian loginLog = createLoginLog(accountId, deviceId, loginType, platform,
                                             ipAddress, userAgent, location, LoginLogDomian.LoginStatus.FAIL);
            loginLogRepository.save(loginLog);
            log.info("记录登录失败日志: accountId={}, loginType={}, platform={}, ip={}",
                    accountId, loginType, platform, ipAddress);
        } catch (Exception e) {
            log.error("记录登录失败日志失败: accountId={}, error={}", accountId, e.getMessage(), e);
        }
    }

    @Override
    public void recordLoginLog(LoginLogDomian loginLog) {
        try {
            if (loginLog.getLoginAt() == null) {
                loginLog.setLoginAt(LocalDateTime.now());
            }
            if (loginLog.getCreatedAt() == null) {
                loginLog.setCreatedAt(LocalDateTime.now());
            }
            if (loginLog.getUpdatedAt() == null) {
                loginLog.setUpdatedAt(LocalDateTime.now());
            }

            loginLogRepository.save(loginLog);
            log.info("记录登录日志: accountId={}, status={}, type={}",
                    loginLog.getAccountId(), loginLog.getLoginStatus(), loginLog.getLoginType());
        } catch (Exception e) {
            log.error("记录登录日志失败: accountId={}, error={}", loginLog.getAccountId(), e.getMessage(), e);
        }
    }

    @Override
    public LoginLogDomian getLastLoginRecord(Long accountId) {
        try {
            Optional<LoginLogDomian> lastLogin = loginLogRepository.findLastLoginByAccountId(accountId);
            return lastLogin.orElse(null);
        } catch (Exception e) {
            log.error("查询最近登录记录失败: accountId={}, error={}", accountId, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public int countLoginFailures(Long accountId, int hours) {
        try {
            LocalDateTime startTime = LocalDateTime.now().minusHours(hours);
            LocalDateTime endTime = LocalDateTime.now();
            return loginLogRepository.countFailLoginsByAccountId(accountId, startTime, endTime);
        } catch (Exception e) {
            log.error("统计登录失败次数失败: accountId={}, hours={}, error={}", accountId, hours, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 创建登录日志对象
     */
    private LoginLogDomian createLoginLog(Long accountId, String deviceId, LoginType loginType,
                                          PlatformType platform, String ipAddress, String userAgent,
                                          String location, LoginLogDomian.LoginStatus status) {
        LoginLogDomian loginLog = new LoginLogDomian();
        loginLog.setAccountId(accountId);
        loginLog.setDeviceId(deviceId);
        loginLog.setLoginType(loginType.getDescription());
        loginLog.setLoginStatus(status.name());
        loginLog.setIpAddress(ipAddress);
        loginLog.setUserAgent(userAgent);
        loginLog.setLocation(location);
        loginLog.setLoginAt(LocalDateTime.now());
        loginLog.setSource(platform.getCode());
        loginLog.setCreatedAt(LocalDateTime.now());
        loginLog.setUpdatedAt(LocalDateTime.now());

        return loginLog;
    }




}
