package com.yuanchuan.user.domain.repository;

import com.yuanchuan.user.domain.model.LoginLogDomian;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 登录日志仓储接口
 */
public interface LoginLogRepository {
    /**
     * 保存登录日志
     *
     * @param loginLog 登录日志
     * @return 保存后的登录日志
     */
    LoginLogDomian save(LoginLogDomian loginLog);

    /**
     * 根据ID查询登录日志
     *
     * @param id 日志ID
     * @return 登录日志
     */
    Optional<LoginLogDomian> findById(Long id);

    /**
     * 根据账户ID查询登录日志
     *
     * @param accountId 账户ID
     * @param limit 限制条数
     * @return 登录日志列表
     */
    List<LoginLogDomian> findByAccountId(Long accountId, Integer limit);

    /**
     * 查询账户最近一次登录记录
     *
     * @param accountId 账户ID
     * @return 登录日志
     */
    Optional<LoginLogDomian> findLastLoginByAccountId(Long accountId);

    /**
     * 查询指定时间段内的登录失败记录
     *
     * @param accountId 账户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 登录失败记录列表
     */
    List<LoginLogDomian> findFailLoginsByAccountId(Long accountId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计指定时间段内的登录失败次数
     *
     * @param accountId 账户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 登录失败次数
     */
    int countFailLoginsByAccountId(Long accountId, LocalDateTime startTime, LocalDateTime endTime);
}
