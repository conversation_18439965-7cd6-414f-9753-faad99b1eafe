package com.yuanchuan.user.domain.service;

import com.yuanchuan.user.domain.model.ContentConfig;

import java.util.List;

/**
 * 内容配置领域服务接口
 */
public interface ContentConfigDomainService {

    /**
     * 创建内容配置
     *
     * @param domain   内容配置DTO
     * @param operator 操作人
     * @return 创建后的内容配置ID
     */
    Long createContentConfig(ContentConfig domain, String operator);

    /**
     * 更新内容配置
     *
     * @param id       内容配置ID
     * @param domain   内容配置领域对象
     * @param operator 操作人
     * @return 更新后的内容配置
     */
    ContentConfig updateContentConfig(Long id, ContentConfig domain, String operator);

    /**
     * 获取内容配置详情
     * 如果是帮助类型的内容，会自动构建子内容层级结构
     *
     * @param id 内容配置ID
     * @return 内容配置领域对象，包含子内容层级结构（如果有的话）
     */
    ContentConfig getContentConfigDetail(Long id);

    /**
     * 根据条件动态查询内容配置列表
     *
     * @param contentConfig 查询条件，包含类型、启用状态等
     * @return 内容配置领域对象列表
     */
    List<ContentConfig> getContentConfigs(ContentConfig contentConfig);

    /**
     * 根据类型获取启用的内容配置列表
     *
     * @param type 内容类型
     * @return 内容配置领域对象列表
     */
    List<ContentConfig> getEnabledContentConfigsByType(String type);

    /**
     * 获取所有启用的内容配置
     *
     * @return 内容配置领域对象列表
     */
    List<ContentConfig> getAllEnabledContentConfigs();

    /**
     * 获取所有内容配置（包括启用和禁用）
     *
     * @return 内容配置领域对象列表
     */
    List<ContentConfig> getAllContentConfigs();



    /**
     * 删除内容配置
     *
     * @param id 内容配置ID
     * @return 是否成功
     */
    boolean deleteContentConfig(Long id);
}
