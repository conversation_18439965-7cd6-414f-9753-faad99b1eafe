package com.yuanchuan.user.domain.service;

import java.util.Map;

/**
 * 用户变更日志服务接口
 * 提供记录用户信息变更的功能
 *
 * <AUTHOR>
 * @date 2025-05-22
 */
public interface UserChangeLogsService {

    /**
     * 创建用户变更日志记录
     *
     * @param userId 用户ID
     * @param oldValue 旧值
     * @param newValue 新值
     * @param deviceId 设备ID
     * @param source 来源
     * @return 是否创建成功
     */
    boolean createUserChangeLogs(Long userId, Object oldValue, Object newValue, String deviceId, String source);

    /**
     * 创建用户变更日志记录（简化版，无设备ID）
     *
     * @param userId 用户ID
     * @param oldValue 旧值
     * @param newValue 新值
     * @param source 来源
     * @return 是否创建成功
     */
    boolean createUserChangeLogs(Long userId, Object oldValue, Object newValue, String source);

    /**
     * 创建字段变更日志记录
     * 记录特定字段的变更，格式为 {"field_name": "value"}
     *
     * @param userId 用户ID
     * @param fieldName 字段名称（数据库字段名格式，如 nick_name）
     * @param oldValue 字段旧值
     * @param newValue 字段新值
     * @param deviceId 设备ID
     * @param source 来源
     * @return 是否创建成功
     */
    boolean createFieldChangeLogs(Long userId, String fieldName, Object oldValue, Object newValue, String deviceId, String source);

    /**
     * 创建字段变更日志记录（简化版，无设备ID）
     *
     * @param userId 用户ID
     * @param fieldName 字段名称（数据库字段名格式，如 nick_name）
     * @param oldValue 字段旧值
     * @param newValue 字段新值
     * @param source 来源
     * @return 是否创建成功
     */
    boolean createFieldChangeLogs(Long userId, String fieldName, Object oldValue, Object newValue, String source);

    /**
     * 创建多字段变更日志记录
     *
     * @param userId 用户ID
     * @param changedFields 变更的字段映射，key为字段名，value为包含oldValue和newValue的Map
     * @param deviceId 设备ID
     * @param source 来源
     * @return 是否创建成功
     */
    boolean createMultiFieldChangeLogs(Long userId, Map<String, Map<String, Object>> changedFields, String deviceId, String source);

    /**
     * 创建多字段变更日志记录（简化版，无设备ID）
     *
     * @param userId 用户ID
     * @param changedFields 变更的字段映射，key为字段名，value为包含oldValue和newValue的Map
     * @param source 来源
     * @return 是否创建成功
     */
    boolean createMultiFieldChangeLogs(Long userId, Map<String, Map<String, Object>> changedFields, String source);
}
