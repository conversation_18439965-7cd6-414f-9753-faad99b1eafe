package com.yuanchuan.user.domain.repository;

import com.yuanchuan.user.domain.model.UserPerson;

import java.util.Optional;

/**
 * 自然人仓储接口
 */
public interface UserPersonRepository {
    /**
     * 保存用户信息
     *
     * @param userPerson 用户信息
     * @return 保存后的用户信息
     */
    UserPerson save(UserPerson userPerson);

    /**
     * 根据ID查询用户
     *
     * @param id 用户ID
     * @return 用户信息
     */
    Optional<UserPerson> findById(Long id);

    /**
     * 根据手机号查询用户
     *
     * @param phone 手机号
     * @return 用户信息
     */
    Optional<UserPerson> findByPhoneAndSource(String phone,String source);

    /**
     * 根据邮箱查询用户
     *
     * @param email 邮箱
     * @return 用户信息
     */
    Optional<UserPerson> findByEmailAndSource(String email,String source);

    /**
     * 更新手机号
     *
     * @param id 用户ID
     * @param phone 手机号
     * @return 是否更新成功
     */
    boolean updatePhone(Long id, String phone);

    /**
     * 更新邮箱
     *
     * @param id 用户ID
     * @param email 邮箱
     * @return 是否更新成功
     */
    boolean updateEmail(Long id, String email);

    /**
     * 删除用户
     *
     * @param id 用户ID
     * @return 是否删除成功
     */
    boolean deleteById(Long id);

    /**
     * 根据用户id删除（逻辑）
     * @param id
     */
    boolean logicDeleteById(Long id);
}
