package com.yuanchuan.user.domain.service;

import com.yuanchuan.common.domain.query.PageQueryV;
import com.yuanchuan.user.domain.model.Permission;

import java.util.List;

/**
 * 权限领域服务接口
 */
public interface PermissionDomainService {

    /**
     * 创建权限
     *
     * @param permission 权限信息
     * @return 创建后的权限信息
     */
    Permission createPermission(Permission permission,Long createdId,String createdBy);

    /**
     * 更新权限
     *
     * @param permission 权限信息
     * @return 更新后的权限信息
     */
    Permission updatePermission(Permission permission,Long createdId,String createdBy);

    /**
     * 根据ID查询权限
     *
     * @param id 权限ID
     * @return 权限信息
     */
    Permission getPermissionById(Long id);

    /**
     * 删除权限
     *
     * @param id 权限ID
     * @return 是否成功
     */
    Boolean deletePermission(Long id);

    /**
     * 根据账户ID查询用户权限树
     *
     * @param accountId 账户ID
     * @return 权限树列表
     */
    List<Permission> getUserPermissionTree(Long accountId);

    /**
     * 分页查询权限
     *
     * @param permissionMenu 父权限ID
     * @param status 状态
     * @param keyword 关键词
     * @param pageQuery 分页查询参数
     * @return 权限列表
     */
    List<Permission> queryPermissions(String permissionMenu, Integer status, String keyword, PageQueryV pageQuery);

    /**
     * 统计权限数量
     *
     * @param permissionMenu 父权限ID
     * @param status 状态
     * @param keyword 关键词
     * @return 权限数量
     */
    Long countPermissions(String permissionMenu, Integer status, String keyword);

    /**
     * 获取权限入口列表（类型为MENU的权限）
     *
     * @return 权限入口列表
     */
    List<Permission> getPermissionEntries();

    /**
     * 校验权限名称唯一性
     *
     * @param permissionName 权限名称
     * @param excludeId 排除的权限ID
     * @return 是否唯一
     */
    Boolean checkPermissionNameUnique(String permissionName, Long excludeId);

    /**
     * 校验权限编码唯一性
     *
     * @param permissionCode 权限编码
     * @param excludeId 排除的权限ID
     * @return 是否唯一
     */
    Boolean checkPermissionCodeUnique(String permissionCode, Long excludeId);
}
