package com.yuanchuan.user.domain.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用户账号领域对象
 */
@Data
@Accessors(chain = true)
public class CustomerAccount {
    /**
     * 账号ID
     */
    private Long id;

    /**
     * 关联自然人ID
     */
    private Long userPersonId;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 头像URL
     */
    private String avatar;

    /**
     * 性别
     */
    private String gender;

    /**
     * 生日
     */
    private LocalDate birthday;

    /**
     * 密码哈希
     */
    private String password;

    /**
     * 密码更新时间
     */
    private LocalDateTime passwordUpdatedAt;

    /**
     * 常居地
     */
    private String address;

    /**
     * 省编码
     */
    private String provinceCode;

    /**
     * 市编码
     */
    private String cityCode;

    /**
     * 地区编码
     */
    private String regionCode;

    /**
     * 省名称
     */
    private String provinceName;

    /**
     * 市名称
     */
    private String cityName;

    /**
     * 地区名称
     */
    private String regionName;

    /**
     * 中心点经度
     */
    private BigDecimal centerLongitude;

    /**
     * 中心点纬度
     */
    private BigDecimal centerLatitude;

    /**
     * 注册来源（平台渠道：APP、H5、小程序等）
     */
    private String registerSource;

    /**
     * 注册类型（注册方式：手机号、邮箱、第三方等）
     */
    private String registerType;

    /**
     * 注册IP
     */
    private String registerIp;

    /**
     * 注册设备
     */
    private String registerDevice;

    /**
     * 注册时间
     */
    private LocalDateTime registerTime;

    /**
     * 首次APP登录时间
     */
    private LocalDateTime firstAppLoginAt;

    /**
     * 用户状态
     */
    private String userStatus;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 是否激活：true-激活 false-未激活
     */
    private Boolean active;

    /**
     * 性别枚举
     */
    public enum Gender {
        /**
         * 未知
         */
        UNKNOWN,

        /**
         * 男
         */
        MALE,

        /**
         * 女
         */
        FEMALE
    }

    /**
     * 用户状态枚举
     */
    public enum UserStatus {
        /**
         * 正常
         */
        ACTIVE,

        /**
         * 锁定
         */
        LOCKED,

        /**
         * 禁用
         */
        DISABLED
    }

    /**
     * 注册来源枚举（平台渠道）
     */
    public enum RegisterSource {
        /**
         * APP应用
         */
        APP,

        /**
         * H5网页
         */
        H5,

        /**
         * 小程序
         */
        MINI_PROGRAM,

        /**
         * 网站
         */
        WEB,

        /**
         * 管理后台
         */
        ADMIN
    }

    /**
     * 注册类型枚举（注册方式）
     */
    public enum RegisterType {
        /**
         * 手机号注册
         */
        PHONE,

        /**
         * 邮箱注册
         */
        EMAIL,

        /**
         * Apple ID注册
         */
        APPLE_ID,

        /**
         * Google注册
         */
        GOOGLE,

        /**
         * Facebook注册
         */
        FACEBOOK,

        /**
         * LINE注册
         */
        LINE,

        /**
         * 心生活注册
         */
        HEART_LIFE
    }

    /**
     * 生成默认昵称
     *
     * @return 默认昵称
     */
    public static String generateDefaultNickname() {
        // 生成"美食客_随机4位数字"格式的昵称
        int randomNum = (int) (Math.random() * 10000);
        return "美食客_" + String.format("%04d", randomNum);
    }

    /**
     * 检查密码强度
     *
     * @param password 密码
     * @return 是否符合强度要求
     */
    public static boolean isValidPassword(String password) {
        // 密码必须包含字母和数字，长度至少为8位
        return password != null && password.length() >= 8 &&
               password.matches(".*[a-zA-Z].*") && password.matches(".*\\d.*");
    }
}
