package com.yuanchuan.user.domain.service;

import com.yuanchuan.user.domain.model.UserThirdPartyAuth;
import com.yuanchuan.user.domain.model.UserThirdPartyBinding;

import java.util.List;

/**
 * 第三方绑定服务接口
 */
public interface ThirdPartyBindingService {
    
    /**
     * 获取用户绑定的第三方账号列表
     *
     * @param userId 用户ID
     * @return 第三方绑定列表
     */
    List<UserThirdPartyBinding> getBindingList(Long userId);
    
    /**
     * 解绑第三方账号
     *
     * @param userId 用户ID
     * @param bindingId 绑定ID
     * @return 是否解绑成功
     */
    boolean unbindThirdParty(Long userId, Long bindingId);
    
    /**
     * 绑定第三方账号
     *
     * @param userId 用户ID
     * @param platform 平台
     * @param externalUserId 外部用户ID
     * @param unionId 统一ID
     * @param nickName 昵称
     * @param avatar 头像
     * @param gender 性别
     * @param accessToken 访问令牌
     * @param refreshToken 刷新令牌
     * @param expiresIn 过期时间
     * @return 是否绑定成功
     */
    boolean bindThirdParty(Long userId, String platform, String externalUserId,
                          String nickName, String avatar, String gender, 
                          String accessToken, String refreshToken, Integer expiresIn,
                           String tokenType,String scope,Integer refreshExpiresAt);
    
    /**
     * 检查第三方账号是否已被绑定
     *
     * @param platform 平台
     * @param externalUserId 外部用户ID
     * @return 是否已被绑定
     */
    boolean isThirdPartyBound(String platform, String externalUserId);
    
    /**
     * 获取第三方账号绑定的用户ID
     *
     * @param platform 平台
     * @param externalUserId 外部用户ID
     * @return 用户ID，如果未绑定则返回null
     */
    Long getBoundUserId(String platform, String externalUserId);

    /**
     * 更新第三方账号信息
     * @param id
     * @param userThirdPartyBinding
     * @param userThirdPartyAuth
     * @return
     */
    int updateThirdParty(Long id, UserThirdPartyBinding userThirdPartyBinding, UserThirdPartyAuth userThirdPartyAuth);
}
