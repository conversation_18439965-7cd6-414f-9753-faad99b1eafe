package com.yuanchuan.user.domain.repository;

import com.yuanchuan.user.domain.model.CustomerAccount;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Optional;

/**
 * 用户账号仓储接口
 */
public interface CustomerAccountRepository {
    /**
     * 保存账号信息
     *
     * @param account 账号信息
     * @return 保存后的账号信息
     */
    CustomerAccount save(CustomerAccount account);

    /**
     * 根据ID查询账号
     *
     * @param id 账号ID
     * @return 账号信息
     */
    Optional<CustomerAccount> findById(Long id);

    /**
     * 根据用户ID查询账号
     *
     * @param userPersonId 用户ID
     * @return 账号信息
     */
    Optional<CustomerAccount> findByUserPersonId(Long userPersonId);

    /**
     * 更新用户状态
     *
     * @param id 账号ID
     * @param userStatus 用户状态
     * @return 是否更新成功
     */
    boolean updateUserStatus(Long id, String userStatus);

    /**
     * 更新密码
     *
     * @param id 账号ID
     * @param password 密码哈希
     * @return 是否更新成功
     */
    boolean updatePassword(Long id, String password);

    /**
     * 更新用户昵称
     *
     * @param id 账号ID
     * @param nickName 昵称
     * @return 是否更新成功
     */
    boolean updateNickName(Long id, String nickName);

    /**
     * 更新用户头像
     *
     * @param id 账号ID
     * @param avatar 头像URL
     * @return 是否更新成功
     */
    boolean updateAvatar(Long id, String avatar);

    /**
     * 更新用户性别
     *
     * @param id 账号ID
     * @param gender 性别
     * @return 是否更新成功
     */
    boolean updateGender(Long id, String gender);

    /**
     * 更新用户生日
     *
     * @param id 账号ID
     * @param birthday 生日
     * @return 是否更新成功
     */
    boolean updateBirthday(Long id, LocalDate birthday);

    /**
     * 更新用户常居地
     *
     * @param id 账号ID
     * @param address 常居地
     * @param provinceCode 省编码
     * @param cityCode 市编码
     * @param regionCode 区编码
     * @param provinceName 省名称
     * @param cityName 市名称
     * @param regionName 区名称
     * @param longitude 经度
     * @param latitude 纬度
     * @return 是否更新成功
     */
    boolean updateAddress(Long id, String address, String provinceCode, String cityCode, String regionCode,
                          String provinceName, String cityName, String regionName,
                          BigDecimal longitude, BigDecimal latitude);

    /**
     * 删除账号
     *
     * @param id 账号ID
     * @return 是否删除成功
     */
    boolean deleteById(Long id);

    /**
     * 根据主键id删除（逻辑）
     * @param id
     */
    boolean logicDeleteById(Long id);
}
