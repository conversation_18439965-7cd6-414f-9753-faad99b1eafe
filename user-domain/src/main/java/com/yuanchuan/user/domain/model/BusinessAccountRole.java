package com.yuanchuan.user.domain.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 商户账户角色关联领域对象
 */
@Data
@Accessors(chain = true)
public class BusinessAccountRole {
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 商户账户ID
     */
    private Long businessAccountId;
    
    /**
     * 角色ID
     */
    private Long roleId;

    /**
     * 组织id
     */
    private Long orgId;

    /**
     * 生效时间
     */
    private LocalDateTime effectiveDate;

    /**
     * 失效时间
     */
    private LocalDateTime expiryDate;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 创建人
     */
    private String createdBy;
    
    /**
     * 更新人
     */
    private String updatedBy;
    
    /**
     * 是否启用 0-禁用(删除) 1-启用
     */
    private Boolean active;
}
