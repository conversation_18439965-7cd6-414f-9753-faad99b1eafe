package com.yuanchuan.user.domain.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yuanchuan.user.domain.model.AuditLog;
import com.yuanchuan.user.domain.repository.AuditLogRepository;
import com.yuanchuan.user.domain.service.AuditLogDomainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 审计日志领域服务实现类
 */
@Slf4j
@Service
public class AuditLogDomainServiceImpl implements AuditLogDomainService {

    @Autowired
    private AuditLogRepository auditLogRepository;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public Long recordAuditLog(String bizType, Long bizId, String actionType, Long operatorId, String operatorName,
                              Map<String, Object> oldValues, Map<String, Object> newValues, String remark) {
        try {
            AuditLog auditLog = new AuditLog();
            auditLog.setBizType(bizType);
            auditLog.setBizId(bizId);
            auditLog.setActionType(actionType);
            auditLog.setOperatorId(operatorId);
            auditLog.setOperatorName(operatorName);
            auditLog.setOperateTime(new Date());
            auditLog.setRemark(remark);
            auditLog.setActive(0);
            auditLog.setCreatedAt(new Date());
            auditLog.setUpdatedAt(new Date());
            auditLog.setCreatedBy(operatorName);
            auditLog.setUpdatedBy(operatorName);

            // 构建变更字段JSON
            Map<String, Map<String, Object>> changeFields = buildChangeFields(oldValues, newValues);
            auditLog.setChangeFields(objectMapper.writeValueAsString(changeFields));

            AuditLog savedLog = auditLogRepository.save(auditLog);
            return savedLog.getId();
        } catch (Exception e) {
            log.error("记录审计日志失败: bizType={}, bizId={}, actionType={}", bizType, bizId, actionType, e);
            return null;
        }
    }

    @Override
    public List<AuditLog> getAuditLogs(String bizType, Long bizId) {
        return auditLogRepository.findByBizTypeAndBizId(bizType, bizId);
    }

    @Override
    public List<AuditLog> queryAuditLogs(String bizType, Long operatorId, Integer pageNum, Integer pageSize) {
        Integer offset = (pageNum - 1) * pageSize;
        return auditLogRepository.findByConditions(bizType, operatorId, offset, pageSize);
    }

    @Override
    public Long countAuditLogs(String bizType, Long operatorId) {
        return auditLogRepository.countByConditions(bizType, operatorId);
    }

    /**
     * 构建变更字段JSON结构
     * 格式：{"字段名": {"before": "旧值", "after": "新值"}}
     */
    private Map<String, Map<String, Object>> buildChangeFields(Map<String, Object> oldValues, Map<String, Object> newValues) {
        Map<String, Map<String, Object>> changeFields = new HashMap<>();

        if (oldValues == null) {
            oldValues = new HashMap<>();
        }
        if (newValues == null) {
            newValues = new HashMap<>();
        }

        // 获取所有字段名
        Set<String> allFields = new HashSet<>();
        allFields.addAll(oldValues.keySet());
        allFields.addAll(newValues.keySet());

        for (String field : allFields) {
            Object oldValue = oldValues.get(field);
            Object newValue = newValues.get(field);

            // 只记录有变化的字段
            if (!Objects.equals(oldValue, newValue)) {
                Map<String, Object> fieldChange = new HashMap<>();
                fieldChange.put("before", oldValue);
                fieldChange.put("after", newValue);
                changeFields.put(field, fieldChange);
            }
        }

        return changeFields;
    }
}
