package com.yuanchuan.user.domain.model;

import com.yuanchuan.common.enums.users.login.PlatformType;
import com.yuanchuan.user.context.enums.RegisterSourceEnum;
import com.yuanchuan.user.context.enums.RegistrationTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户注册请求DTO
 */
@Data
public class UserRegisterDomain implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 手机号
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;


    /**
     * 平台类型
     */
    private PlatformType source;


    /**
     * 昵称
     */
    private String nickname;

    /**
     * 注册IP
     */
    private String registerIp;

    /**
     * 注册设备信息
     */
    private String registerDevice;


    /**
     * 设备信息
     */
    private AccountDevice accountDevice;

    private String accountName;

    private String passWord;

    /**
     * 首次APP登录时间
     */
    private LocalDateTime firstAppLoginAt;


    /**
     * 注册类型
     */
    private RegistrationTypeEnum registrationType;


    /**
     * 注册来源
     */
    private RegisterSourceEnum registerSource = RegisterSourceEnum.APP;

    /**
     * 三方绑定信息
     */
    private UserThirdPartyBinding userThirdPartyBinding;

    /**
     * 三方授权信息
     */
    private UserThirdPartyAuth userThirdPartyAuth;

}
