package com.yuanchuan.user.domain.repository;

import com.yuanchuan.common.domain.query.PageQueryV;
import com.yuanchuan.user.domain.model.Permission;

import java.util.List;
import java.util.Optional;

/**
 * 权限仓储接口
 */
public interface PermissionRepository {

    /**
     * 保存权限信息
     *
     * @param permission 权限信息
     * @return 保存后的权限信息
     */
    Permission save(Permission permission);

    /**
     * 根据ID查询权限
     *
     * @param id 权限ID
     * @return 权限信息
     */
    Optional<Permission> findById(Long id);

    /**
     * 根据权限编码查询权限
     *
     * @param permissionCode 权限编码
     * @return 权限信息
     */
    Optional<Permission> findByPermissionCode(String permissionCode);

    /**
     * 根据权限名称查询权限
     *
     * @param permissionName 权限名称
     * @return 权限信息
     */
    Optional<Permission> findByPermissionName(String permissionName);

    /**
     * 查询所有权限
     *
     * @return 权限列表
     */
    List<Permission> findAll();

    /**
     * 根据类型查询权限
     *
     * @param type 权限类型
     * @return 权限列表
     */
    List<Permission> findByType(String type);

    /**
     * 根据父权限ID查询子权限
     *
     * @param parentId 父权限ID
     * @return 权限列表
     */
    List<Permission> findByParentId(Long parentId);

    /**
     * 根据账户ID查询用户权限
     *
     * @param accountId 账户ID
     * @return 权限列表
     */
    List<Permission> findByAccountId(Long accountId);

    /**
     * 根据角色ID查询权限
     *
     * @param roleId 角色ID
     * @return 权限列表
     */
    List<Permission> findByRoleId(Long roleId);

    /**
     * 分页查询权限
     *
     * @param permissionMenu 父权限ID
     * @param status 状态
     * @param keyword 关键词
     * @param pageQuery 分页查询参数
     * @return 权限列表
     */
    List<Permission> findByConditions(String permissionMenu, Integer status, String keyword, PageQueryV pageQuery);

    /**
     * 统计权限数量
     *
     * @param permissionMenu 父权限ID
     * @param status 状态
     * @param keyword 关键词
     * @return 权限数量
     */
    Long countByConditions(String permissionMenu, Integer status, String keyword);

    /**
     * 删除权限（逻辑删除）
     *
     * @param id 权限ID
     * @return 是否成功
     */
    Boolean deleteById(Long id);

    Permission findByPermissionId(Long id);
}
