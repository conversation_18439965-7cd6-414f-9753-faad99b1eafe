package com.yuanchuan.user.domain.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 账户设备领域对象
 */
@Data
@Accessors(chain = true)
public class AccountDevice {
    /**
     * 设备ID
     */
    private Long id;
    
    /**
     * 关联账户ID
     */
    private Long accountId;
    
    /**
     * 设备唯一标识
     */
    private String deviceId;
    
    /**
     * 设备类型
     */
    private String deviceType;
    
    /**
     * 设备名称
     */
    private String deviceName;
    
    /**
     * 操作系统版本
     */
    private String osVersion;
    
    /**
     * 应用版本
     */
    private String appVersion;
    
    /**
     * IP地址
     */
    private String ipAddress;
    
    /**
     * User-Agent
     */
    private String userAgent;
    
    /**
     * 是否已验证
     */
    private Boolean isVerified;
    
    /**
     * 是否活跃
     */
    private Boolean isActive;
    
    /**
     * 最后登录时间
     */
    private LocalDateTime lastLoginAt;
    
    /**
     * 来源
     */
    private String source;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新人
     */
    private String updatedBy;


    /**
     * 是否启用 0-禁用(删除) 1-启用
     */
    private Boolean active;

    
    /**
     * 设备来源枚举
     */
    public enum DeviceSource {
        /**
         * C端用户登录
         */
        CUSTOMER_LOGIN,
        
        /**
         * 商户登录
         */
        SHOP_LOGIN
    }
    
    /**
     * 设备类型枚举
     */
    public enum DeviceType {
        /**
         * iOS设备
         */
        IOS,
        
        /**
         * Android设备
         */
        ANDROID,
        
        /**
         * Web浏览器
         */
        WEB,
        
        /**
         * 其他设备
         */
        OTHER
    }
}
