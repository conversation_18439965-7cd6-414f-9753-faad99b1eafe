package com.yuanchuan.user.domain.repository;

import com.yuanchuan.user.domain.model.BusinessAccountRole;

import java.util.List;
import java.util.Optional;

/**
 * 商户账户角色关联仓储接口
 */
public interface BusinessAccountRoleRepository {
    /**
     * 保存商户账户角色关联信息
     *
     * @param businessAccountRole 商户账户角色关联信息
     * @return 保存后的商户账户角色关联信息
     */
    BusinessAccountRole save(BusinessAccountRole businessAccountRole);

    /**
     * 批量保存商户账户角色关联信息
     *
     * @param businessAccountRoles 商户账户角色关联信息列表
     * @return 保存后的商户账户角色关联信息列表
     */
    List<BusinessAccountRole> batchSave(List<BusinessAccountRole> businessAccountRoles);

    /**
     * 根据ID查询商户账户角色关联
     *
     * @param id 商户账户角色关联ID
     * @return 商户账户角色关联信息
     */
    Optional<BusinessAccountRole> findById(Long id);

    /**
     * 根据商户账户ID查询商户账户角色关联列表
     *
     * @param businessAccountId 商户账户ID
     * @return 商户账户角色关联列表
     */
    List<BusinessAccountRole> findByBusinessAccountId(Long businessAccountId);

    /**
     * 根据角色ID查询商户账户角色关联列表
     *
     * @param roleId 角色ID
     * @return 商户账户角色关联列表
     */
    List<BusinessAccountRole> findByRoleId(Long roleId);

    /**
     * 根据商户账户ID和角色ID查询商户账户角色关联
     *
     * @param businessAccountId 商户账户ID
     * @param roleId 角色ID
     * @return 商户账户角色关联信息
     */
    Optional<BusinessAccountRole> findByBusinessAccountIdAndRoleId(Long businessAccountId, Long roleId);

    /**
     * 根据商户账户ID删除商户账户角色关联
     *
     * @param businessAccountId 商户账户ID
     * @return 删除的记录数
     */
    int deleteByBusinessAccountId(Long businessAccountId);

    /**
     * 根据商户账户ID和角色ID删除商户账户角色关联
     *
     * @param businessAccountId 商户账户ID
     * @param roleId 角色ID
     * @return 删除的记录数
     */
    int deleteByBusinessAccountIdAndRoleId(Long businessAccountId, Long roleId);
}
