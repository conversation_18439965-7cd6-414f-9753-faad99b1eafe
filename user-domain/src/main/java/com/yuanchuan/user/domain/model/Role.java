package com.yuanchuan.user.domain.model;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 角色领域对象
 */
@Data
@Accessors(chain = true)
public class Role {
    /**
     * 角色ID
     */
    private Long id;
    
    /**
     * 角色编码，唯一，例如：ADMIN、SHOP_MANAGER
     */
    private String roleCode;
    
    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 所属组织ID
     */
    private Long orgId;

    /**
     * 角色描述
     */
    private String description;

    /**
     * 是否启用 0-禁用 1-启用
     */
    private Integer status;

    /**
     * 是否启用 0-禁用(删除) 1-启用
     */
    private Integer active;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 创建人
     */
    private String createdBy;
    
    /**
     * 更新人
     */
    private String updatedBy;

    private String permissionIds; // e.g. "101,102,103"
    private String permissionGroupDesc; // e.g. "商户审核：审核信息查看、审核处理"
}
