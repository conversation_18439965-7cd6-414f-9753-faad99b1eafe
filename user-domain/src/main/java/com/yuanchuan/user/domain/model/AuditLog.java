package com.yuanchuan.user.domain.model;

import lombok.Data;

import java.util.Date;

/**
 * 角色权限差异字段日志表
 * @TableName audit_log
 */
@Data
public class AuditLog {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 业务类型，如 ROLE、PERMISSION、ROLE_PERMISSION 等
     */
    private String bizType;

    /**
     * 业务主键ID
     */
    private Long bizId;

    /**
     * 操作类型（CREATE、UPDATE、DELETE）
     */
    private String actionType;

    /**
     * 操作人ID
     */
    private Long operatorId;

    /**
     * 操作人姓名
     */
    private String operatorName;

    /**
     * 操作时间
     */
    private Date operateTime;

    /**
     * 变更字段及前后值，结构：{"字段名": {"before": "旧值", "after": "新值"}}
     */
    private Object changeFields;

    /**
     * 备注或操作说明
     */
    private String remark;

    /**
     * 是否启用
     */
    private Integer active;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 更新时间
     */
    private Date updatedAt;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新人
     */
    private String updatedBy;
}