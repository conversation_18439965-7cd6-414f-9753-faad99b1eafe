package com.yuanchuan.user.domain.repository;

import com.yuanchuan.user.context.enums.ContentType;
import com.yuanchuan.user.domain.model.ContentConfig;

import java.util.List;
import java.util.Optional;

/**
 * 内容配置仓储接口
 */
public interface ContentConfigRepository {

    /**
     * 保存内容配置
     *
     * @param contentConfig 内容配置
     * @return 保存后的内容配置
     */
    ContentConfig save(ContentConfig contentConfig);

    /**
     * 根据ID查询内容配置
     *
     * @param id 内容配置ID
     * @return 内容配置
     */
    Optional<ContentConfig> findById(Long id);

    /**
     * 根据类型查询内容配置列表
     *
     * @param type 内容类型
     * @return 内容配置列表
     */
    List<ContentConfig> findByType(ContentType type);

    /**
     * 根据类型和标题查询内容配置
     *
     * @param type  内容类型
     * @param title 标题
     * @return 内容配置
     */
    Optional<ContentConfig> findByTypeAndTitle(ContentType type, String title);

    /**
     * 查询所有内容配置
     *
     * @return 内容配置列表
     */
    List<ContentConfig> findAll();

    /**
     * 查询所有启用的内容配置
     *
     * @return 内容配置列表
     */
    List<ContentConfig> findAllEnabled();

    /**
     * 根据条件动态查询内容配置
     *
     * @param condition 查询条件对象，包含类型、启用状态、父级ID等
     * @return 内容配置列表
     */
    List<ContentConfig> findByCondition(ContentConfig condition);

    /**
     * 根据类型查询所有启用的内容配置
     *
     * @param type 内容类型
     * @return 内容配置列表
     */
    List<ContentConfig> findEnabledByType(ContentType type);

    /**
     * 根据父级ID查询内容配置列表
     *
     * @param parentId 父级内容ID
     * @return 内容配置列表
     */
    List<ContentConfig> findByParentId(Long parentId);

    /**
     * 根据父级ID查询所有启用的内容配置
     *
     * @param parentId 父级内容ID
     * @return 内容配置列表
     */
    List<ContentConfig> findEnabledByParentId(Long parentId);


    List<ContentConfig> findEnabledByParentIds(List<Long> parentIds);

    /**
     * 查询所有一级内容（父级ID为null或0的内容）
     *
     * @return 内容配置列表
     */
    List<ContentConfig> findRootContents();

    /**
     * 查询所有启用的一级内容（父级ID为null或0的内容）
     *
     * @return 内容配置列表
     */
    List<ContentConfig> findEnabledRootContents();

    /**
     * 根据类型查询所有一级内容（父级ID为null或0的内容）
     *
     * @param type 内容类型
     * @return 内容配置列表
     */
    List<ContentConfig> findRootContentsByType(ContentType type);

    /**
     * 根据类型查询所有启用的一级内容（父级ID为null或0的内容）
     *
     * @param type 内容类型
     * @return 内容配置列表
     */
    List<ContentConfig> findEnabledRootContentsByType(ContentType type);

    /**
     * 逻辑删除内容配置
     *
     * @param id 内容配置ID
     * @return 是否删除成功
     */
    boolean deleteById(Long id);
}
