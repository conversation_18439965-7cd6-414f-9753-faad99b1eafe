package com.yuanchuan.user.domain.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 登录日志领域对象
 */
@Data
@Accessors(chain = true)
public class LoginLogDomian {
    /**
     * 日志ID
     */
    private Long id;
    
    /**
     * 关联账户ID
     */
    private Long accountId;
    
    /**
     * 设备ID
     */
    private String deviceId;
    
    /**
     * 登录类型
     */
    private String loginType;
    
    /**
     * 登录状态
     */
    private String loginStatus;
    
    /**
     * IP地址
     */
    private String ipAddress;
    
    /**
     * User-Agent
     */
    private String userAgent;
    
    /**
     * 登录位置
     */
    private String location;
    
    /**
     * 登录时间
     */
    private LocalDateTime loginAt;
    
    /**
     * 来源
     */
    private String source;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    

    
    /**
     * 登录状态枚举
     */
    public enum LoginStatus {
        /**
         * 成功
         */
        SUCCESS,
        
        /**
         * 失败
         */
        FAIL
    }

}
