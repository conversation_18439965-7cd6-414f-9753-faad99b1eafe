package com.yuanchuan.user.domain.auth;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.yuanchuan.common.utils.AuthStateUtils;
import com.yuanchuan.common.utils.HttpUtils;
import me.zhyd.oauth.cache.AuthStateCache;
import me.zhyd.oauth.config.AuthConfig;
import me.zhyd.oauth.enums.AuthResponseStatus;
import me.zhyd.oauth.exception.AuthException;
import me.zhyd.oauth.model.AuthCallback;
import me.zhyd.oauth.model.AuthResponse;
import me.zhyd.oauth.model.AuthToken;
import me.zhyd.oauth.model.AuthUser;
import me.zhyd.oauth.request.AuthDefaultRequest;

import java.net.URLEncoder;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @date 2025-05-12 14:26:14:26
 */
public class FetnetAuthRequest extends AuthDefaultRequest {


    private final FetnetProperties fetnetProperties;

    public FetnetAuthRequest(AuthConfig config, FetnetProperties fetnetProperties) {
        super(config, FetnetAuthSource.FETNET);
        this.fetnetProperties = fetnetProperties;
    }

    public FetnetAuthRequest(AuthConfig config, AuthStateCache authStateCache, FetnetProperties fetnetProperties) {
        super(config, FetnetAuthSource.FETNET, authStateCache);
        this.fetnetProperties = fetnetProperties;
    }


    @Override
    public String authorize(String state) {
        String codeChallenge = generateCodeChallenge(state);
        Map<String, String> params = new HashMap<>();

        // 添加PKCE参数
        params.put("code_challenge_method", "S256");
        params.put("code_challenge", codeChallenge);

        // 添加FETnet自定义参数
        addFetnetCustomParams(params);

        // 缓存code_verifier
        // authStateCache.cache(state, codeChallenge);

        // 构造 URL
        String baseAuthorizeUrl = fetnetProperties.getBaseUrl() + fetnetProperties.getAuthorizeUrl();
        return HttpUtils.appendUrlParams(baseAuthorizeUrl, params);
    }


    /**
     * 获取access token
     *
     * @param authCallback 授权成功后的回调参数
     * @return token
     * @see AuthDefaultRequest#authorize()
     * @see AuthDefaultRequest#authorize(String)
     */
    @Override
    public AuthToken getAccessToken(AuthCallback authCallback) {

        // 获取缓存的code_verifier
        String codeVerifier = authStateCache.get(authCallback.getState());

        Map<String, String> form = new HashMap<>();
        form.put("client_id", config.getClientId());
        form.put("client_secret", config.getClientSecret());
        form.put("code", authCallback.getCode());
        form.put("grant_type", "authorization_code");
        form.put("redirect_uri", config.getRedirectUri());
        form.put("code_verifier", codeVerifier); // 添加PKCE验证

        HttpUtils.HttpResponse response = null;
        try {
            response = HttpUtils.postForm(source.accessToken(), form);
        } catch (Exception e) {
            throw new AuthException("Token parse failed", e);
        }
        return parseToken(response);
    }



    /**
     * 使用token换取用户信息
     *
     * @param authToken token信息
     * @return 用户信息
     * @see AuthDefaultRequest#getAccessToken(AuthCallback)
     */
    @Override
    public AuthUser getUserInfo(AuthToken authToken) {
        return AuthUser.builder()/*.username("test").nickname("test").gender(AuthUserGender.MALE).token(authToken).source(this.source.toString())*/.build();
    }

    /**
     * 撤销授权
     *
     * @param authToken 登录成功后返回的Token信息
     * @return AuthResponse
     */
    @Override
    public AuthResponse revoke(AuthToken authToken) {
        Map<String, String> form = new HashMap<>();
        form.put("client_id", config.getClientId());
        form.put("client_secret", config.getClientSecret());
        form.put("token", authToken.getAccessToken());

        HttpUtils.HttpResponse response = null;
        try {
            response = HttpUtils.postForm(fetnetProperties.getRevokeUrl(), form);
        } catch (Exception e) {
            throw new AuthException("Token parse failed", e);
        }
        return AuthResponse.builder().code(response.getCode()).msg(response.getBody()).build();
    }

    /**
     * 刷新access token （续期）
     *
     * @param authToken 登录成功后返回的Token信息
     * @return AuthResponse
     */
    @Override
    public AuthResponse refresh(AuthToken authToken) {
        return AuthResponse.builder().code(AuthResponseStatus.SUCCESS.getCode()).data(AuthToken.builder().openId("openId").expireIn(1000).idToken("idToken").scope("scope").refreshToken("refreshToken").accessToken("accessToken").code("code").build()).build();
    }



    private void addFetnetCustomParams(Map<String, String> params) {
        // 添加标准参数
        params.put("display", "desktop");
        params.put("x_aic_login", "true");

        // 处理x_friday参数
        String fridayParams = "v3=08fetnet=58fetnet=18google=18fb=18yahoo=18visitor_uri=https://shopping.friday.tw/login/sdp/visitor/callback";
        String encoded = URLEncoder.encode(
                Base64.getEncoder().encodeToString(fridayParams.getBytes(StandardCharsets.UTF_8)),
                StandardCharsets.UTF_8
        );
        params.put("x_friday", encoded);

        // 其他自定义参数...
        params.put("x_fido", "true");
        params.put("x_logo", "shopping");
    }

    private String generateCodeChallenge(String state) {
        // 生成PKCE code_verifier和challenge
        String codeVerifier = AuthStateUtils.generateCodeVerifier();
        authStateCache.cache(state, codeVerifier);
        return AuthStateUtils.generateCodeChallenge(codeVerifier);
    }

    private AuthToken parseToken(HttpUtils.HttpResponse response) throws AuthException {
        if (response.getCode() != 200) {
            throw new AuthException("Token request failed: " + response.getBody());
        }

        try {
            JSONObject node = JSON.parseObject(response.getBody());
            JSONObject tokens =  node.getJSONObject("token");

            return AuthToken.builder()
                    .openId(node.getString("uuid"))
                    .expireIn(tokens.getInteger("expireIn"))
                    .idToken(tokens.getString("idToken"))
                    .scope(tokens.getString("scope"))
                    .refreshToken(tokens.getString("refreshToken"))
                    .accessToken(tokens.getString("accessToken"))
                    // .code("code")
                    .build();

        } catch (Exception e) {
            throw new AuthException("Token parse failed", e);
        }
    }

}
