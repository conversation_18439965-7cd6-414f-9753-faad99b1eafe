package com.yuanchuan.user.domain.service;

import com.yuanchuan.user.domain.model.AuditLog;

import java.util.List;
import java.util.Map;

/**
 * 审计日志领域服务接口
 */
public interface AuditLogDomainService {

    /**
     * 记录操作日志
     *
     * @param bizType 业务类型：ROLE-角色，PERMISSION-权限
     * @param bizId 业务ID
     * @param actionType 操作类型：CREATE-创建，UPDATE-更新，DELETE-删除
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     * @param oldValues 变更前的值
     * @param newValues 变更后的值
     * @param remark 备注
     * @return 审计日志ID
     */
    Long recordAuditLog(String bizType, Long bizId, String actionType, Long operatorId, String operatorName,
                       Map<String, Object> oldValues, Map<String, Object> newValues, String remark);

    /**
     * 查询业务操作日志
     *
     * @param bizType 业务类型
     * @param bizId 业务ID
     * @return 审计日志列表
     */
    List<AuditLog> getAuditLogs(String bizType, Long bizId);

    /**
     * 分页查询审计日志
     *
     * @param bizType 业务类型
     * @param operatorId 操作人ID
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 审计日志列表
     */
    List<AuditLog> queryAuditLogs(String bizType, Long operatorId, Integer pageNum, Integer pageSize);

    /**
     * 统计审计日志数量
     *
     * @param bizType 业务类型
     * @param operatorId 操作人ID
     * @return 日志数量
     */
    Long countAuditLogs(String bizType, Long operatorId);
}
