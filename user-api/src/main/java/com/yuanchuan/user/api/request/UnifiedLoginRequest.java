package com.yuanchuan.user.api.request;

import com.yuanchuan.authentication.context.enums.BindingPhoneVerifyStatus;
import com.yuanchuan.common.enums.users.ThirdAuthPlatformType;
import com.yuanchuan.common.enums.users.login.LoginType;
import com.yuanchuan.common.enums.users.login.PlatformType;
import com.yuanchuan.user.api.dto.AccountDeviceDTO;
import com.yuanchuan.user.context.enums.RegisterSourceEnum;
import com.yuanchuan.user.context.enums.SmsAndEmailBusinessType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * 统一登录请求
 */
@Data
@Schema(description = "统一登录请求")
public class UnifiedLoginRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    @NotNull
    @Schema(description = "登录类型", required = true, example = "0")
    private LoginType loginType;

    @Schema(description = "手机号，当loginType为SMS_CODE或PASSWORD且使用手机号登录时必填", example = "**********")
    private String phone;

    @Schema(description = "邮箱，当loginType为EMAIL_CODE或PASSWORD且使用邮箱登录时必填", example = "<EMAIL>")
    private String email;

    @Schema(description = "验证码，当loginType为SMS_CODE或EMAIL_CODE时必填", example = "123456")
    private String code;

    @Schema(description = "密码，当loginType为PASSWORD时必填", example = "password123")
    private String password;

    @Schema(description = "第三方平台类型，当loginType为THIRD_PARTY时必填", example = "LINE")
    private ThirdAuthPlatformType thirdPartyType;

    @Schema(description = "第三方授权码，当loginType为THIRD_PARTY且非首次授权时可能需要", example = "auth_code_from_third_party")
    private String authCode;

    @Schema(description = "是否沿用已有账号，当绑定手机号且手机号已存在账号时使用", example = "true")
    private Boolean accountUseFlag;

    @Schema(description = "流程状态，用于多步骤流程的状态跟踪")
    private BindingPhoneVerifyStatus flowStatus;

    @Schema(description = "平台类型", example = "CUSTOMER")
    private PlatformType platform = PlatformType.CUSTOMER;

    @Schema(description = "设备信息对象")
    private AccountDeviceDTO deviceInfo;

    @Schema(description = "业务类型，用于验证码业务场景区分")
    private SmsAndEmailBusinessType businessType = SmsAndEmailBusinessType.REGISTER_LOGIN_CODE;

    @Schema(description = "用户代理字符串", example = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
    private String userAgent;

    @Schema(description = "注册来源", example = "app")
    private RegisterSourceEnum registerSourceEnum;

    private Long userId;

    private String clientIp;

    @Schema(description = "二次验证令牌（用于替代明文userId）", example = "encrypted_token_here")
    private String secondaryVerificationToken;
}
