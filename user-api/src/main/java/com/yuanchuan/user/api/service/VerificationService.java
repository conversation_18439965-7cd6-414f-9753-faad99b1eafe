package com.yuanchuan.user.api.service;

import com.yuanchuan.user.context.enums.SmsAndEmailBusinessType;

/**
 * 验证码服务接口
 */
public interface VerificationService {


    /**
     * 发送手机验证码（带业务类型）
     *
     * @param phone 手机号
     * @param businessType 业务类型
     * @return 验证码过期时间戳
     */
    Long sendSmsCode(String phone, SmsAndEmailBusinessType businessType);

    /**
     * 发送邮箱验证码
     *
     * @param email 邮箱
     * @return 验证码过期时间戳
     */
    Long sendEmailCode(String email, SmsAndEmailBusinessType businessType);



    /**
     * 验证手机验证码（带业务类型）
     *
     * @param phone 手机号
     * @param code 验证码
     * @param businessType 业务类型
     * @return 是否验证通过
     */
    boolean verifySmsCode(String phone, String code, SmsAndEmailBusinessType businessType);

    /**
     * 验证邮箱验证码
     *
     * @param email 邮箱
     * @param code 验证码
     * @return 是否验证通过
     */
    boolean verifyEmailCode(String email, String code,SmsAndEmailBusinessType businessType);

    /**
     * 检查手机号发送频率限制
     *
     * @param phone 手机号
     * @return 是否可以发送
     */
    boolean checkSmsRateLimit(String phone);

    /**
     * 检查邮箱发送频率限制
     *
     * @param email 邮箱
     * @return 是否可以发送
     */
    boolean checkEmailRateLimit(String email);

    /**
     * 检查IP发送频率限制
     *
     * @param ip IP地址
     * @return 是否可以发送
     */
    boolean checkIpRateLimit(String ip);
}
