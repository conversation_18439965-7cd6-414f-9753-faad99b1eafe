package com.yuanchuan.user.api.service;

import com.yuanchuan.user.api.dto.AccountDeviceDTO;
import com.yuanchuan.user.api.dto.UserDeviceOperationDTO;

import java.util.List;

/**
 * 设备管理服务接口
 */
public interface UserDeviceService {

    /**
     * 注册设备
     * @param deviceInfoDTO 设备信息
     * @return 操作是否成功
     */
    boolean registerDevice(AccountDeviceDTO deviceInfoDTO);

    /**
     * 绑定设备
     *
     * @param deviceInfoDTO 设备信息
     * @return 操作是否成功
     */
    boolean bindDevice(AccountDeviceDTO deviceInfoDTO);

    /**
     * 解绑设备
     *
     * @param operationDTO 操作参数
     * @return 操作是否成功
     */
    boolean unbindDevice(UserDeviceOperationDTO operationDTO);

    /**
     * 禁用设备
     *
     * @param operationDTO 操作参数
     * @return 操作是否成功
     */
    boolean disableDevice(UserDeviceOperationDTO operationDTO);

    /**
     * 重命名设备
     *
     * @param operationDTO 操作参数
     * @return 操作是否成功
     */
    boolean renameDevice(UserDeviceOperationDTO operationDTO);

    /**
     * 获取设备详情
     *
     * @param deviceId 设备ID
     * @return 设备信息
     */
    AccountDeviceDTO getDeviceInfo(Long deviceId);

    /**
     * 获取用户设备列表
     *
     * @param userId 用户ID
     * @return 设备列表
     */
    List<AccountDeviceDTO> getUserDevices(Long userId);
}
