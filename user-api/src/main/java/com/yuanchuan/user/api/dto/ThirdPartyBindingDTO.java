package com.yuanchuan.user.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 第三方绑定相关DTO
 */
public class ThirdPartyBindingDTO {

    /**
     * 第三方绑定信息
     */
    @Data
    @Schema(description = "第三方绑定信息")
    public static class ThirdPartyBindingInfo implements Serializable {
        private static final long serialVersionUID = 1L;

        @Schema(description = "绑定ID")
        private Long id;

        @Schema(description = "第三方平台：APPLE_ID-苹果ID, FACEBOOK-脸书, GOOGLE-谷歌, LINE-Line等")
        private String platform;
    }

    /**
     * 第三方绑定列表响应
     */
    @Data
    @Schema(description = "第三方绑定列表响应")
    public static class ThirdPartyBindingListResponse implements Serializable {
        private static final long serialVersionUID = 1L;

        @Schema(description = "第三方绑定列表")
        private List<ThirdPartyBindingInfo> bindingList;
    }

    /**
     * 解绑第三方请求
     */
    @Data
    @Schema(description = "解绑第三方请求")
    public static class UnbindThirdPartyRequest implements Serializable {
        private static final long serialVersionUID = 1L;

        @Schema(description = "绑定ID", required = true)
        @NotBlank(message = "綁定 ID 不可為空")
        private Long bindingId;
    }

    /**
     * 绑定第三方请求
     */
    @Data
    @Schema(description = "绑定第三方请求")
    public static class BindThirdPartyRequest implements Serializable {
        private static final long serialVersionUID = 1L;

        @Schema(description = "第三方平台用户唯一标识", required = true)
        @NotBlank(message = "第三方平台使用者唯一識別碼不可為空")
        private String sub;
    }
}
