package com.yuanchuan.user.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 权限树形结构DTO
 */
@Data
@Schema(description = "权限树形结构DTO")
public class PermissionTreeDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "权限ID")
    private Long permissionId;

    @Schema(description = "权限编码")
    private String permissionCode;

    @Schema(description = "权限类型：MENU-菜单, PAGE-页面, BUTTON-按钮, API-接口")
    private String type;

    @Schema(description = "权限名称")
    private String permissionName;

    @Schema(description = "页面路径或API路径")
    private String url;

    @Schema(description = "子权限列表")
    private List<PermissionTreeDTO> children;
}
