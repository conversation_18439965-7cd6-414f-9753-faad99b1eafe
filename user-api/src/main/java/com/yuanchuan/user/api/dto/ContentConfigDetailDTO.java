package com.yuanchuan.user.api.dto;


import com.yuanchuan.user.context.enums.ContentType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 内容配置详情DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "内容配置详情DTO")
public class ContentConfigDetailDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "内容类型")
    private ContentType type;

    @Schema(description = "内容类型描述")
    private String typeDescription;

    @Schema(description = "标题枚举类型")
    private String titleEnum;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "内容")
    private String content;

    @Schema(description = "父级内容ID，用于构建层级关系，如果为null，表示是一级内容")
    private Long parentId;

    @Schema(description = "跳转URL，用于帮助内容跳转到特定页面，如果为null，表示不需要跳转")
    private String redirectUrl;

    @Schema(description = "子内容列表，用于帮助内容的层级结构")
    private List<ContentConfigDetailDTO> children;

    @Schema(description = "排序")
    private Integer sortOrder;

    @Schema(description = "是否启用")
    private Boolean isEnabled;

    @Schema(description = "创建人")
    private String createdBy;

    @Schema(description = "更新人")
    private String updatedBy;

    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;
}
