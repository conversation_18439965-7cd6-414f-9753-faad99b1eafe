package com.yuanchuan.user.api.response;

import com.yuanchuan.user.api.dto.PermissionTreeDTO;
import com.yuanchuan.user.api.dto.RoleDTO;
import com.yuanchuan.user.api.dto.TokenInfoDTO;
import com.yuanchuan.user.api.dto.UserInfoDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

import java.io.Serializable;

/**
 * @ClassName LoginResponse
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/5/16 10:12
 * @Version 1.0
 */
@Data
public class LoginResponse  implements Serializable {
    private static final long serialVersionUID = 1L;



    @Schema(description = "用户ID", example = "123")
    private Long userId;

    @Schema(description = "账户Id", example = "123")
    private Long businessAccountId;

    @Schema(description = "二次验证令牌（用于替代明文userId）", example = "encrypted_token_here")
    private String secondaryVerificationToken;

    @Schema(description = "Token信息")
    private TokenInfoDTO token;

    @Schema(description = "用户信息")
    private UserInfoDTO userInfo;

    @Schema(description = "是否跨平台用户（在其他平台注册的用户）", example = "true")
    private Boolean isCrossPlatformUser  = Boolean.FALSE;

    @Schema(description = "所有可验证的方式列表")
    private List<TwoVerificationResponse> twoVerificationResponses;

    @Schema(description = "角色列表")
    private List<RoleDTO> userRoles;

    @Schema(description = "权限列表")
    private List<PermissionTreeDTO> userPermissions;



    @Data
    public static class TwoVerificationResponse implements Serializable{
        private static final long serialVersionUID = 1L;


        @Schema(description = "可验证的方式")
        private String verificationType;

        @Schema(description = "脱敏的验证目标 邮箱或者手机号", example = "138****1234")
        private String maskedTarget;


    }






}