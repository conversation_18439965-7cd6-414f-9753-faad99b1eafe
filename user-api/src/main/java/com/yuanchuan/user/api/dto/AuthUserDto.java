package com.yuanchuan.user.api.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025-05-10 14:13:14:13
 */
@Data
public class AuthUserDto implements Serializable {

    private String avatar;
    private String email;
    private String gender;
    private String location;
    private String nickname;
    private RawUserInfo rawUserInfo;
    private String source;
    private Token token;
    private String username;
    private String uuid;

    @Data
    public class RawUserInfo {

        private String sub;
        private Boolean emailVerified;
        private String name;
        private String givenName;
        private String locale;
        private String picture;
        private String email;
        @JsonProperty("at_hash")
        private String atHash;
        private String aud;
        private Long authTime;
        private String iss;
        private Date exp;
        private Date iat;
        private Boolean nonceSupported;
        private Boolean snapshotUser;
    }

    @Data
    class Token {

        // @JsonProperty("access_token")
        private String accessToken;
        private Integer expireIn;
        // @JsonProperty("id_token")
        private String idToken;
        // @JsonProperty("refresh_token")
        private String refreshToken;
        // @JsonProperty("refresh_token_expire_in")
        private Integer refreshTokenExpireIn;
        private Boolean snapshotUser;
        // @JsonProperty("token_type")
        private String tokenType;
        private String scope;
    }

    public AuthUserResponse convertToAuthUserResponse() {
        AuthUserResponse response = new AuthUserResponse();
        response.setSub(this.rawUserInfo.getSub());
        response.setNickname(this.nickname);
        response.setAvatar(this.avatar);
        response.setGender(this.gender);
        response.setAccessToken(this.token.getAccessToken());
        response.setRefreshToken(this.token.getRefreshToken());
        response.setTokenType(this.token.getTokenType());
        response.setScope(this.token.getScope());
        response.setExpiresAt(this.token.getExpireIn());
        response.setRefreshExpiresAt(this.token.getRefreshTokenExpireIn());
        response.setPlatform(this.source);
        return response;
    }

}
