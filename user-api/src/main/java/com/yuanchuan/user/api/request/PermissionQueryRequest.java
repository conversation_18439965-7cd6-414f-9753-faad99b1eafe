package com.yuanchuan.user.api.request;

import com.yuanchuan.common.domain.query.PageQueryV;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 权限查询请求
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "权限查询请求")
public class PermissionQueryRequest extends PageQueryV {
    private static final long serialVersionUID = 1L;

    @Schema(description = "权限入口")
    private String permissionMenu;

    @Schema(description = "权限状态：0-禁用，1-启用")
    private Integer status;

    @Schema(description = "关键词搜索（权限名称或权限编码）")
    private String keyword;

}
