package com.yuanchuan.user.api.request;

import com.yuanchuan.common.domain.query.PageQueryV;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 角色查询请求
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "角色查询请求")
public class RoleQueryRequest extends PageQueryV {
    private static final long serialVersionUID = 1L;

    @Schema(description = "角色ID")
    private String roleId;

    @Schema(description = "角色状态：0-禁用，1-启用")
    private Integer status;

    @Schema(description = "关键词搜索（角色名称或权限名称）")
    private String keyword;
}
