package com.yuanchuan.user.api.request;

import com.yuanchuan.user.api.dto.AccountDeviceDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @ClassName UploadDeviceInfoRequest
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/5/15 20:26
 * @Version 1.0
 */
@Data
@Schema(description = "上传设备信息请求")
public  class UploadDeviceInfoRequest {
    @NotBlank(message = "平台不可為空")
    @Schema(description = "平台类型，如iOS、Android、Web等", example = "iOS", required = true)
    private String platform;

    @Schema(description = "设备信息对象，包含设备类型、设备ID等信息")
    private AccountDeviceDTO deviceInfo;
}
