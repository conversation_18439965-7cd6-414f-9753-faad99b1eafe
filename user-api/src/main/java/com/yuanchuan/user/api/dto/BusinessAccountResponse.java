package com.yuanchuan.user.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户响应DTO
 */
@Data
public class BusinessAccountResponse implements Serializable {
    private static final long serialVersionUID = 1L;
    
    /**
     * 用户ID
     */
    private Long id;

    /**
     * iD
     */
    private Long userPersonId;

    /**
     * 账户名称
     */
    private String accountName;
    
    /**
     * 邮箱
     */
    private String email;
    
    /**
     * 手机号
     */
    private String phone;
    
    /**
     * 状态
     */
    private Integer accountStatus;

    /**
     * 创建时间
     */
    private Long createTime;
    
    /**
     * 更新时间
     */
    private Long updateTime;
}
