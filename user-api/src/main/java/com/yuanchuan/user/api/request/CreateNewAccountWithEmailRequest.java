package com.yuanchuan.user.api.request;

import com.yuanchuan.authentication.context.enums.BindingPhoneVerifyStatus;
import com.yuanchuan.common.enums.users.login.PlatformType;
import com.yuanchuan.user.api.dto.AccountDeviceDTO;
import com.yuanchuan.user.api.dto.AuthUserResponse;
import com.yuanchuan.user.context.enums.RegistrationTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName CreateNewAccountWithEmailRequest
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/5/15 20:27
 * @Version 1.0
 */
@Data
@Schema(description = "确认使用已有账号请求/创建新账号（邮箱+手机号）请求")
public class CreateNewAccountWithEmailRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "是否沿用已有账号", example = "true", required = true)
    private boolean accountUseFlag;

    @Schema(description = "邮箱地址", example = "<EMAIL>", required = true)
    private String email;

    @NotBlank(message = "手機號碼不可為空")
    @Schema(description = "台湾手机号，以09开头，長10位", example = "09********", required = true)
    private String phone;

    @Schema(description = "用户昵称，如不提供则使用默认昵称", example = "美食客_1234")
    private String nickname;

    @Schema(description = "平台类型", example = "CUSTOMER")
    private PlatformType platform;

    @Schema(description = "设备信息对象，包含设备类型、设备ID等信息")
    private AccountDeviceDTO deviceInfo;

    @Schema(description = "手机号加密验证令牌，用于验证手机号的合法性", example = "a1b2c3d4e5f6g7h8:********")
    private String phoneToken;

    @Schema(description = "注册类型：PHONE_REGISTRATION-手机号注册，EMAIL_BINDING-邮箱注册绑定手机号")
    private RegistrationTypeEnum registrationType;

    @Schema(description = "临时令牌", example = "token-123456")
    private String temporaryToken;

    @Schema(description = "三方授权信息")
    private AuthUserResponse authUserResponse;

    @Schema(description = "流程状态，用于多步骤流程的状态跟踪")
    private BindingPhoneVerifyStatus flowStatus;

}
