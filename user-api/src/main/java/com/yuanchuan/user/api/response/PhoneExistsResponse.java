package com.yuanchuan.user.api.response;

import com.yuanchuan.user.api.dto.UserInfoDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName PhoneExistsResponse
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/5/15 20:18
 * @Version 1.0
 */
@Data
@Schema(description = "手机号已存在响应")
public class PhoneExistsResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "手机号是否已存在", example = "true")
    private Boolean exists;

    @Schema(description = "已存在账号的用户ID", example = "123456")
    private Long userId;

    @Schema(description = "已存在账号的用户信息")
    private UserInfoDTO userInfo;

}
