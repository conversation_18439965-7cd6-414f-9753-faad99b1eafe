package com.yuanchuan.user.api.request;

import com.yuanchuan.common.enums.users.login.PlatformType;
import com.yuanchuan.user.context.enums.RegisterSourceEnum;
import com.yuanchuan.user.context.enums.SmsAndEmailBusinessType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName handleSmsSecondaryVerificationRequest
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/5/28 19:43
 * @Version 1.0
 */
@Data
@Schema(description = "二次验证请求")
public class HandleSmsSecondaryVerificationRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "台湾手机号，以09开头，长度10位", example = "0912345678", required = true)
    private String phone;

    @Schema(description = "6位数字验证码", example = "123456", required = true)
    private String code;

    @Schema(description = "邮箱地址", example = "<EMAIL>", required = true)
    private String email;

    @Schema(description = "短信业务类型：PHONE_REGISTER-手机号注册，PHONE_UNBIND-解绑手机号，MERCHANT_REGISTER-商户注册", example = "1")
    private SmsAndEmailBusinessType businessType = SmsAndEmailBusinessType.PHONE_UNBIND_CODE;

    @Schema(description = "平台类型", example = "CUSTOMER")
    private PlatformType platform;

    @Schema(description = "注册来源", example = "app")
    private RegisterSourceEnum registerSource = RegisterSourceEnum.APP;
}
