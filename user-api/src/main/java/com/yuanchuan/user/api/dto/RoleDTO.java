package com.yuanchuan.user.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 角色DTO
 */
@Data
@Schema(description = "角色DTO")
public class RoleDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "角色ID")
    private Long id;

    @Schema(description = "角色编码")
    private String roleCode;

    @Schema(description = "角色名称")
    private String roleName;

    @Schema(description = "角色描述")
    private String description;

    @Schema(description = "角色状态：0-禁用，1-启用")
    private Integer status;


    @Schema(description = "所属权限Id列表")
    private List<Long> permissionIds;

    @Schema(description = "所属权限名称列表")
    private List<String> permissionNames;

    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;

    @Schema(description = "更新人")
    private String updatedBy;
}
