package com.yuanchuan.user.api.service;

import com.yuanchuan.common.enums.users.login.LoginType;
import com.yuanchuan.common.enums.users.login.PlatformType;
import com.yuanchuan.user.api.dto.AccountDeviceDTO;

/**
 * 登录日志应用服务接口
 */
public interface LoginLogApplicationService {
    
    /**
     * 记录登录成功日志
     *
     * @param accountId 账户ID
     * @param loginType 登录类型
     * @param platform 平台类型
     * @param deviceInfo 设备信息
     * @param ipAddress IP地址
     * @param userAgent User-Agent
     */
    void recordLoginSuccess(Long accountId, LoginType loginType, PlatformType platform, 
                           AccountDeviceDTO deviceInfo, String ipAddress, String userAgent);
    
    /**
     * 记录登录失败日志
     *
     * @param accountId 账户ID（可能为null）
     * @param loginType 登录类型
     * @param platform 平台类型
     * @param deviceInfo 设备信息
     * @param ipAddress IP地址
     * @param userAgent User-Agent
     */
    void recordLoginFailure(Long accountId, LoginType loginType, PlatformType platform, 
                           AccountDeviceDTO deviceInfo, String ipAddress, String userAgent);
    
    /**
     * 检查账户是否存在异常登录行为
     *
     * @param accountId 账户ID
     * @return 是否存在异常
     */
    boolean checkAbnormalLogin(Long accountId);
}
