package com.yuanchuan.user.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 登录日志DTO
 */
@Data
@Schema(description = "登录日志")
public class LoginLogDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "日志ID", example = "1")
    private Long id;

    @Schema(description = "账户ID", example = "123")
    private Long accountId;

    @Schema(description = "设备ID", example = "device123")
    private String deviceId;

    @Schema(description = "登录类型", example = "PASSWORD")
    private String loginType;

    @Schema(description = "登录状态", example = "SUCCESS")
    private String loginStatus;

    @Schema(description = "IP地址", example = "***********")
    private String ipAddress;

    @Schema(description = "User-Agent", example = "Mozilla/5.0...")
    private String userAgent;

    @Schema(description = "登录位置", example = "台北市")
    private String location;

    @Schema(description = "登录时间", example = "2025-05-23T10:30:00")
    private LocalDateTime loginAt;

    @Schema(description = "来源", example = "CUSTOMER_LOGIN")
    private String source;

    @Schema(description = "创建时间", example = "2025-05-23T10:30:00")
    private LocalDateTime createdAt;
}
