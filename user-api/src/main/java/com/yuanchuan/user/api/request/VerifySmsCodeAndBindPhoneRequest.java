package com.yuanchuan.user.api.request;

import com.yuanchuan.common.enums.users.login.PlatformType;
import com.yuanchuan.user.api.dto.AccountDeviceDTO;
import com.yuanchuan.user.api.dto.AuthUserResponse;
import com.yuanchuan.user.context.enums.RegisterSourceEnum;
import com.yuanchuan.user.context.enums.RegistrationTypeEnum;
import com.yuanchuan.user.context.enums.SmsAndEmailBusinessType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName VerifySmsCodeAndBindPhoneRequest
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/5/15 20:24
 * @Version 1.0
 */

@Data
@Schema(description = "验证手机验证码请求")
public class VerifySmsCodeAndBindPhoneRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    @NotBlank(message = "手機號碼不可為空")
    @Schema(description = "台湾手机号，以09开头，长度10位", example = "**********", required = true)
    private String phone;

    @NotBlank(message = "驗證碼不可為空")
    @Schema(description = "6位数字验证码", example = "123456", required = true)
    private String code;


    @Schema(description = "设备信息对象，包含设备类型、设备ID等信息", example = "{\"deviceUUID\":\"550e8400-e29b-41d4-a716-************\",\"deviceName\":\"iPhone 14 Pro\",\"deviceType\":\"iOS\"}")
    private AccountDeviceDTO deviceInfo;

    @Schema(description = "用户来源枚举")
    private PlatformType source;

    @Schema(description = "注册类型：PHONE_REGISTRATION-手机号注册，EMAIL_BINDING-邮箱注册绑定手机号")
    private RegistrationTypeEnum registrationType = RegistrationTypeEnum.PHONE_REGISTRATION;

    @Schema(description = "邮箱地址", example = "<EMAIL>", required = true)
    private String email;

    @NotNull
    @Schema(description = "短信业务类型：PHONE_REGISTER-手机号注册，PHONE_UNBIND-解绑手机号，MERCHANT_REGISTER-商户注册", example = "1")
    private SmsAndEmailBusinessType businessType = SmsAndEmailBusinessType.PHONE_UNBIND_CODE;

    @Schema(description = "授权用户信息")
    private AuthUserResponse authUser;

    @Schema(description = "临时令牌", example = "token-123456")
    private String temporaryToken;

    @Schema(description = "注册来源", example = "app")
    private RegisterSourceEnum registerSource = RegisterSourceEnum.APP;

    @Deprecated
    @Schema(description = "用户ID（已废弃，请使用secondaryVerificationToken）", example = "123")
    private Long userId;

    @Schema(description = "二次验证令牌（用于替代明文userId）", example = "encrypted_token_here")
    private String secondaryVerificationToken;

    @Schema(description = "平台类型", example = "CUSTOMER")
    private PlatformType platform = PlatformType.CUSTOMER;


}