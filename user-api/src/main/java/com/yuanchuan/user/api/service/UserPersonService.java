package com.yuanchuan.user.api.service;

import com.yuanchuan.user.api.dto.AuthCallbackRequest;
import com.yuanchuan.user.api.request.*;
import com.yuanchuan.user.api.response.LoginResponse;
import com.yuanchuan.user.api.response.VerifySmsCodeAndLoginResponse;

import java.io.IOException;

public interface UserPersonService {


    /**
     * 三方登录
     *
     * @param type 登录类型
     * @return 回调地址
     * @throws IOException IO异常
     */
    String login(String type);


    /**
     * 三方登录回调
     *
     * @param type     类型 google 、line
     * @param callback 回调
     */
    String loginCallback(String type,AuthCallbackRequest callback);

    /**
     * 退出登录
     */
    void logout();

    /**
     * 注销用户
     */
    void deletedUser();

    /**
     * 验证短信验证码并创建商户账户
     *
     * @param request 验证请求
     * @param clientIp 客户端IP
     * @return 登录响应
     */
    LoginResponse verifySmsCodeAndCreateMerchant(VerifySmsCodeAndBindPhoneRequest request, String clientIp);

    /**
     * 验证邮箱验证码并创建商户账户
     *
     * @param request 验证请求
     * @param clientIp 客户端IP
     * @return 登录响应
     */
    LoginResponse verifyEmailCodeAndCreateMerchant(VerifyEmailCodeAndLoginRequest request, String clientIp);



    /**
     * 账号密码登录
     *
     * @param request  登录请求
     * @param clientIp 客户端IP
     * @return 登录响应
     */
    LoginResponse passwordLogin(PasswordLoginRequest request, String clientIp);

    /**
     * 统一登录接口
     *
     * @param request  统一登录请求
     * @return 统一登录响应
     */
    UnifiedLoginResponse unifiedLogin(UnifiedLoginRequest request);
}
