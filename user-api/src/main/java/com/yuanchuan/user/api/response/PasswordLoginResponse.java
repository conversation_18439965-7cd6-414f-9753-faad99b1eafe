package com.yuanchuan.user.api.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 密码登录响应
 */
@Data
@Schema(description = "密码登录响应")
public class PasswordLoginResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "登录响应，登录成功时返回")
    private LoginResponse loginResponse;

    @Schema(description = "是否需要二次验证", example = "false")
    private Boolean needSecondaryVerification = Boolean.FALSE;


}
