package com.yuanchuan.user.api.request;

import com.yuanchuan.authentication.context.enums.BindingPhoneVerifyStatus;
import com.yuanchuan.user.api.response.LoginResponse;
import com.yuanchuan.user.api.response.PhoneExistsResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 统一登录响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "统一登录响应")
public class UnifiedLoginResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "登录响应，登录成功时返回")
    private LoginResponse loginResponse;

    @Schema(description = "流程状态，多步骤流程中返回")
    private BindingPhoneVerifyStatus flowStatus;

    @Schema(description = "手机号已存在响应，当绑定手机号时发现手机号已存在账号时返回")
    private PhoneExistsResponse phoneExistsResponse;

    @Schema(description = "是否需要继续流程", example = "true")
    private Boolean needContinue;

    @Schema(description = "下一步操作提示", example = "请绑定手机号")
    private String nextStepHint;

    @Schema(description = "临时令牌，用于多步骤流程的状态跟踪")
    private String temporaryToken;
}
