package com.yuanchuan.user.api.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName VerifyCodeResponse
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/5/15 20:25
 * @Version 1.0
 */
@Data
@Schema(description = "验证码验证响应")
public  class VerifyCodeResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "验证码是否验证通过", example = "true")
    private Boolean verified;
}