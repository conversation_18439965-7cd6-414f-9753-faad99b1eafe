package com.yuanchuan.user.api.dto;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025-05-09 18:33:18:33
 */
public class AuthCallbackRequest implements Serializable {
    private String code;
    private String auth_code;
    private String state;
    private String authorization_code;
    private String oauth_token;
    private String oauth_verifier;

    public static AuthCallbackBuilder builder() {
        return new AuthCallbackBuilder();
    }

    public String getCode() {
        return this.code;
    }

    public String getAuth_code() {
        return this.auth_code;
    }

    public String getState() {
        return this.state;
    }

    public String getAuthorization_code() {
        return this.authorization_code;
    }

    public String getOauth_token() {
        return this.oauth_token;
    }

    public String getOauth_verifier() {
        return this.oauth_verifier;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setAuth_code(String auth_code) {
        this.auth_code = auth_code;
    }

    public void setState(String state) {
        this.state = state;
    }

    public void setAuthorization_code(String authorization_code) {
        this.authorization_code = authorization_code;
    }

    public void setOauth_token(String oauth_token) {
        this.oauth_token = oauth_token;
    }

    public void setOauth_verifier(String oauth_verifier) {
        this.oauth_verifier = oauth_verifier;
    }

    public AuthCallbackRequest(String code, String auth_code, String state, String authorization_code, String oauth_token, String oauth_verifier) {
        this.code = code;
        this.auth_code = auth_code;
        this.state = state;
        this.authorization_code = authorization_code;
        this.oauth_token = oauth_token;
        this.oauth_verifier = oauth_verifier;
    }

    public AuthCallbackRequest() {
    }

    public static class AuthCallbackBuilder {
        private String code;
        private String auth_code;
        private String state;
        private String authorization_code;
        private String oauth_token;
        private String oauth_verifier;

        AuthCallbackBuilder() {
        }

        public AuthCallbackBuilder code(String code) {
            this.code = code;
            return this;
        }

        public AuthCallbackBuilder auth_code(String auth_code) {
            this.auth_code = auth_code;
            return this;
        }

        public AuthCallbackBuilder state(String state) {
            this.state = state;
            return this;
        }

        public AuthCallbackBuilder authorization_code(String authorization_code) {
            this.authorization_code = authorization_code;
            return this;
        }

        public AuthCallbackBuilder oauth_token(String oauth_token) {
            this.oauth_token = oauth_token;
            return this;
        }

        public AuthCallbackBuilder oauth_verifier(String oauth_verifier) {
            this.oauth_verifier = oauth_verifier;
            return this;
        }

        public AuthCallbackRequest build() {
            return new AuthCallbackRequest(this.code, this.auth_code, this.state, this.authorization_code, this.oauth_token, this.oauth_verifier);
        }

        public String toString() {
            return "AuthCallback.AuthCallbackBuilder(code=" + this.code + ", auth_code=" + this.auth_code + ", state=" + this.state + ", authorization_code=" + this.authorization_code + ", oauth_token=" + this.oauth_token + ", oauth_verifier=" + this.oauth_verifier + ")";
        }
    }
}
