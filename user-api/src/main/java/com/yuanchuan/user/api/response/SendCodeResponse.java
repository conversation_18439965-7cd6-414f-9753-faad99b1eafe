package com.yuanchuan.user.api.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName SendCodeResponse
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/5/15 20:22
 * @Version 1.0
 */
@Data
@Schema(description = "发送验证码响应")
public class SendCodeResponse  implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "验证码过期时间戳（毫秒）", example = "1625097600000")
    private Long expireTime;
}
