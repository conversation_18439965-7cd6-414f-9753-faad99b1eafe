package com.yuanchuan.user.api.service;

import com.yuanchuan.user.api.dto.ContentConfigDTO;
import com.yuanchuan.user.api.dto.ContentConfigDetailDTO;
import com.yuanchuan.user.api.request.ContentConfigModifyRequest;
import com.yuanchuan.user.api.request.ContentConfigQueryRequest;

import java.util.List;

/**
 * 内容配置服务接口
 */
public interface ContentConfigService {

    /**
     * 创建内容配置
     *
     * @param dto      内容配置DTO
     * @param operator 操作人
     * @return 创建后的内容配置ID
     */
    Long createContentConfig(ContentConfigDTO dto, String operator);


    /**
     * 更新内容配置
     *
     * @return 更新后的内容配置
     */
    ContentConfigDetailDTO updateContentConfig(ContentConfigModifyRequest request);

    /**
     * 获取内容配置详情
     *
     * @param id 内容配置ID
     * @return 内容配置详情
     */
    ContentConfigDetailDTO getContentConfigDetail(Long id);



    /**
     * 根据类型获取内容配置列表
     *
     * @param request 内容类型、是否启用
     * @return 内容配置列表
     */
    List<ContentConfigDTO> getContentConfigs(ContentConfigQueryRequest request);

    /**
     * 根据类型获取启用的内容配置列表
     *
     * @param type 内容类型
     * @return 内容配置列表
     */
    List<ContentConfigDTO> getEnabledContentConfigsByType(String type);


}
