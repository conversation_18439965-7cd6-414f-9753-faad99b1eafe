package com.yuanchuan.user.api.request;

import com.yuanchuan.user.api.dto.AccountDeviceDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @ClassName VerifySmsCodeRequest
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/5/13 18:23
 * @Version 1.0
 */
@Data
@Schema(description = "验证手机验证码请求")
public  class VerifySmsCodeRequest {
    @NotBlank(message = "手機號碼不可為空")
    @Schema(description = "台湾手机号，以09开头，长度10位", example = "**********", required = true)
    private String phone;

    @NotBlank(message = "驗證碼不可為空")
    @Schema(description = "6位数字验证码", example = "123456", required = true)
    private String code;

    @Schema(description = "平台类型", example = "CUSTOMER")
    private String platform;

    @Schema(description = "设备信息对象，包含设备类型、设备ID等信息", example = "{\"deviceUUID\":\"550e8400-e29b-41d4-a716-************\",\"deviceName\":\"iPhone 14 Pro\",\"deviceType\":\"iOS\"}")
    private AccountDeviceDTO deviceInfo;
}
