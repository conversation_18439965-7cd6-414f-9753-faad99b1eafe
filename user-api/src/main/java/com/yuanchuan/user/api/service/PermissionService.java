package com.yuanchuan.user.api.service;

import com.yuanchuan.common.response.PageResult;
import com.yuanchuan.user.api.dto.PermissionDTO;
import com.yuanchuan.user.api.dto.PermissionSelectDTO;
import com.yuanchuan.user.api.dto.PermissionTreeDTO;
import com.yuanchuan.user.api.request.PermissionCreateRequest;
import com.yuanchuan.user.api.request.PermissionQueryRequest;
import com.yuanchuan.user.api.request.PermissionUpdateRequest;

import java.util.List;

/**
 * 权限管理服务接口
 */
public interface PermissionService {

    /**
     * 根据账户ID查询用户权限树
     *
     * @param accountId 账户ID
     * @return 权限树列表
     */
    List<PermissionTreeDTO> getUserPermissionTree(Long accountId);

    /**
     * 分页查询权限列表
     *
     * @param request 查询请求
     * @return 权限分页结果
     */
    PageResult<PermissionDTO> queryPermissions(PermissionQueryRequest request);

    /**
     * 获取权限入口下拉列表（类型为MENU的权限）
     *
     * @return 权限入口列表
     */
    List<PermissionSelectDTO> getPermissionEntries();

    /**
     * 创建权限
     *
     * @param request 创建请求
     * @return 权限ID
     */
    Long createPermission(PermissionCreateRequest request);

    /**
     * 更新权限
     *
     * @param request 更新请求
     * @return 是否成功
     */
    Boolean updatePermission(PermissionUpdateRequest request);

    /**
     * 根据ID查询权限详情
     *
     * @param id 权限ID
     * @return 权限详情
     */
    PermissionDTO getPermissionById(Long id);

    /**
     * 删除权限
     *
     * @param id 权限ID
     * @return 是否成功
     */
    Boolean deletePermission(Long id);
}
