package com.yuanchuan.user.api.request;

import com.yuanchuan.user.context.enums.SmsAndEmailBusinessType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName VerifyEmailCodeRequest
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/5/15 20:24
 * @Version 1.0
 */
@Data
@Schema(description = "验证邮箱验证码请求")
public class VerifyEmailCodeRequest implements Serializable {
    private static final long serialVersionUID = 1L;
    @NotBlank(message = "密碼不可為空")
    @Schema(description = "邮箱地址", example = "<EMAIL>", required = true)
    private String email;

    @NotBlank(message = "驗證碼不可為空")
    @Schema(description = "6位数字验证码", example = "123456", required = true)
    private String code;

    @NotNull
    @Schema(description = "短信业务类型：PHONE_REGISTER-手机号注册，PHONE_UNBIND-解绑手机号，MERCHANT_REGISTER-商户注册", example = "1")
    private SmsAndEmailBusinessType businessType = SmsAndEmailBusinessType.PHONE_UNBIND_CODE;
}