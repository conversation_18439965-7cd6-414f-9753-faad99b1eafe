package com.yuanchuan.user.api.request;

import com.yuanchuan.user.context.enums.ContentTitleEnum;
import com.yuanchuan.user.context.enums.ContentType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @ClassName ContentConfigUpdateRequest
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/5/15 13:47
 * @Version 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "内容配置修改request")
public class ContentConfigModifyRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull(message = "欲修改的主鍵 ID 不可為空")
    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "内容类型", required = true)
    private ContentType type;

    @Schema(description = "标题枚举类型，帮助类型的内容可以不提供此字段")
    private ContentTitleEnum titleEnum;

    @Schema(description = "标题", required = true)
    private String title;

    @Schema(description = "内容", required = true)
    private String content;

    @Schema(description = "父级内容ID，用于构建层级关系，如果为null，表示是一级内容")
    private Long parentId;

    @Schema(description = "跳转URL，用于帮助内容跳转到特定页面，如果为null，表示不需要跳转")
    private String redirectUrl;

    @Schema(description = "排序")
    private Integer sortOrder;

    @Schema(description = "是否启用")
    private Boolean isEnabled;

    @Schema(description = "删除标识0删除，1正常")
    private Boolean active;

    private String operator;



}
