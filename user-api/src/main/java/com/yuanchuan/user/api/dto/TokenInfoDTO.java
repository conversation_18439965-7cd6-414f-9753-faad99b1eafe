package com.yuanchuan.user.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName TokenInfo
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/5/16 10:14
 * @Version 1.0
 */
@Data
@Schema(description = "Token信息")
public class TokenInfoDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "访问Token", example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...")
    private String accessToken;

    @Schema(description = "刷新Token", example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...")
    private String refreshToken;

    @Schema(description = "Token过期时间", example = "1625097600000")
    private String expireTime;
}