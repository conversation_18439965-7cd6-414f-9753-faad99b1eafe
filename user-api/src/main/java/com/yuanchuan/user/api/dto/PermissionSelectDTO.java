package com.yuanchuan.user.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 权限入口下拉选择DTO
 */
@Data
@Schema(description = "权限入口下拉选择DTO")
public class PermissionSelectDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "权限ID")
    private Long id;

    @Schema(description = "权限名称")
    private String permissionName;

    @Schema(description = "权限编码")
    private String permissionCode;
}
