package com.yuanchuan.user.api.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 角色更新请求
 */
@Data
@Schema(description = "角色更新请求")
public class RoleUpdateRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    @NotNull(message = "角色ID不可為空")
    @Schema(description = "角色ID", required = true)
    private Long id;

    @Size(max = 50, message = "角色名稱不能超過 50 個字元")
    @Schema(description = "角色名称", required = true)
    private String roleName;

    @Size(max = 50, message = "角色編碼不能超過 50 個字元")
    @Schema(description = "角色编码", required = true)
    private String roleCode;

    @Size(max = 200, message = "角色描述不能超過 200 個字元")
    @Schema(description = "角色描述")
    private String description;

    @Schema(description = "角色状态：0-禁用，1-启用")
    private Integer status;

    @Schema(description = "权限ID列表")
    private List<Long> permissionIds;
}
