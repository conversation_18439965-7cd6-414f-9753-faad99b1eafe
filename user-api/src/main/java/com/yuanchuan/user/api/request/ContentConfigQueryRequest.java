package com.yuanchuan.user.api.request;

import com.yuanchuan.user.context.enums.ContentTitleEnum;
import com.yuanchuan.user.context.enums.ContentType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @ClassName ContentConfigUpdateRequest
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/5/15 13:47
 * @Version 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "内容配置查询request")
public class ContentConfigQueryRequest implements Serializable {

    private static final long serialVersionUID = 1L;


    @Schema(description = "不传默认全部,内容类型", required = true)
    private ContentType type;

    @Schema(description = "不传默认全部,是否启用")
    private Boolean isEnabled;




}
