package com.yuanchuan.user.api.request;

import com.yuanchuan.user.context.enums.SmsAndEmailBusinessType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * @ClassName SendSmsCodeRequest
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/5/15 20:23
 * @Version 1.0
 */
@Data
@Schema(description = "发送手机验证码请求")
public  class SendSmsCodeRequest {
    @NotBlank(message = "手機號碼不可為空")
    @Schema(description = "台湾手机号，以09开头，长度10位", example = "0912345678", required = true)
    private String phone;

    @NotNull
    @Schema(description = "短信业务类型：PHONE_REGISTER-手机号注册，PHONE_UNBIND-解绑手机号，MERCHANT_REGISTER-商户注册", example = "2")
    private SmsAndEmailBusinessType businessType = SmsAndEmailBusinessType.PHONE_UNBIND_CODE;
}