package com.yuanchuan.user.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 运营账号信息DTO
 */
@Data
@Schema(description = "运营账号信息")
public class OperationAccountDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "账号ID")
    private Long id;

    @Schema(description = "姓名")
    private String name;

    @Schema(description = "员工编码")
    private String employeeCode;

    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "角色列表")
    private List<RoleInfo> roles;

    @Schema(description = "账户状态：1-正常，2-锁定，3-禁用")
    private Integer accountStatus;

    @Schema(description = "账户状态描述")
    private String accountStatusDesc;

    @Schema(description = "更新人")
    private String updatedBy;

    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;

    /**
     * 角色信息
     */
    @Data
    @Schema(description = "角色信息")
    public static class RoleInfo implements Serializable {
        private static final long serialVersionUID = 1L;

        @Schema(description = "角色ID")
        private Long roleId;

        @Schema(description = "角色名称")
        private String roleName;

        @Schema(description = "角色状态：0-禁用，1-启用")
        private Integer status;
    }
}
