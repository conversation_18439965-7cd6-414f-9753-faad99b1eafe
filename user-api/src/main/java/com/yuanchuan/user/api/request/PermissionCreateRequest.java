package com.yuanchuan.user.api.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import java.io.Serializable;

/**
 * 权限创建请求
 */
@Data
@Schema(description = "权限创建请求")
public class PermissionCreateRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    @NotBlank(message = "權限名稱不能為空")
    @Size(max = 40, message = "權限名稱不能超過 40 個字元")
    @Schema(description = "权限名称", required = true)
    private String permissionName;

    @Size(max = 200, message = "權限說明不能超過 200 個字元")
    @Schema(description = "权限说明")
    private String description;

    @NotBlank(message = "權限 Key 值不能為空")
    @Size(max = 200, message = "權限 Key 值不能超過 200 個字元")
    @Schema(description = "权限Key值", required = true)
    private String permissionCode;

    @Schema(description = "权限类型：MENU-菜单, PAGE-页面, BUTTON-按钮, API-接口")
    private String type;

    @Schema(description = "父权限ID")
    private Long parentId;

    @Schema(description = "页面路径或API路径")
    private String url;

    @Schema(description = "HTTP方法")
    private String httpMethod;

    @Schema(description = "菜单图标")
    private String icon;

    @Schema(description = "组件路径")
    private String componentPath;

    @Schema(description = "排序字段")
    private Integer orderNum;

    @Schema(description = "是否可见：0-隐藏，1-显示")
    private Integer isVisible;
}
