package com.yuanchuan.user.api.request;

import com.yuanchuan.user.api.dto.AccountDeviceDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @ClassName EmailPhoneRegisterRequest
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/5/15 20:26
 * @Version 1.0
 */
@Data
@Schema(description = "邮箱+手机号注册请求")
public  class EmailPhoneRegisterRequest {
    @NotBlank(message = "電子郵件不可為空")
    @Email(message = "邮箱格式不正确")
    @Schema(description = "邮箱地址", example = "<EMAIL>", required = true)
    private String email;

    @NotBlank(message = "手機號碼不可為空")
    @Schema(description = "台湾手机号，以09开头，长度10位", example = "**********", required = true)
    private String phone;

    @NotBlank(message = "密碼不可為空")
    @Schema(description = "密码，至少8位，包含字母和数字", example = "Password123", required = true)
    private String password;

    @Schema(description = "用户昵称，如不提供则使用默认昵称", example = "美食客_1234")
    private String nickname;

    @NotBlank(message = "平台不可為空")
    @Schema(description = "平台类型，如iOS、Android、Web等", example = "iOS", required = true)
    private String platform;

    @Schema(description = "是否沿用已有账号（当手机号已绑定其他账号时）", example = "true")
    private Boolean reuseAccount;

    @Schema(description = "设备信息对象，包含设备类型、设备ID等信息")
    private AccountDeviceDTO deviceInfo;
}