package com.yuanchuan.user.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 权限DTO
 */
@Data
@Schema(description = "权限DTO")
public class PermissionDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "权限ID")
    private Long permissionId;

    @Schema(description = "权限入口")
    private String permissionMenu;

    @Schema(description = "权限名称")
    private String permissionName;

    @Schema(description = "权限编码")
    private String permissionCode;

    @Schema(description = "权限描述")
    private String description;

    @Schema(description = "关联角色列表")
    private String roleNames;

    @Schema(description = "关联角色ID列表")
    private List<Long> roleIds;

    @Schema(description = "权限状态：0-禁用，1-启用")
    private Integer status;

    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;

    @Schema(description = "更新人")
    private String updatedBy;
}
