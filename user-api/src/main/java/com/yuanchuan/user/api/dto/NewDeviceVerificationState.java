package com.yuanchuan.user.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 新设备验证状态
 */
@Data
public class NewDeviceVerificationState implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 设备ID
     */
    private String deviceId;

    /**
     * 原始登录方式：PHONE, EMAIL
     */
    private String originalLoginMethod;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 过期时间
     */
    private LocalDateTime expireTime;

    /**
     * 目标邮箱（二次验证时的邮箱地址）
     */
    private String targetEmail;

    /**
     * 目标手机号（二次验证时的手机号）
     */
    private String targetPhone;

    /**
     * 原始IP地址（用于二次验证时的IP一致性检查）
     */
    private String originalIpAddress;
}
