package com.yuanchuan.user.api.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025-06-05 19:34:19:34
 */
@Data
@Schema(description = "商户创建角色请求")
public class MerchantCreateRoleRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    @NotNull(message = "商户ID不能為空")
    @Schema(description = "商户ID", example = "1", required = true)
    private Long merchantId;

    @NotBlank(message = "商戶名稱不能為空")
    @Schema(description = "商户名称不能為空", example = "我爱吃火锅", required = true)
    private String merchantName;

    @NotBlank(message = "組織類型不能為空")
    @Schema(description = "组织类型", example = "merchant", required = true)
    private String orgType;

    @NotNull(message = "賬戶ID不能為空")
    @Schema(description = "账户ID", example = "2", required = true)
    private Long businessAccountId;
}
