package com.yuanchuan.user.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;

/**
 * 用户个人信息修改DTO
 */
@Data
public class UserProfileUpdateDTO {

    /**
     * 昵称修改请求
     */
    @Data
    @Schema(description = "昵称修改请求")
    public static class NicknameUpdateRequest implements Serializable {
        private static final long serialVersionUID = 1L;

        @Size(min = 1, max = 20, message = "昵称长度必须在1-20个字符之间")
        @Pattern(regexp = "^[\\u4e00-\\u9fa5a-zA-Z0-9]+$", message = "暱稱只能包含中文、英文與數字")
        @Schema(description = "用户昵称", example = "美食客_1234", required = true)
        @NotBlank(message = "暱稱不可為空")
        private String nickname;
    }

    /**
     * 头像修改请求
     */
    @Data
    @Schema(description = "头像修改请求")
    public static class AvatarUpdateRequest implements Serializable {
        private static final long serialVersionUID = 1L;

        @Schema(description = "头像链接", required = true)
        @NotBlank(message = "頭像不可為空")
        private String avatrUrl;

        @Schema(description = "图片类型", example = "jpg", required = true)
        private String imageType;
    }

    /**
     * 性别修改请求
     */
    @Data
    @Schema(description = "性别修改请求")
    public static class GenderUpdateRequest implements Serializable {
        private static final long serialVersionUID = 1L;

        @Schema(description = "性别：MALE-男，FEMALE-女，UNKNOWN-保密", example = "MALE", required = true)
        private String gender;
    }

    /**
     * 生日修改请求
     */
    @Data
    @Schema(description = "生日修改请求")
    public static class BirthdayUpdateRequest implements Serializable {
        private static final long serialVersionUID = 1L;

        @Schema(description = "生日", example = "1990-01-01", required = true)
        private Date birthday;
    }

    /**
     * 常居地修改请求
     */
    @Data
    @Schema(description = "常居地修改请求")
    public static class AddressUpdateRequest implements Serializable {
        private static final long serialVersionUID = 1L;

        @Schema(description = "常居地地址", example = "台北市信义区", required = true)
        private String address;

        @Schema(description = "省编码", example = "710000")
        private String provinceCode;

        @Schema(description = "市编码", example = "710100")
        private String cityCode;

        @Schema(description = "区编码", example = "710101")
        private String regionCode;

        @Schema(description = "省名称", example = "台湾省")
        private String provinceName;

        @Schema(description = "市名称", example = "台北市")
        private String cityName;

        @Schema(description = "区名称", example = "信义区")
        private String regionName;

        @Schema(description = "经度", example = "121.5654")
        private String longitude;

        @Schema(description = "纬度", example = "25.0330")
        private String latitude;
    }

    /**
     * 通用响应
     */
    @Data
    @Schema(description = "个人信息修改响应")
    public static class UpdateResponse implements Serializable {
        private static final long serialVersionUID = 1L;

        @Schema(description = "是否成功", example = "true")
        private boolean success;

        @Schema(description = "消息", example = "修改成功")
        private String message;
    }
}
