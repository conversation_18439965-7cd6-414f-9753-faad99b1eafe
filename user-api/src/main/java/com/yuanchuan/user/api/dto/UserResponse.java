package com.yuanchuan.user.api.dto;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * 用户响应DTO
 */
@Data
public class UserResponse implements Serializable {
    private static final long serialVersionUID = 1L;
    
    /**
     * 用户ID
     */
    private Long id;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 邮箱
     */
    private String email;
    
    /**
     * 手机号
     */
    private String phone;
    
    /**
     * 昵称
     */
    private String nickname;

    /**
     * 性别
     */
    private String gender;
    
    /**
     * 头像
     */
    private String avatar;

    /**
     * 常居地
     */
    private String address;
    
    /**
     * 状态
     */
    private String userStatus;

    /**
     * 生日
     */
    private Date birthday;
    
    /**
     * 创建时间
     */
    private Long createTime;
    
    /**
     * 更新时间
     */
    private Long updateTime;


    /**
     * 密码是否设置
     */
    private boolean pwdFlag;
}
