package com.yuanchuan.user.api.dto;

import com.yuanchuan.common.enums.users.login.PlatformType;
import com.yuanchuan.user.context.enums.SmsAndEmailBusinessType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

import java.io.Serializable;

/**
 * 用户账号更新DTO
 */
public class UserAccountUpdateDTO {

    /**
     * 手机号验证请求
     */
    @Data
    @Schema(description = "手机号验证请求")
    public static class PhoneVerifyRequest implements Serializable {
        private static final long serialVersionUID = 1L;

        @NotBlank(message = "手機號碼不可為空")
        @Pattern(regexp = "^09\\d{8}$", message = "請輸入正確的台灣手機號碼")
        @Schema(description = "手机号", example = "**********", required = true)
        private String phone;
    }

    /**
     * 换绑手机号请求
     */
    @Data
    @Schema(description = "换绑手机号请求")
    public static class PhoneUpdateRequest implements Serializable {
        private static final long serialVersionUID = 1L;

        @NotBlank(message = "新手機號碼不可為空")
        @Pattern(regexp = "^09\\d{8}$", message = "請輸入正確的台灣手機號碼")
        @Schema(description = "新手机号", example = "**********", required = true)
        private String newPhone;

        @NotBlank(message = "驗證碼不可為空。")
        @Schema(description = "验证码", example = "123456", required = true)
        private String code;

        @Schema(description = "设备ID", example = "device-uuid-123")
        private String deviceId;
        @NotNull
        @Schema(description = "短信业务类型：PHONE_REGISTER-手机号注册，PHONE_UNBIND-解绑手机号，MERCHANT_REGISTER-商户注册", example = "1")
        private SmsAndEmailBusinessType businessType = SmsAndEmailBusinessType.PHONE_UNBIND_CODE;

        @Schema(description = "平台类型", example = "CUSTOMER")
        private PlatformType source;
    }

    /**
     * 绑定邮箱请求
     */
    @Data
    @Schema(description = "绑定邮箱请求")
    public static class EmailBindRequest implements Serializable {
        private static final long serialVersionUID = 1L;

        @NotBlank(message = "電子郵件不可為空")
        @Email(message = "邮箱格式不正确")
        @Schema(description = "邮箱", example = "<EMAIL>", required = true)
        private String email;

        @Schema(description = "验证码", example = "123456")
        private String code;
        @NotNull
        @Schema(description = "短信业务类型：PHONE_REGISTER-手机号注册，PHONE_UNBIND-解绑手机号，MERCHANT_REGISTER-商户注册", example = "1")
        private SmsAndEmailBusinessType businessType = SmsAndEmailBusinessType.PHONE_UNBIND_CODE;

        @Schema(description = "平台类型", example = "CUSTOMER")
        private PlatformType source;
    }

    /**
     * 换绑邮箱请求 - 原邮箱验证
     */
    @Data
    @Schema(description = "换绑邮箱请求 - 原邮箱验证")
    public static class EmailVerifyRequest implements Serializable {
        private static final long serialVersionUID = 1L;

        @NotBlank(message = "原電子郵件不可為空")
        @Email(message = "邮箱格式不正确")
        @Schema(description = "原邮箱", example = "<EMAIL>", required = true)
        private String email;
    }


    /**
     * 设置密码请求
     */
    @Data
    @Schema(description = "设置密码请求")
    public static class PasswordSetRequest implements Serializable {
        private static final long serialVersionUID = 1L;

        @NotBlank(message = "密碼不可為空")
        @Schema(description = "新密码", example = "Password123!", required = true)
        private String newPassword;
    }

    /**
     * 修改密码请求
     */
    @Data
    @Schema(description = "修改密码请求")
    public static class PasswordUpdateRequest implements Serializable {
        private static final long serialVersionUID = 1L;

        @NotBlank(message = "原密碼不可為空")
        @Schema(description = "原密码", example = "OldPassword123!", required = true)
        private String oldPassword;
    }

}
