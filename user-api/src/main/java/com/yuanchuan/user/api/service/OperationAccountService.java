package com.yuanchuan.user.api.service;

import com.yuanchuan.common.response.PageResult;
import com.yuanchuan.user.api.dto.OperationAccountDTO;
import com.yuanchuan.user.api.request.OperationAccountCreateRequest;
import com.yuanchuan.user.api.request.OperationAccountQueryRequest;
import com.yuanchuan.user.api.request.OperationAccountUpdateRequest;

/**
 * 运营账号管理服务接口
 */
public interface OperationAccountService {

    /**
     * 分页查询运营账号列表
     *
     * @param request 查询请求
     * @return 分页结果
     */
    PageResult<OperationAccountDTO> queryOperationAccounts(OperationAccountQueryRequest request);

    /**
     * 新增运营账号
     *
     * @param request 新增请求
     * @return 账号ID
     */
    Long createOperationAccount(OperationAccountCreateRequest request);

    /**
     * 修改运营账号
     *
     * @param request 修改请求
     * @return 是否成功
     */
    Boolean updateOperationAccount(OperationAccountUpdateRequest request);

    /**
     * 根据ID查询运营账号详情
     *
     * @param id 账号ID
     * @return 账号详情
     */
    OperationAccountDTO getOperationAccountById(Long id);
}
