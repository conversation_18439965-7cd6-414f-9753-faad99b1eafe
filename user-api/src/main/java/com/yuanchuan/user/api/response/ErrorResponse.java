package com.yuanchuan.user.api.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName ErrorResponse
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/5/15 20:28
 * @Version 1.0
 */
@Data
@Schema(description = "错误响应")
public  class ErrorResponse  implements Serializable {
    private static final long serialVersionUID = 1L;


    @Schema(description = "错误信息", example = "验证码不正确")
    private String message;

    public ErrorResponse(String message) {
        this.message = message;
    }
}