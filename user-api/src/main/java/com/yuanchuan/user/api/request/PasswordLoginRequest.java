package com.yuanchuan.user.api.request;

import com.yuanchuan.common.enums.users.login.PlatformType;
import com.yuanchuan.user.api.dto.AccountDeviceDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serializable;

/**
 * 账号密码登录请求
 */
@Data
@Schema(description = "账号密码登录请求")
public class PasswordLoginRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    @NotBlank(message = "帳號不可為空")
    @Schema(description = "账号（手机号或邮箱）", example = "**********", required = true)
    private String account;

    @NotBlank(message = "密碼不可為空")
    @Schema(description = "密码", example = "password123", required = true)
    private String password;

    @Schema(description = "设备信息对象，包含设备类型、设备ID等信息", example = "{\"deviceUUID\":\"550e8400-e29b-41d4-a716-************\",\"deviceName\":\"iPhone 14 Pro\",\"deviceType\":\"iOS\"}")
    private AccountDeviceDTO deviceInfo;

    @Schema(description = "用户来源枚举")
    private PlatformType platform = PlatformType.CUSTOMER;

}
