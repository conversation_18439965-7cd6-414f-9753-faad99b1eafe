package com.yuanchuan.user.api.dto;

import com.yuanchuan.common.enums.users.login.PlatformType;
import com.yuanchuan.user.context.enums.RegistrationTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName VerifySmsCodeAndLoginRequest
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/5/9 19:15
 * @Version 1.0
 */
@Data
public class VerifySmsCodeAndLoginRequest implements Serializable {
    private static final long serialVersionUID = 1L;


    @NotBlank(message = "手機號碼不可為空")
    @Schema(description = "台湾手机号，以09开头，长度10位", example = "**********", required = true)
    private String phone;

    @NotBlank(message = "驗證碼不可為空")
    @Schema(description = "6位数字验证码", example = "123456", required = true)
    private String code;

    @Schema(description = "平台类型", example = "CUSTOMER")
    private PlatformType source;

    @Schema(description = "设备信息对象，包含设备类型、设备ID等信息")
    private AccountDeviceDTO deviceInfo;


    @Schema(description = "邮箱，当registrationType为EMAIL_BINDING时必填", example = "<EMAIL>")
    private String email;


    @Schema(description = "注册类型：PHONE_REGISTRATION-手机号注册，EMAIL_BINDING-邮箱注册绑定手机号")
    private RegistrationTypeEnum registrationType = RegistrationTypeEnum.PHONE_REGISTRATION;

}
