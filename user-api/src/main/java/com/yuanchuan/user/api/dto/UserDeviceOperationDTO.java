package com.yuanchuan.user.api.dto;

import java.io.Serializable;

import lombok.Data;

/**
 * 用户设备操作DTO
 */
@Data
public class UserDeviceOperationDTO implements Serializable {
    private static final long serialVersionUID = 1L;



    /**
     * 主键id
     */
    private Long accountDeviceId;
    /**
     * 设备ID
     */
    private String deviceId;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 新设备名称（用于重命名操作）
     */
    private String newDeviceName;
}
