package com.yuanchuan.user.api.dto;

import com.yuanchuan.authentication.context.enums.BindingPhoneVerifyStatus;
import lombok.Data;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025-05-09 19:26:19:26
 */
@Data
public class AuthUserResponse implements Serializable {

    // 三方用户唯一标识
   private String sub;

   // 三方用户昵称
   private String nickname;

   // 三方用户头像
   private String avatar;

   // 三方用户性别
   private String gender;

   // asstoken
   private String accessToken;
   private String refreshToken;
   private String tokenType;
   private String scope;

   // 失效时间
   private Integer expiresAt;

   // refreshToken 长期有效表示	0-长期有效
   private Integer refreshExpiresAt;

   // 平台类型
   private String platform;

   /**
    * 拼接为 URL 查询参数格式：yourapp://oauth/bind?sub=xxx&nickname=xxx...
    */
   public String toBindUrl(String scheme) {
      StringBuilder sb = new StringBuilder(scheme);
      sb.append("?")
              .append("sub=").append(encode(sub))
              .append("&flag=").append("loginBindPhone")
              .append("&flowStatus=").append(BindingPhoneVerifyStatus.R_THIRD_PARTY_AUTHORIZED.getCode());
      return sb.toString();
   }

   private String encode(String value) {
      try {
         return value == null ? "" : URLEncoder.encode(value, StandardCharsets.UTF_8.toString());
      } catch (UnsupportedEncodingException e) {
         throw new RuntimeException("URL encoding failed", e);
      }
   }
}
