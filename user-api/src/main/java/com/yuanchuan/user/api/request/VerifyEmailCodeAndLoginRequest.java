package com.yuanchuan.user.api.request;

import com.yuanchuan.common.enums.users.login.PlatformType;
import com.yuanchuan.user.api.dto.AccountDeviceDTO;
import com.yuanchuan.user.context.enums.SmsAndEmailBusinessType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName VerifyEmailCodeAndLoginRequest
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/5/15 20:25
 * @Version 1.0
 */
@Data
@Schema(description = "验证邮箱验证码并登录请求")
public  class VerifyEmailCodeAndLoginRequest implements Serializable {
    private static final long serialVersionUID = 1L;
    @NotBlank(message = "電子郵件不可為空")
    @Schema(description = "邮箱地址", example = "<EMAIL>", required = true)
    private String email;

    @NotBlank(message = "驗證碼不可為空")
    @Schema(description = "6位数字验证码", example = "123456", required = true)
    private String code;

    @Schema(description = "平台类型", example = "CUSTOMER")
    private PlatformType platform;

    @Schema(description = "设备信息对象，包含设备类型、设备ID等信息", example = "{\"deviceUUID\":\"550e8400-e29b-41d4-a716-************\",\"deviceName\":\"iPhone 14 Pro\",\"deviceType\":\"iOS\"}")
    private AccountDeviceDTO deviceInfo;

    @NotNull
    @Schema(description = "短信业务类型：PHONE_REGISTER-手机号注册，PHONE_UNBIND-解绑手机号，MERCHANT_REGISTER-商户注册", example = "1")
    private SmsAndEmailBusinessType businessType = SmsAndEmailBusinessType.PHONE_UNBIND_CODE;

    @Deprecated
    @Schema(description = "用户ID（已废弃，请使用secondaryVerificationToken）", example = "123")
    private Long userId;

    @Schema(description = "二次验证令牌（用于替代明文userId）", example = "encrypted_token_here")
    private String secondaryVerificationToken;

    private String userAgent;

}