package com.yuanchuan.user.api.response;

import com.yuanchuan.user.api.dto.TokenInfoDTO;
import com.yuanchuan.user.api.dto.UserInfoDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName RegisterResponse
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/5/15 20:26
 * @Version 1.0
 */
@Data
@Schema(description = "注册响应")
public  class RegisterResponse  implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "用户ID", example = "123456")
    private Long userId;

    @Schema(description = "Token信息")
    private TokenInfoDTO token;

    @Schema(description = "用户信息")
    private UserInfoDTO userInfo;


}