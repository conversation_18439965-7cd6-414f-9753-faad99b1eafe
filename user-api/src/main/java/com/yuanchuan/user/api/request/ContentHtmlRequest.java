package com.yuanchuan.user.api.request;

import com.yuanchuan.user.context.enums.ContentType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 获取内容HTML的请求参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "获取内容HTML的请求参数")
public class ContentHtmlRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull(message = "內容類型不可為空")
    @Schema(description = "内容类型，如AGREEMENT（协议）、HELP（帮助）等", required = true)
    private ContentType type;

    @Schema(description = "内容标题，如果不提供，则返回该类型的第一个内容")
    private String title;
}
