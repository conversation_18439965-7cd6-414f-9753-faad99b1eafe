package com.yuanchuan.user.api.service;

import com.yuanchuan.user.api.dto.AddressLookupDTO;
import com.yuanchuan.user.api.dto.ThirdPartyBindingDTO;
import com.yuanchuan.user.api.dto.UserAccountUpdateDTO;
import com.yuanchuan.user.api.dto.UserProfileUpdateDTO;
import com.yuanchuan.user.api.dto.UserResponse;

import java.util.List;

/**
 * 用户信息相关
 * <AUTHOR>
 * @date 2025-05-14 16:38:16:38
 */
public interface UserService {

    /**
     * 获取用户个人信息
     ** @return 用户信息
     */
    UserResponse getUserProfile();

    /**
     * 更新用户昵称
     *
     * @param request 昵称更新请求
     * @return 更新结果
     */
    void updateNickname(UserProfileUpdateDTO.NicknameUpdateRequest request);

    /**
     * 更新用户头像
     * @param request 头像更新请求
     * @return 更新结果
     */
    void updateAvatar(UserProfileUpdateDTO.AvatarUpdateRequest request);

    /**
     * 更新用户性别
     *
     * @param request 性别更新请求
     * @return 更新结果
     */
    void updateGender(UserProfileUpdateDTO.GenderUpdateRequest request);

    /**
     * 更新用户生日
     *
     * @param request 生日更新请求
     * @return 更新结果
     */
    void updateBirthday(UserProfileUpdateDTO.BirthdayUpdateRequest request);

    /**
     * 更新用户常居地
     *
     * @param request 常居地更新请求
     * @return 更新结果
     */
    void updateAddress(UserProfileUpdateDTO.AddressUpdateRequest request);

    /**
     * 验证手机号是否是当前账号绑定的手机号
     *
     * @param request 手机号验证请求
     * @return 验证结果
     */
    void verifyPhone(UserAccountUpdateDTO.PhoneVerifyRequest request);

    /**
     * 换绑手机号
     *
     * @param request 换绑手机号请求
     * @return 换绑结果
     */
    void updatePhone(UserAccountUpdateDTO.PhoneUpdateRequest request);

    /**
     * 绑定邮箱
     *
     * @param request 绑定邮箱请求
     * @return 绑定结果
     */
    void bindEmail(UserAccountUpdateDTO.EmailBindRequest request);

    /**
     * 验证原邮箱（换绑邮箱第一步）
     *
     * @param request 原邮箱验证请求
     * @return 验证结果
     */
    void verifyOriginalEmail(UserAccountUpdateDTO.EmailVerifyRequest request);

    /**
     * 设置密码
     *
     * @param request 设置密码请求
     * @return 设置结果
     */
    void setPassword(UserAccountUpdateDTO.PasswordSetRequest request);

    /**
     * 验证原密码
     *
     * @param request 修改密码请求
     * @return 修改结果
     */
    void verifyOriginalPassword(UserAccountUpdateDTO.PasswordUpdateRequest request);

    /**
     * 获取用户绑定的第三方账号列表
     *
     * @return 第三方绑定列表
     */
    ThirdPartyBindingDTO.ThirdPartyBindingListResponse getThirdPartyBindingList();

    /**
     * 解绑第三方账号
     *
     * @param request 解绑请求
     * @return 解绑结果
     */
    void unbindThirdParty(ThirdPartyBindingDTO.UnbindThirdPartyRequest request);

    /**
     * 绑定第三方账号
     *
     * @param request 绑定请求
     * @return 绑定结果
     */
    void bindThirdParty(ThirdPartyBindingDTO.BindThirdPartyRequest request);

    /**
     * 根据坐标获取地址信息
     *
     * @param request 坐标请求
     * @return 地址信息
     */
    AddressLookupDTO.AddressResponse getAddressByCoordinate(AddressLookupDTO.CoordinateRequest request);

    /**
     * 根据名称搜索地址信息
     *
     * @param request 名称搜索请求
     * @return 地址信息
     */
    List<AddressLookupDTO.AddressResponse> getAddressByName(AddressLookupDTO.NameRequest request);

    /**
     * 验证原邮箱（换绑邮箱第一步）
     *
     * @param request 原邮箱验证请求
     * @return 验证结果
     */
    void verifyNewEmail(UserAccountUpdateDTO.EmailVerifyRequest request);

}
