package com.yuanchuan.user.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 地址查询DTO
 */
public class AddressLookupDTO {

    /**
     * 坐标请求
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "坐标请求")
    public static class CoordinateRequest implements Serializable {
        private static final long serialVersionUID = 1L;

        @NotNull(message = "經度不可為空")
        @Schema(description = "经度", example = "121.5654", required = true)
        private String longitude;

        @NotNull(message = "緯度不可為空")
        @Schema(description = "纬度", example = "25.0330", required = true)
        private String latitude;
    }

    /**
     * 名称搜索请求
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "名称搜索请求")
    public static class NameRequest implements Serializable {
        private static final long serialVersionUID = 1L;

        @NotBlank(message = "搜尋名稱不可為空")
        @Schema(description = "搜索名称", example = "台北市信义区", required = true)
        private String name;
    }

    /**
     * 地址响应
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "地址响应")
    public static class AddressResponse implements Serializable {
        private static final long serialVersionUID = 1L;

        @Schema(description = "常居地地址", example = "台北市信义区", required = true)
        private String address;
        
        @Schema(description = "省编码", example = "710000")
        private String provinceCode;
        
        @Schema(description = "市编码", example = "710100")
        private String cityCode;
        
        @Schema(description = "区编码", example = "710101")
        private String regionCode;
        
        @Schema(description = "省名称", example = "台湾省")
        private String provinceName;
        
        @Schema(description = "市名称", example = "台北市")
        private String cityName;
        
        @Schema(description = "区名称", example = "信义区")
        private String regionName;
        
        @Schema(description = "经度", example = "121.5654")
        private String longitude;
        
        @Schema(description = "纬度", example = "25.0330")
        private String latitude;
    }
}
