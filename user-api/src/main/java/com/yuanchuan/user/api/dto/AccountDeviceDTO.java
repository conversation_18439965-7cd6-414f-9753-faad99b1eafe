package com.yuanchuan.user.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "设备信息对象")
public class AccountDeviceDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    @Schema(description = "设备唯一标识（客户端生成的UUID）", example = "550e8400-e29b-41d4-a716-************")
    private String deviceId;

    @Schema(description = "设备名称或型号", example = "iPhone 14 Pro")
    private String deviceName;

    @Schema(description = "设备类型，如iOS、Android、Web等", example = "iOS")
    private String deviceType;

    @Schema(description = "操作系统版本例如iOS 17.1.2", example = "iOS 17.1.2")
    private String osVersion;

    @Schema(description = "客户端 App 的版本号", example = "iOS 17.1.2")
    private String appVersion;

    @Schema(description = "设备状态，0-未激活，1-正常，2-已禁用", example = "1")
    private Integer status;

    @Schema(description = "关联的用户ID", example = "456")
    private Long accountId;

    @Schema(description = "登录IP地址", example = "***********")
    private String ipAddress;

    @Schema(description = "标识浏览器或客户端信息")
    private String userAgent;

    @Schema(description = "登录时间", example = "2023-01-01T12:00:00")
    private LocalDateTime loginTime;



}