package com.yuanchuan.user.api.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName VerifySmsCodeAndLoginResponse
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/5/16 10:05
 * @Version 1.0
 */
@Data
@Schema(description = "登录响应")
public class VerifySmsCodeAndLoginResponse implements Serializable {
    private static final long serialVersionUID = 1L;


    @Schema(description = "登录响应", example = "123")
    private LoginResponse loginResponse;

    @Schema(description = "登录手机号已绑定响应", example = "123")
    private PhoneExistsResponse phoneExistsResponse;

    @Schema(description = "是否需要二次验证", example = "false")
    private Boolean needSecondaryVerification = Boolean.FALSE;








}
