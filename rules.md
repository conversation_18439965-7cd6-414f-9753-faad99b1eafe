# 你是一个资深的后端开发程序员，熟练掌握java开发语言和各种框架,
# 开发时严格按照规范和设计，代码风格统一，注释清晰，代码可读性强。
# 擅长各种设计模式的应用。
# 沿用现有代码框架和项目结构，禁止乱加包或目录，按现有设计进行编码。
# mybatis-plus 使用。  
# 尽量完整的创建文件，避免漏写。
# 文件按定义的属性进行读写操作，不能写set或get不存在的属性。

### 模块结构

```
1. shop-common：通用模块
│   └── com.yuanchuan.shop.common
│       ├── util/        # 通用工具类（日期/字符串/加密/文件处理）
│       ├── constant/    # 系统常量定义
│       ├── exception/   # 统一异常处理
│       └── config/      # 公共配置类

2. shop-domain：领域模块
│   └── com.yuanchuan.shop.domain
│       ├── model/       # 领域模型
│       ├── service/     # 领域服务
│       ├── repository/  # 仓储接口
│       └── event/       # 领域事件

3. shop-infrastructure：基础设施模块
│   └── com.yuanchuan.shop.infrastructure
│       ├── persistence/        # 持久化实现
│       │   ├── po/             # 数据库实体
│       │   ├── mapper/         # MyBatis mapper接口
│       │   └── repository/     # 仓储实现
│       │   └── assembler/      # PO与领域对象转换
│       ├── integration/ # 外部服务集成
│       ├── cache/      # 缓存实现
│       └── mq/         # 消息队列实现

4. shop-api：接口模块
│   └── com.yuanchuan.shop.api
│       ├── dto/         # 数据传输对象
│       ├── req/         # 数据传输请求
│       ├── service/     # 提供的rpc接口
│   ├── interface.controllor/  # api接口


5. shop-application：应用服务模块
│   └── com.yuanchuan.shop.application
│       └── assembler/  # DTO转换器
│       ├── service/    # 应用服务实现
│       ├── event/      # 事件处理器
│       └── task/       # 定时任务