# User 服务手動部署操作指南

## 步驟1：載入配置變數
```bash
source deploy-config.sh
show_config
```

## 步驟2：檢查必要工具
```bash
check_tools
```

## 步驟3：檢查Azure登錄狀態
```bash
check_azure_login
```

## 步驟4：檢查K8s集群現有服務
```bash
# 檢查現有deployment 管理Pod的控制器
kubectl get deployment user
# 刪除deployment（會自動刪除pods）
kubectl delete deployment user --ignore-not-found=true

# 檢查現有service 為Pod提供穩定網絡訪問的抽象層
kubectl get svc user
# 刪除service
kubectl delete svc user --ignore-not-found=true

# 檢查現有configmap
kubectl get configmap user-config
# 刪除configmap
kubectl delete configmap user-config --ignore-not-found=true

# 檢查現有pods Kubernetes中最小的部署單元
kubectl get pods -l app=user
# 删除
kubectl delete pods -l app=user
kubectl delete pods -l app=user --force --grace-period=0

# 確認清理完成
kubectl get all -l app=user
```

## 步驟5：構建Java應用
```bash
# 清理並構建
mvn clean package -DskipTests

# 檢查構建結果
ls -la user-bootstrap/target/*.jar
```

## 步驟6：構建Docker映像
```bash
# 使用ACR遠程構建
az acr build --registry $ACR_NAME --image $IMAGE_NAME:$IMAGE_TAG .

# 查看ACR中所有的倉庫(查看，非必须)
az acr repository list --name $ACR_NAME --output table

# 檢查映像是否成功推送
az acr repository show-tags --name $ACR_NAME --repository $IMAGE_NAME --output table
```

## 步驟7：連接K8s集群
```bash
# kubectl cluster-info 有信息后，后续可跳过
kubectl cluster-info

# 獲取集群憑證
az aks get-credentials --resource-group $RESOURCE_GROUP --name $AKS_CLUSTER --overwrite-existing

# 檢查集群連接
kubectl cluster-info

# 檢查節點狀態
kubectl get nodes
```

## 步驟8：應用ConfigMap（已有配置文件）
```bash
# 直接應用已有的ConfigMap配置
kubectl apply -f k8s/configmap.yaml

# 查看ConfigMap內容
kubectl get configmap user-config -o yaml
```

## 步驟9：應用Service（已有配置文件）
```bash
# 直接應用已有的Service配置
kubectl apply -f k8s/service.yaml

# 檢查Service狀態
kubectl get svc user
```

## 步驟10：更新並應用Deployment
```bash
# 更新deployment.yaml中的鏡像標籤
sed -i.bak "s|image: frchacrdev.azurecr.io/user:.*|image: $ACR_LOGIN_SERVER/$IMAGE_NAME:$IMAGE_TAG|" k8s/deployment.yaml

# 應用deployment配置
kubectl apply -f k8s/deployment.yaml

# 檢查deployment狀態
kubectl get deployment user
```

## 步驟11：檢查部署狀態
```bash
# 檢查所有資源
kubectl get all -l app=user

# 檢查deployment狀態
kubectl get deployment user

# 檢查service狀態（顯示所有端口）
kubectl get svc user -o wide

# 檢查configmap
kubectl get configmap user-config
```

## 步驟12：檢查Pod狀態
```bash
# 檢查pod狀態
kubectl get pods -l app=user -o wide

# 檢查pod詳細信息
kubectl describe pods -l app=user

# 檢查pod事件
kubectl get events --field-selector involvedObject.kind=Pod --sort-by='.lastTimestamp'
```

## 步驟13：檢查應用日誌
```bash
# 查看所有pod日誌
kubectl logs -l app=user --tail=50

# 實時查看日誌
kubectl logs -l app=user -f

# 查看特定pod日誌（如果需要）
kubectl logs deployment/user
```

## 步驟14：檢查服務健康狀態
```bash
# 等待pod ready
kubectl wait --for=condition=ready pod -l app=user --timeout=300s

# 檢查端點
kubectl get endpoints user

# 測試Dubbo QoS健康檢查
kubectl port-forward service/user 22222:22222 &
sleep 5
echo "檢查Dubbo QoS狀態："
curl -s http://localhost:22222/status
echo -e "\n檢查Dubbo就緒狀態："
curl -s http://localhost:22222/ready
echo -e "\n檢查Dubbo健康詳情："
curl -s http://localhost:22222/health
pkill -f "kubectl port-forward"
```

## 步驟15：基本連通性驗證
```bash
# 簡單驗證服務DNS和基本連通性
kubectl run test-user --image=busybox --rm -it --restart=Never -- nslookup user

# 直接檢查服務端點狀態
kubectl get endpoints user

# 快速健康檢查
USER_POD=$(kubectl get pods -l app=user -o jsonpath='{.items[0].metadata.name}')
if [ ! -z "$USER_POD" ]; then
  kubectl exec $USER_POD -- curl -s http://localhost:22222/status
fi
```

## 步驟16：檢查Dubbo註冊狀態
```bash
# 檢查是否能訪問QoS端點
kubectl exec deployment/user -- curl -s http://localhost:22222/ls

# 檢查服務提供者狀態
kubectl exec deployment/user -- curl -s http://localhost:22222/ps

# 檢查消費者狀態
kubectl exec deployment/user -- curl -s http://localhost:22222/cd

# **确认健康检查状态
kubectl describe pods -l app=user | grep -A3 "Conditions:"
```

## 步驟17：最終驗證
```bash
# 檢查所有資源狀態
echo "=== Deployment Status ==="
kubectl get deployment user

echo "=== Pod Status ==="
kubectl get pods -l app=user

echo "=== Service Status (所有端口) ==="
kubectl get svc user -o wide

echo "=== ConfigMap Status ==="
kubectl get configmap user-config

echo "=== 端口信息 ==="
echo "- Spring Boot HTTP: 8082 (API端口)"
echo "- Dubbo RPC: 20801 (主要服務端口)"
echo "- Dubbo QoS: 22222 (健康檢查端口)"

echo "=== Recent Events ==="
kubectl get events --sort-by='.lastTimestamp' | tail -10

echo "=== 服務健康檢查 ==="
USER_POD=$(kubectl get pods -l app=user -o jsonpath='{.items[0].metadata.name}')
if [ ! -z "$USER_POD" ]; then
  echo "檢查Pod: $USER_POD"
  kubectl exec $USER_POD -- curl -s http://localhost:22222/status || echo "QoS檢查失敗"
fi
```

## 步驟18：清理測試資源（可選）
```bash
# 如果需要重新部署，執行清理
# kubectl delete -f k8s/
# 或使用腳本清理
# kubectl delete deployment user
# kubectl delete service user
# kubectl delete configmap user-config
```

## 快速部署命令（整合版）
```bash
# 一鍵部署（適用於重複部署）
source deploy-config.sh && deploy_app

# 或者分步驟執行
source deploy-config.sh
check_tools && check_azure_login
mvn clean package -DskipTests
az acr build --registry $ACR_NAME --image $IMAGE_NAME:$IMAGE_TAG .
az aks get-credentials --resource-group $RESOURCE_GROUP --name $AKS_CLUSTER --overwrite-existing
sed -i.bak "s|image: frchacrdev.azurecr.io/user:.*|image: $ACR_LOGIN_SERVER/$IMAGE_NAME:$IMAGE_TAG|" k8s/deployment.yaml
kubectl apply -f k8s/
kubectl rollout status deployment/user
kubectl get all -l app=user
```

## 故障排除
```bash
# 如果Pod啟動失敗
kubectl describe pod -l app=user
kubectl logs -l app=user --previous

# 如果健康檢查失敗
kubectl exec deployment/user -- curl -v http://localhost:22222/status

# 如果Dubbo註冊失敗
kubectl exec deployment/user -- curl -s http://localhost:22222/ls

# 重新載入配置（如果ConfigMap修改）
kubectl patch configmap user-config --patch '
data:
  application.yml: |
    # 在這裡放置新的配置內容
'
kubectl rollout restart deployment user
```

## 注意事項
- 用户服务提供HTTP API和Dubbo RPC两种访问方式
- HTTP API端口：8082，主要用于用户管理相关的RESTful API
- Dubbo RPC端口：20801，供其他微服务进行RPC调用
- 健康檢查使用Dubbo QoS端點（端口22222）
- 服務類型為ClusterIP，主要供集群內部訪問
- 確保Zookeeper服務可用，因為Dubbo需要註冊中心
- 支持第三方登录（Google、Apple、LINE等）
- 包含登录日志记录功能，支持异常登录检测
- 使用JWT进行用户认证（需要authentication服务支持）