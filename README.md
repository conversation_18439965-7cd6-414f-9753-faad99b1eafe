# User 用戶管理微服務項目 - 用戶設備管理
包路徑：com.yuanchuan.user

### 服務基本信息

- **服務名稱**: user-service
- **服務類型**: 純 Dubbo RPC 服務
- **主要端口**:
  - HTTP 端口: `8082` (Spring Boot Actuator 健康檢查)
  - Dubbo RPC 端口: `20801` (服務間通信)

### 核心功能模塊

- **用戶管理**: 用戶註冊、登錄、個人信息管理
- **設備管理**: 用戶設備綁定、解綁、狀態管理
- **第三方認證**: Google、Apple、LINE 等第三方登錄
- **內容配置**: 協議、幫助文檔等內容管理
- **驗證服務**: 短信、郵箱驗證碼服務

### 健康檢查配置

#### 1. Dubbo QoS 健康檢查（推薦）
項目已啟用Dubbo QoS功能，可以使用更精確的健康檢查：

```yaml
# K8s健康檢查配置
livenessProbe:
  httpGet:
    path: /live
    port: 22222
  initialDelaySeconds: 60
  periodSeconds: 10
  timeoutSeconds: 5
  failureThreshold: 3

readinessProbe:
  httpGet:
    path: /ready  
    port: 22222
  initialDelaySeconds: 30
  periodSeconds: 5
  timeoutSeconds: 3
  failureThreshold: 3
```

#### 2. Spring Boot Actuator 健康檢查（備選方案）
```yaml
# 使用 Spring Boot Actuator 健康檢查
livenessProbe:
  httpGet:
    path: /actuator/health/liveness
    port: 8082
  initialDelaySeconds: 60
  periodSeconds: 10
  timeoutSeconds: 5
  failureThreshold: 3

readinessProbe:
  httpGet:
    path: /actuator/health/readiness
    port: 8082
  initialDelaySeconds: 30
  periodSeconds: 5
  timeoutSeconds: 3
  failureThreshold: 3
```

#### 3. TCP 端口檢查（備選方案）
```yaml
# K8s Deployment 中的健康檢查配置
livenessProbe:
  tcpSocket:
    port: 20801
  initialDelaySeconds: 60
  periodSeconds: 10
  timeoutSeconds: 5
  failureThreshold: 3

readinessProbe:
  tcpSocket:
    port: 8082
  initialDelaySeconds: 30
  periodSeconds: 5
  timeoutSeconds: 3
  failureThreshold: 3
```

### Service 配置

```yaml
apiVersion: v1
kind: Service
metadata:
  name: user-service
  namespace: yuanchuan
  labels:
    app: user-service
spec:
  selector:
    app: user-service
  ports:
  - name: http
    port: 8082
    targetPort: 8082
    protocol: TCP
  - name: dubbo
    port: 20801
    targetPort: 20801
    protocol: TCP
  - name: qos
    port: 22222
    targetPort: 22222
    protocol: TCP
  type: ClusterIP
```

### Deployment 配置示例

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: user-service
  namespace: yuanchuan
spec:
  replicas: 1
  selector:
    matchLabels:
      app: user-service
  template:
    metadata:
      labels:
        app: user-service
    spec:
      containers:
      - name: user-service
        image: user-service:latest
        ports:
        - containerPort: 8082
          name: http
        - containerPort: 20801
          name: dubbo
        - containerPort: 22222
          name: qos
        # Dubbo QoS健康檢查（推薦）
        livenessProbe:
          httpGet:
            path: /live
            port: 22222
          initialDelaySeconds: 60
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: 22222
          initialDelaySeconds: 30
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "prod"
        - name: SERVER_PORT
          value: "8082"
        - name: DUBBO_PORT
          value: "20801"
        - name: DUBBO_QOS_PORT
          value: "22222"
        - name: ZOOKEEPER_ADDRESS
          value: "zookeeper-service:2181"
        - name: MYSQL_HOST
          valueFrom:
            secretKeyRef:
              name: mysql-secret
              key: host
        - name: MYSQL_USERNAME
          valueFrom:
            secretKeyRef:
              name: mysql-secret
              key: username
        - name: MYSQL_PASSWORD
          valueFrom:
            secretKeyRef:
              name: mysql-secret
              key: password
        - name: REDIS_HOST
          valueFrom:
            secretKeyRef:
              name: redis-secret
              key: host
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: redis-secret
              key: password
```

### 服務發現和負載均衡

#### Dubbo 服務發現
- **Zookeeper註冊中心**: Dubbo服務自動註冊到Zookeeper
- **服務消費**: 其他微服務通過Dubbo客戶端調用
- **負載均衡**: Dubbo內置負載均衡策略

### 主要 Dubbo 服務接口

#### 用戶管理服務 (UserService)
- `getUserProfile()` - 獲取用戶信息
- `updateUserProfile(UserProfileUpdateDTO)` - 更新用戶信息
- `bindEmail(EmailBindRequest)` - 綁定郵箱
- `setPassword(PasswordSetRequest)` - 設置密碼
- `getThirdPartyBindingList()` - 獲取第三方綁定列表
- `getAddressByCoordinate(CoordinateRequest)` - 根據坐標獲取地址

#### 用戶認證服務 (UserPersonService)
- `login(String type)` - 第三方登錄
- `loginCallback(String type, AuthCallbackRequest)` - 登錄回調
- `passwordLogin(PasswordLoginRequest, String clientIp)` - 密碼登錄
- `unifiedLogin(UnifiedLoginRequest, String clientIp)` - 統一登錄
- `logout()` - 退出登錄
- `deletedUser()` - 註銷用戶

#### 設備管理服務 (UserDeviceService)
- `registerDevice(AccountDeviceDTO)` - 註冊設備
- `getDeviceList(UserDeviceOperationDTO)` - 獲取設備列表
- `unbindDevice(UserDeviceOperationDTO)` - 解綁設備
- `updateDeviceStatus(UserDeviceOperationDTO)` - 更新設備狀態

#### 驗證碼服務 (VerificationService)
- `sendSmsCode(String phone, SmsAndEmailBusinessType)` - 發送短信驗證碼
- `sendEmailCode(String email, SmsAndEmailBusinessType)` - 發送郵箱驗證碼
- `verifySmsCode(String phone, String code, SmsAndEmailBusinessType)` - 驗證短信驗證碼
- `verifyEmailCode(String email, String code, SmsAndEmailBusinessType)` - 驗證郵箱驗證碼

#### 內容配置服務 (ContentConfigService)
- `createContentConfig(ContentConfigDTO, String operator)` - 創建內容配置
- `updateContentConfig(ContentConfigModifyRequest)` - 更新內容配置
- `getContentConfigDetail(Long id)` - 獲取內容配置詳情
- `getContentConfigs(ContentConfigQueryRequest)` - 獲取內容配置列表
- `getEnabledContentConfigsByType(String type)` - 獲取啟用的內容配置

### 監控和觀測

#### 1. Dubbo Admin 監控
```yaml
# 可以部署Dubbo Admin來監控服務狀態
apiVersion: apps/v1
kind: Deployment
metadata:
  name: dubbo-admin
spec:
  # ... dubbo-admin配置
```

#### 2. Spring Boot Actuator 監控
```yaml
# 暴露監控端點
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
```

#### 3. 日誌收集
```yaml
# 通過sidecar或日誌收集器收集應用日誌
volumes:
- name: logs
  emptyDir: {}
volumeMounts:
- name: logs
  mountPath: /app/logs
```

### QoS 配置和運維

#### QoS 啟用配置

**當前狀態**: 項目配置中沒有顯式配置 QoS，但 Dubbo 3.x 默認啟用 QoS 功能。

**建議配置**: 為了確保配置的明確性和一致性，建議在配置文件中顯式添加以下配置：

```yaml
# application.yml 中啟用 QoS
dubbo:
  application:
    qos-enable: true
    qos-port: 22222
    qos-accept-foreign-ip: false  # 安全考慮，僅允許本地訪問
```

**默認行為**: 
- Dubbo 3.x 默認啟用 QoS，端口為 22222
- 默認只允許本地訪問（qos-accept-foreign-ip: false）
- 如需禁用 QoS，需要顯式設置 `qos-enable: false`

#### QoS 運維命令

啟用QoS後，可以通過以下命令進行運維操作：

```bash
# 在Pod內執行以下命令

# 查看服務健康狀態
curl http://localhost:22222/live
curl http://localhost:22222/ready

# 查看所有服務
curl http://localhost:22222/ls

# 查看服務詳情
curl http://localhost:22222/ls -l

# 上線/下線服務
curl http://localhost:22222/online com.yuanchuan.user.api.service.UserService
curl http://localhost:22222/offline com.yuanchuan.user.api.service.UserService

# 查看配置信息
curl http://localhost:22222/getConfig

# 查看服務提供者
curl http://localhost:22222/ps

# 查看服務消費者
curl http://localhost:22222/cd
```

### 注意事項

1. **純 Dubbo 服務**: 本服務是純Dubbo RPC服務，不提供HTTP接口
2. **健康檢查**: 推薦使用Dubbo QoS健康檢查，更精確可靠
3. **服務間通信**: 通過Dubbo RPC進行，不走HTTP
4. **監控**: 可通過Dubbo Admin、Spring Boot Actuator、QoS端點、日誌等方式監控
5. **擴縮容**: 基於CPU/內存使用率或自定義指標進行HPA配置
6. **QoS功能**: 已啟用，端口22222，僅允許本地訪問（安全考慮）
7. **第三方依賴**: 依賴Zookeeper、MySQL、Redis等外部服務
8. **安全配置**: 敏感信息通過Secret管理
9. **無需Ingress**: 純Dubbo服務不需要配置Ingress，通過Zookeeper進行服務發現

### 依賴服務

- **Zookeeper**: 服務註冊與發現
- **MySQL**: 用戶數據持久化
- **Redis**: 緩存和會話管理
- **Authentication Service**: 認證服務依賴

### HPA 自動擴縮容配置

```yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: user-service-hpa
  namespace: yuanchuan
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: user-service
  minReplicas: 1
  maxReplicas: 1
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

