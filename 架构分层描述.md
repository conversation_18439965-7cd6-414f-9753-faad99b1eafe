各层的职责和特点：

1. 用户服务API层 ( `user-api` )
- 对外暴露的接口定义
- DTO对象定义
- 面向其他微服务的Facade接口
- 主要包含UserController和相关DTO
2. 应用层 ( `user-application` )
- 负责业务流程的编排
- 不包含业务规则
- 调用领域层完成业务逻辑
- 包含UserApplicationService等应用服务
3. 领域层 ( `user-domain` )
- 核心业务逻辑所在
- 包含领域模型（User、UserDevice等）
- 领域服务（ `UserDomainService` ）
- 定义仓储接口（ `UserRepository` ）
4. 基础设施层 ( `user-infrastructure` )
- 技术细节实现
- 实现领域层定义的接口
- 包含数据库访问、缓存、消息等
- 包含PO对象和Mapper
5. 启动层 ( `user-bootstrap` )
- 应用程序入口
- 配置管理
- 依赖装配
- 包含Application启动类和配置文件
特点：

1. 依赖关系清晰：从外到内依次是 api -> application -> domain <- infrastructure
2. 领域层独立：不依赖其他层，保持业务逻辑的纯粹性
3. 关注点分离：每层职责单一，便于维护和扩展
4. 遵循DDD原则：聚合根、值对象、领域服务等概念的运用
这种分层架构的优势：

1. 业务逻辑集中在领域层，便于维护
2. 技术实现和业务逻辑解耦
3. 便于单元测试
4. 支持微服务架构的演进