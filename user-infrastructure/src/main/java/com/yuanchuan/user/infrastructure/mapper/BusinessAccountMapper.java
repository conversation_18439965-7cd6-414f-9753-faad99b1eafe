package com.yuanchuan.user.infrastructure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuanchuan.user.infrastructure.po.BusinessAccountPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * 商户账户Mapper接口
 */
@Mapper
public interface BusinessAccountMapper extends BaseMapper<BusinessAccountPO> {
    
    /**
     * 更新账户状态
     *
     * @param id 商户账户ID
     * @param accountStatus 账户状态
     * @return 影响行数
     */
    @Update("UPDATE business_account SET account_status = #{accountStatus}, updated_at = NOW() WHERE id = #{id}")
    int updateAccountStatus(@Param("id") Long id, @Param("accountStatus") Integer accountStatus);
    
    /**
     * 更新密码
     *
     * @param id 商户账户ID
     * @param password 密码哈希
     * @return 影响行数
     */
    @Update("UPDATE business_account SET password = #{password}, updated_at = NOW() WHERE id = #{id}")
    int updatePassword(@Param("id") Long id, @Param("password") String password);
    
    /**
     * 更新账户名称
     *
     * @param id 商户账户ID
     * @param accountName 账户名称
     * @return 影响行数
     */
    @Update("UPDATE business_account SET account_name = #{accountName}, updated_at = NOW() WHERE id = #{id}")
    int updateAccountName(@Param("id") Long id, @Param("accountName") String accountName);
}
