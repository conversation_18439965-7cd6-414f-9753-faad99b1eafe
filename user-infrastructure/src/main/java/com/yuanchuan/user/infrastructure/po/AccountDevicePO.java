package com.yuanchuan.user.infrastructure.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 账户设备表持久化对象
 */
@Data
@TableName("account_device")
public class AccountDevicePO {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 关联账户ID，外键指向 customer_account 表
     */
    private Long accountId;
    
    /**
     * 设备唯一标识，客户端生成的 UUID 或设备指纹
     */
    private String deviceId;
    
    /**
     * 设备类型 ios, android
     */
    private String deviceType;
    
    /**
     * 设备名称或型号，例如 iPhone 14 Pro
     */
    private String deviceName;
    
    /**
     * 操作系统版本，例如 iOS 17.1.2
     */
    private String osVersion;
    
    /**
     * 客户端 App 的版本号
     */
    private String appVersion;
    
    /**
     * 登录时的 IP 地址（支持 IPv6）
     */
    private String ipAddress;
    
    /**
     * User-Agent 字符串，标识浏览器或客户端信息
     */
    private String userAgent;
    
    /**
     * 该设备是否已通过安全验证（如短信验证码）
     */
    private Boolean isVerified;
    
    /**
     * 该设备是否当前活跃（用于设备登录控制或踢出）
     */
    private Boolean isActive;
    
    /**
     * 最近一次登录时间
     */
    private LocalDateTime lastLoginAt;
    
    /**
     * 来源：CUSTOMER_LOGIN-C端用户登录 , SHOP_LOGIN-商户登录
     */
    private String source;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 是否启用 0-禁用(删除) 1-启用
     */
    private Boolean active;
}
