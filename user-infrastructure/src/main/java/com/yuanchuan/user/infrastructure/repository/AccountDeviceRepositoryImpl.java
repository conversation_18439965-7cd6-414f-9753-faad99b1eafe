package com.yuanchuan.user.infrastructure.repository;

import com.yuanchuan.user.domain.model.AccountDevice;
import com.yuanchuan.user.domain.repository.AccountDeviceRepository;
import com.yuanchuan.user.infrastructure.mapper.AccountDeviceMapper;
import com.yuanchuan.user.infrastructure.po.AccountDevicePO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 账户设备仓储实现类
 */
@Repository
public class AccountDeviceRepositoryImpl implements AccountDeviceRepository {
    
    @Autowired
    private AccountDeviceMapper accountDeviceMapper;
    
    @Override
    public AccountDevice save(AccountDevice device) {
        AccountDevicePO po = toPO(device);
        if (po.getId() == null) {
            accountDeviceMapper.insert(po);
        } else {
            accountDeviceMapper.updateById(po);
        }
        return toEntity(po);
    }
    
    @Override
    public Optional<AccountDevice> findById(Long id) {
        AccountDevicePO po = accountDeviceMapper.selectById(id);
        return Optional.ofNullable(po).map(this::toEntity);
    }
    
    @Override
    public List<AccountDevice> findByAccountId(Long accountId) {
        List<AccountDevicePO> poList = accountDeviceMapper.selectByAccountId(accountId);
        return poList.stream().map(this::toEntity).collect(Collectors.toList());
    }
    
    @Override
    public Optional<AccountDevice> findByAccountIdAndDeviceId(Long accountId, String deviceId) {
        AccountDevicePO po = accountDeviceMapper.selectByAccountIdAndDeviceId(accountId, deviceId);
        return Optional.ofNullable(po).map(this::toEntity);
    }
    
    @Override
    public boolean updateActiveStatus(Long id, Boolean isActive) {
        return accountDeviceMapper.updateActiveStatus(id, isActive) > 0;
    }
    
    @Override
    public boolean updateVerifiedStatus(Long id, Boolean isVerified) {
        return accountDeviceMapper.updateVerifiedStatus(id, isVerified) > 0;
    }
    
    @Override
    public int countActiveDevicesByAccountId(Long accountId) {
        return accountDeviceMapper.countActiveDevicesByAccountId(accountId);
    }
    
    @Override
    public boolean deleteById(Long id) {
        return accountDeviceMapper.deleteById(id) > 0;
    }

    @Override
    public boolean logicDeleteByIds(List<Long> deviceIds) {
        return accountDeviceMapper.logicDeleteByIds(deviceIds) > 0;
    }


    /**
     * 将领域对象转换为持久化对象
     *
     * @param entity 领域对象
     * @return 持久化对象
     */
    private AccountDevicePO toPO(AccountDevice entity) {
        if (entity == null) {
            return null;
        }
        AccountDevicePO po = new AccountDevicePO();
        BeanUtils.copyProperties(entity, po);
        return po;
    }
    
    /**
     * 将持久化对象转换为领域对象
     *
     * @param po 持久化对象
     * @return 领域对象
     */
    private AccountDevice toEntity(AccountDevicePO po) {
        if (po == null) {
            return null;
        }
        AccountDevice entity = new AccountDevice();
        BeanUtils.copyProperties(po, entity);
        return entity;
    }
}
