package com.yuanchuan.user.infrastructure.repository;

import com.yuanchuan.user.domain.model.UserChangeLogs;
import com.yuanchuan.user.domain.repository.UserChangeLogsRepository;
import com.yuanchuan.user.infrastructure.mapper.UserChangeLogsMapper;
import com.yuanchuan.user.infrastructure.po.UserChangeLogsPO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025-05-22 17:42:17:42
 */
@Service
public class UserChangeLogsRepositoryImpl implements UserChangeLogsRepository {

    @Autowired
    private UserChangeLogsMapper userChangeLogsMapper;

    @Override
    public boolean insert(UserChangeLogs userChangeLogs) {
        UserChangeLogsPO userChangeLogsPO = toPO(userChangeLogs);
        return userChangeLogsMapper.insert(  userChangeLogsPO) > 0;
    }



    public UserChangeLogsPO toPO(UserChangeLogs userChangeLogs) {
        UserChangeLogsPO po = new UserChangeLogsPO();
        BeanUtils.copyProperties(userChangeLogs, po);
        return po;
    }
}
