package com.yuanchuan.user.infrastructure.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 账户登录日志表持久化对象
 */
@Data
@TableName("login_log")
public class LoginLogPO {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 关联账户ID
     */
    private Long accountId;
    
    /**
     * 设备唯一标识，用于标识来源设备
     */
    private String deviceId;
    
    /**
     * 登录方式 PASSWORD SMS_CODE OAUTH OTHER
     */
    private String loginType;
    
    /**
     * 登录状态 SUCCESS FAIL
     */
    private String loginStatus;
    
    /**
     * 登录时使用的 IP 地址
     */
    private String ipAddress;
    
    /**
     * User-Agent 信息
     */
    private String userAgent;
    
    /**
     * 可选：登录地理位置（通过 IP 解析）
     */
    private String location;
    
    /**
     * 登录时间
     */
    private LocalDateTime loginAt;
    
    /**
     * 来源：CUSTOMER_LOGIN-C端用户登录 , SHOP_LOGIN-商户登录
     */
    private String source;
    
    /**
     * 创建人
     */
    private String createdBy;
    
    /**
     * 更新人
     */
    private String updatedBy;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 是否启用 0-禁用(删除) 1-启用
     */
    private Boolean active;
}
