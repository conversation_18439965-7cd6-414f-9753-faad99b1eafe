package com.yuanchuan.user.infrastructure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuanchuan.user.infrastructure.po.CustomerAccountPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 用户(自然人)账号表Mapper接口
 */
@Mapper
public interface CustomerAccountMapper extends BaseMapper<CustomerAccountPO> {
    /**
     * 根据用户ID查询账号
     *
     * @param userPersonId 用户ID
     * @return 账号信息
     */
    CustomerAccountPO selectByUserPersonId(@Param("userPersonId") Long userPersonId);

    /**
     * 更新用户状态
     *
     * @param id 账号ID
     * @param userStatus 用户状态
     * @return 影响行数
     */
    int updateUserStatus(@Param("id") Long id, @Param("userStatus") String userStatus);

    /**
     * 更新密码
     *
     * @param id 账号ID
     * @param password 密码哈希
     * @return 影响行数
     */
    int updatePassword(@Param("id") Long id, @Param("password") String password);

    /**
     * 更新用户昵称
     *
     * @param id 账号ID
     * @param nickName 昵称
     * @return 影响行数
     */
    int updateNickName(@Param("id") Long id, @Param("nickName") String nickName);

    /**
     * 更新用户头像
     *
     * @param id 账号ID
     * @param avatar 头像URL
     * @return 影响行数
     */
    int updateAvatar(@Param("id") Long id, @Param("avatar") String avatar);

    /**
     * 根据主键删除(逻辑）
     * @param id
     * @return
     */
    int logicDeleteById(@Param("id") Long id);

    /**
     * 更新用户性别
     *
     * @param id 账号ID
     * @param gender 性别
     * @return 影响行数
     */
    int updateGender(@Param("id") Long id, @Param("gender") String gender);

    /**
     * 更新用户生日
     *
     * @param id 账号ID
     * @param birthday 生日
     * @return 影响行数
     */
    int updateBirthday(@Param("id") Long id, @Param("birthday") LocalDate birthday);

    /**
     * 更新用户常居地
     *
     * @param id 账号ID
     * @param address 常居地
     * @param provinceCode 省编码
     * @param cityCode 市编码
     * @param regionCode 区编码
     * @param provinceName 省名称
     * @param cityName 市名称
     * @param regionName 区名称
     * @param longitude 经度
     * @param latitude 纬度
     * @return 影响行数
     */
    int updateAddress(@Param("id") Long id, @Param("address") String address,
                    @Param("provinceCode") String provinceCode, @Param("cityCode") String cityCode,
                    @Param("regionCode") String regionCode, @Param("provinceName") String provinceName,
                    @Param("cityName") String cityName, @Param("regionName") String regionName,
                    @Param("longitude") BigDecimal longitude, @Param("latitude") BigDecimal latitude);
}
