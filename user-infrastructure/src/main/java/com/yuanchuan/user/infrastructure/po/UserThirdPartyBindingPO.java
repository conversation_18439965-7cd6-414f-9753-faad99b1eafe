package com.yuanchuan.user.infrastructure.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yuanchuan.user.domain.model.UserThirdPartyBinding;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.util.Date;

/**
 * 用户第三方账号绑定表
 * @TableName user_third_party_binding
 */
@Data
@TableName("user_third_party_binding")
public class UserThirdPartyBindingPO {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID，关联user表
     */
    private Long customerAccountId;

    /**
     * 第三方平台：APPLE_ID-苹果ID ,FACE_BOOK-脸书 , GOOGLE-谷歌,LINK-link等
     */
    private String platform;

    /**
     * 第三方平台用户唯一标识
     */
    private String externalUserId;

    /**
     * 第三方平台用户昵称
     */
    private String nickName;

    /**
     * 第三方平台用户头像URL
     */
    private String avatar;

    /**
     * 第三方平台用户性别：性别：UNKNOWN-未知，MALE-男，FEMALE-女
     */
    private String gender;

    /**
     * 绑定状态：BINDGING-绑定，UNBIND-解绑
     */
    private String bindingStatus;

    /**
     * 绑定时间
     */
    private Date bindTime;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 更新时间
     */
    private Date updatedAt;

    /**
     * 是否启用 0-禁用(删除) 1-启用
     */
    private Integer active;

    public UserThirdPartyBinding convertToUserThirdPartyBinding() {
        UserThirdPartyBinding userThirdPartyBinding = new UserThirdPartyBinding();
        BeanUtils.copyProperties(this, userThirdPartyBinding);
        return userThirdPartyBinding;
    }
}