package com.yuanchuan.user.infrastructure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuanchuan.user.infrastructure.po.UserPersonPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 自然人Mapper接口
 */
@Mapper
public interface UserPersonMapper extends BaseMapper<UserPersonPO> {


    /**
     * 根据手机号和来源查询
     * 查询用户
     *
     * @param phone 手机号
     * @return 用户信息
     */
    UserPersonPO selectByPhoneAndSource(@Param("phone") String phone,@Param("source") String source);

    /**
     * 根据邮箱和来源查询
     * 查询用户
     *
     * @param email 邮箱
     * @return 用户信息
     */
    UserPersonPO selectByEmailAndSource(@Param("email") String email,@Param("source") String source);



    /**
     * 更新手机号
     *
     * @param id 用户ID
     * @param phone 手机号
     * @return 影响行数
     */
    int updatePhone(@Param("id") Long id, @Param("phone") String phone);
    
    /**
     * 更新邮箱
     *
     * @param id 用户ID
     * @param email 邮箱
     * @return 影响行数
     */
    int updateEmail(@Param("id") Long id, @Param("email") String email);

    int logicDeleteById(@Param("id") Long id);
}
