package com.yuanchuan.user.infrastructure.assembler;

import com.yuanchuan.user.context.enums.ContentType;
import com.yuanchuan.user.domain.model.ContentConfig;
import com.yuanchuan.user.infrastructure.po.ContentConfigPO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

/**
 * 内容配置PO转换器
 */
@Component
public class ContentConfigPOAssembler {

    /**
     * 将领域对象转换为持久化对象
     *
     * @param entity 领域对象
     * @return 持久化对象
     */
    public ContentConfigPO toPO(ContentConfig entity) {
        if (entity == null) {
            return null;
        }
        ContentConfigPO po = new ContentConfigPO();
        BeanUtils.copyProperties(entity, po);
        if (entity.getType() != null) {
            po.setType(entity.getType().name());
        }
        return po;
    }

    /**
     * 将持久化对象转换为领域对象
     *
     * @param po 持久化对象
     * @return 领域对象
     */
    public ContentConfig toEntity(ContentConfigPO po) {
        if (po == null) {
            return null;
        }
        ContentConfig entity = new ContentConfig();
        BeanUtils.copyProperties(po, entity);
        if (po.getType() != null) {
            entity.setType(ContentType.valueOf(po.getType()));
        }
        return entity;
    }

}
