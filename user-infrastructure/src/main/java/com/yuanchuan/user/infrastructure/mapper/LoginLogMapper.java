package com.yuanchuan.user.infrastructure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuanchuan.user.infrastructure.po.LoginLogPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 账户登录日志表Mapper接口
 */
@Mapper
public interface LoginLogMapper extends BaseMapper<LoginLogPO> {
    /**
     * 根据账户ID查询登录日志
     *
     * @param accountId 账户ID
     * @param limit 限制条数
     * @return 登录日志列表
     */
    List<LoginLogPO> selectByAccountId(@Param("accountId") Long accountId, @Param("limit") Integer limit);
    
    /**
     * 查询账户最近一次登录记录
     *
     * @param accountId 账户ID
     * @return 登录日志
     */
    LoginLogPO selectLastLoginByAccountId(@Param("accountId") Long accountId);
    
    /**
     * 查询指定时间段内的登录失败记录
     *
     * @param accountId 账户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 登录失败记录列表
     */
    List<LoginLogPO> selectFailLoginsByAccountId(@Param("accountId") Long accountId, 
                                                @Param("startTime") LocalDateTime startTime, 
                                                @Param("endTime") LocalDateTime endTime);
    
    /**
     * 统计指定时间段内的登录失败次数
     *
     * @param accountId 账户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 登录失败次数
     */
    int countFailLoginsByAccountId(@Param("accountId") Long accountId, 
                                  @Param("startTime") LocalDateTime startTime, 
                                  @Param("endTime") LocalDateTime endTime);
}
