package com.yuanchuan.user.infrastructure.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuanchuan.user.domain.model.UserThirdPartyBinding;
import com.yuanchuan.user.infrastructure.po.UserThirdPartyBindingPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【user_third_party_binding(用户第三方账号绑定表)】的数据库操作Mapper
* @createDate 2025-05-13 16:15:21
* @Entity generator.domain.UserThirdPartyBinding
*/
public interface UserThirdPartyBindingMapper extends BaseMapper<UserThirdPartyBindingPO> {

    /**
     * 根据用户id 查询绑定三方信息
     * @param userId
     * @return
     */
    List<UserThirdPartyBindingPO> findByUserId(@Param("userId") Long userId);

    /**
     * 主键id批量删除(逻辑）
     * @param bingdingIds
     * @return
     */
    int logicDeleteByIds(@Param("bingdingIds") List<Long> bingdingIds);

    /**
     * 根据ID逻辑删除
     *
     * @param id 绑定ID
     * @return 影响行数
     */
    int logicDeleteById(@Param("id") Long id);

    /**
     * 根据平台和外部用户ID查询绑定信息
     *
     * @param platform 平台
     * @param externalUserId 外部用户ID
     * @return 绑定信息
     */
    UserThirdPartyBindingPO selectByPlatformAndExternalUserId(@Param("platform") String platform, @Param("externalUserId") String externalUserId);

    /**
     * 根据用户ID和外部用户ID查询绑定信息
     *
     * @param customerAccountId 用户ID
     * @param externalUserId 外部用户ID
     * @return 绑定信息
     */
    UserThirdPartyBinding selectByCustomerAccountIdAndExternalUserId(@Param("customerAccountId")  Long customerAccountId, @Param("externalUserId")  String externalUserId);
}




