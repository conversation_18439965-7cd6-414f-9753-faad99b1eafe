package com.yuanchuan.user.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yuanchuan.user.domain.model.TaiwanRegion;
import com.yuanchuan.user.domain.repository.TaiwanRegionRepository;
import com.yuanchuan.user.infrastructure.mapper.TaiwanRegionMapper;
import com.yuanchuan.user.infrastructure.po.TaiwanRegionPO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 台湾行政区划仓储实现类
 * <AUTHOR>
 * @date 2025-05-20 10:58:10:58
 */
@Repository
public class TaiwanRegionRepositoryImpl implements TaiwanRegionRepository {

    @Autowired
    private TaiwanRegionMapper taiwanRegionMapper;

    @Override
    public Optional<TaiwanRegion> findByCoordinate(BigDecimal longitude, BigDecimal latitude) {
        // 根据经纬度查询最近的行政区划
        // 这里使用简化的实现，实际可能需要使用空间查询或距离计算
        TaiwanRegionPO po = taiwanRegionMapper.findNearestByCoordinate(longitude, latitude);
        return Optional.ofNullable(po).map(this::toEntity);
    }

    @Override
    public Optional<List<TaiwanRegion>> findByName(String name) {
        // 根据名称模糊查询行政区划
        LambdaQueryWrapper<TaiwanRegionPO> query = new LambdaQueryWrapper<>();
        query.like(TaiwanRegionPO::getName, name);

        List<TaiwanRegionPO> po = taiwanRegionMapper.selectList(query);
        return Optional.ofNullable(po)
                .filter(list -> !list.isEmpty())
                .map(list -> list.stream().map(this::toEntity).collect(Collectors.toList()));
    }

    @Override
    public Optional<TaiwanRegion> findById(Long id) {
        TaiwanRegionPO po = taiwanRegionMapper.selectById(id);
        return Optional.ofNullable(po).map(this::toEntity);
    }

    @Override
    public Optional<TaiwanRegion> findByCode(String code) {
        LambdaQueryWrapper<TaiwanRegionPO> query = new LambdaQueryWrapper<>();
        query.eq(TaiwanRegionPO::getCode, code);
        
        TaiwanRegionPO po = taiwanRegionMapper.selectOne(query);
        return Optional.ofNullable(po).map(this::toEntity);
    }

    @Override
    public Optional<TaiwanRegion> findParentById(Long id) {
        // 先查询当前区划，获取父ID
        TaiwanRegionPO current = taiwanRegionMapper.selectById(id);
        if (current == null || current.getParentId() == null) {
            return Optional.empty();
        }
        
        // 再查询父级区划
        TaiwanRegionPO parent = taiwanRegionMapper.selectById(current.getParentId());
        return Optional.ofNullable(parent).map(this::toEntity);
    }
    
    /**
     * 将持久化对象转换为领域对象
     *
     * @param po 持久化对象
     * @return 领域对象
     */
    private TaiwanRegion toEntity(TaiwanRegionPO po) {
        if (po == null) {
            return null;
        }
        TaiwanRegion entity = new TaiwanRegion();
        BeanUtils.copyProperties(po, entity);
        return entity;
    }
}
