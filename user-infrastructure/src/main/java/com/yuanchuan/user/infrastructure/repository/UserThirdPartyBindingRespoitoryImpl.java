package com.yuanchuan.user.infrastructure.repository;

import com.yuanchuan.user.domain.model.UserThirdPartyAuth;
import com.yuanchuan.user.domain.model.UserThirdPartyBinding;
import com.yuanchuan.user.domain.repository.UserThirdPartyBindingRespoitory;
import com.yuanchuan.user.infrastructure.mapper.UserThirdPartyBindingMapper;
import com.yuanchuan.user.infrastructure.po.UserThirdPartyAuthPO;
import com.yuanchuan.user.infrastructure.po.UserThirdPartyBindingPO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-05-13 16:40:16:40
 */
@Repository
public class UserThirdPartyBindingRespoitoryImpl implements UserThirdPartyBindingRespoitory {

    @Autowired
    private UserThirdPartyBindingMapper userThirdPartyBindingMapper;

    @Override
    public List<UserThirdPartyBinding> findByUserId(Long userId) {
        List<UserThirdPartyBindingPO> bindingPOList =  userThirdPartyBindingMapper.findByUserId(userId);
        return bindingPOList.stream().map(UserThirdPartyBindingPO::convertToUserThirdPartyBinding).toList();
    }

    @Override
    public boolean logicDeleteByIds(List<Long> bingdingIds) {
        return userThirdPartyBindingMapper.logicDeleteByIds(bingdingIds) > 0;
    }

    @Override
    public UserThirdPartyBinding findById(Long id) {
         return userThirdPartyBindingMapper.selectById(id).convertToUserThirdPartyBinding();
    }

    @Override
    public boolean logicDeleteById(Long id) {
        return userThirdPartyBindingMapper.logicDeleteById(id) > 0;
    }

    @Override
    public UserThirdPartyBinding save(UserThirdPartyBinding binding) {
        UserThirdPartyBindingPO bindingPO = toPO(binding);
        if (bindingPO.getId() == null) {
            userThirdPartyBindingMapper.insert(bindingPO);
        } else {
            userThirdPartyBindingMapper.updateById(bindingPO);
        }

        return bindingPO.convertToUserThirdPartyBinding();
    }

    @Override
    public UserThirdPartyBinding findByPlatformAndExternalUserId(String platform, String externalUserId) {
        UserThirdPartyBindingPO userThirdPartyBindingPO = userThirdPartyBindingMapper.selectByPlatformAndExternalUserId(platform, externalUserId);
        if (userThirdPartyBindingPO == null) {
            return null;
        }
        return userThirdPartyBindingPO.convertToUserThirdPartyBinding();
    }

    @Override
    public UserThirdPartyBinding findByCustomerAccountIdAndExternalUserId(Long customerAccountId, String externalUserId) {
        return userThirdPartyBindingMapper.selectByCustomerAccountIdAndExternalUserId(customerAccountId,externalUserId);
    }


    private UserThirdPartyBindingPO toPO(UserThirdPartyBinding userThirdPartyBinding){
        UserThirdPartyBindingPO userThirdPartyBindingPO = new UserThirdPartyBindingPO();
        BeanUtils.copyProperties(userThirdPartyBinding,userThirdPartyBindingPO);
        return userThirdPartyBindingPO;
    }
}
