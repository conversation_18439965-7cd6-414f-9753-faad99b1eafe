package com.yuanchuan.user.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuanchuan.common.domain.query.PageQueryV;
import com.yuanchuan.user.domain.model.Permission;
import com.yuanchuan.user.domain.repository.PermissionRepository;
import com.yuanchuan.user.infrastructure.mapper.PermissionMapper;
import com.yuanchuan.user.infrastructure.po.PermissionPO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 权限仓储实现类
 */
@Repository
public class PermissionRepositoryImpl implements PermissionRepository {

    @Autowired
    private PermissionMapper permissionMapper;

    @Override
    public Permission save(Permission permission) {
        PermissionPO po = toPO(permission);
        if (po.getId() == null) {
            permissionMapper.insert(po);
        } else {
            permissionMapper.updateById(po);
        }
        return toEntity(po);
    }

    @Override
    public Optional<Permission> findById(Long id) {
        LambdaQueryWrapper<PermissionPO> query = new LambdaQueryWrapper<>();
        query.eq(PermissionPO::getId, id)
                .eq(PermissionPO::getActive, 1); // 只查未删除
        PermissionPO po = permissionMapper.selectOne(query);
        return Optional.ofNullable(po).map(this::toEntity);
    }

    @Override
    public Optional<Permission> findByPermissionCode(String permissionCode) {
        LambdaQueryWrapper<PermissionPO> query = new LambdaQueryWrapper<>();
        query.eq(PermissionPO::getPermissionCode, permissionCode)
                .eq(PermissionPO::getActive, 1);
        PermissionPO po = permissionMapper.selectOne(query);
        return Optional.ofNullable(po).map(this::toEntity);
    }

    @Override
    public Optional<Permission> findByPermissionName(String permissionName) {
        LambdaQueryWrapper<PermissionPO> query = new LambdaQueryWrapper<>();
        query.eq(PermissionPO::getPermissionName, permissionName)
                .eq(PermissionPO::getActive, 1);
        PermissionPO po = permissionMapper.selectOne(query);
        return Optional.ofNullable(po).map(this::toEntity);
    }

    @Override
    public List<Permission> findAll() {
        LambdaQueryWrapper<PermissionPO> query = new LambdaQueryWrapper<>();
        query.eq(PermissionPO::getActive, 1)
                .orderByAsc(PermissionPO::getOrderNum);
        List<PermissionPO> poList = permissionMapper.selectList(query);
        return poList.stream().map(this::toEntity).collect(Collectors.toList());
    }

    @Override
    public List<Permission> findByType(String type) {
        LambdaQueryWrapper<PermissionPO> query = new LambdaQueryWrapper<>();
        query.eq(PermissionPO::getType, type)
                .eq(PermissionPO::getActive, 1)
                .eq(PermissionPO::getStatus, 1)
                .orderByAsc(PermissionPO::getOrderNum);
        List<PermissionPO> poList = permissionMapper.selectList(query);
        return poList.stream().map(this::toEntity).collect(Collectors.toList());
    }

    @Override
    public List<Permission> findByParentId(Long parentId) {
        LambdaQueryWrapper<PermissionPO> query = new LambdaQueryWrapper<>();
        query.eq(PermissionPO::getParentId, parentId)
                .eq(PermissionPO::getActive, 1)
                .orderByAsc(PermissionPO::getOrderNum);
        List<PermissionPO> poList = permissionMapper.selectList(query);
        return poList.stream().map(this::toEntity).collect(Collectors.toList());
    }

    @Override
    public List<Permission> findByAccountId(Long accountId) {
        List<PermissionPO> poList = permissionMapper.findByAccountId(accountId);
        return poList.stream().map(this::toEntity).collect(Collectors.toList());
    }

    @Override
    public List<Permission> findByRoleId(Long roleId) {
        List<PermissionPO> poList = permissionMapper.findByRoleId(roleId);
        return poList.stream().map(this::toEntity).collect(Collectors.toList());
    }

    @Override
    public List<Permission> findByConditions(String permissionMenu, Integer status, String keyword, PageQueryV pageQuer) {
        List<Permission> permissionList = new ArrayList<>();

        List<PermissionPO> poList = permissionMapper.findByConditions(permissionMenu, status, keyword, pageQuer.getUsePaging(), pageQuer.getOffset(), pageQuer.getPageSize());

        if(CollectionUtils.isEmpty(poList)){
            return permissionList;
        }

        for(PermissionPO po : poList){
            Permission permission = new Permission();
            BeanUtils.copyProperties(po, permission);
            if(StringUtils.hasText(po.getRoleIds())){
                permission.setRoleIds(Arrays.stream(po.getRoleIds().split(","))
                        .map(String::trim)          // 去除空格，防止输入为 "1, 2 ,3"
                        .filter(s -> !s.isEmpty())  // 防止空字符串
                        .map(Long::valueOf)         // 转为 Long 类型
                        .collect(Collectors.toList()));
            }

            permissionList.add(permission);
        }

        return permissionList;
    }

    @Override
    public Long countByConditions(String permissionMenu, Integer status, String keyword) {
        return permissionMapper.countByConditions(permissionMenu, status, keyword);
    }

    @Override
    public Boolean deleteById(Long id) {
        PermissionPO po = new PermissionPO();
        po.setId(id);
        po.setActive(0); // 逻辑删除
        return permissionMapper.updateById(po) > 0;
    }


    @Override
    public Permission findByPermissionId(Long id) {
        PermissionPO po = permissionMapper.findByPermissionId(id);
        return toEntity(po);
    }


    /**
     * 将领域对象转换为持久化对象
     */
    private PermissionPO toPO(Permission permission) {
        PermissionPO po = new PermissionPO();
        BeanUtils.copyProperties(permission, po);
        return po;
    }

    /**
     * 将持久化对象转换为领域对象
     */
    private Permission toEntity(PermissionPO po) {
        if(po == null){return null;}
        Permission permission = new Permission();
        BeanUtils.copyProperties(po, permission);

        if(StringUtils.hasText(po.getRoleIds())){
            permission.setRoleIds(Arrays.stream(po.getRoleIds().split(","))
                    .map(String::trim)          // 去除空格，防止输入为 "1, 2 ,3"
                    .filter(s -> !s.isEmpty())  // 防止空字符串
                    .map(Long::valueOf)         // 转为 Long 类型
                    .collect(Collectors.toList()));
        }
        return permission;
    }
}
