package com.yuanchuan.user.infrastructure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuanchuan.user.infrastructure.po.TaiwanRegionPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;

/**
* <AUTHOR>
* @description 针对表【taiwan_region(行政区划表)】的数据库操作Mapper
* @createDate 2025-05-19 15:57:47
*/
@Mapper
public interface TaiwanRegionMapper extends BaseMapper<TaiwanRegionPO> {

    /**
     * 根据经纬度查询最近的行政区划
     * 使用简单的欧几里得距离计算
     *
     * @param longitude 经度
     * @param latitude 纬度
     * @return 最近的行政区划
     */

    TaiwanRegionPO findNearestByCoordinate(@Param("longitude") BigDecimal longitude, @Param("latitude") BigDecimal latitude);
}
