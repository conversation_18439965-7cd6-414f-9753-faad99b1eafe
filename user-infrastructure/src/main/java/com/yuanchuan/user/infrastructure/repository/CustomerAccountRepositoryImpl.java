package com.yuanchuan.user.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yuanchuan.user.domain.model.CustomerAccount;
import com.yuanchuan.user.domain.repository.CustomerAccountRepository;
import com.yuanchuan.user.infrastructure.mapper.CustomerAccountMapper;
import com.yuanchuan.user.infrastructure.po.CustomerAccountPO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Optional;

/**
 * 用户账号仓储实现类
 */
@Repository
public class CustomerAccountRepositoryImpl implements CustomerAccountRepository {

    @Autowired
    private CustomerAccountMapper customerAccountMapper;

    @Override
    public CustomerAccount save(CustomerAccount account) {
        CustomerAccountPO po = toPO(account);
        if (po.getId() == null) {
            customerAccountMapper.insert(po);
        } else {
            customerAccountMapper.updateById(po);
        }
        return toEntity(po);
    }

    @Override
    public Optional<CustomerAccount> findById(Long id) {
        LambdaQueryWrapper<CustomerAccountPO> query = new LambdaQueryWrapper<>();
        query.eq(CustomerAccountPO::getId, id)
                .eq(CustomerAccountPO::getActive, 1); // 只查未删除
        CustomerAccountPO po = customerAccountMapper.selectOne(query);
        return Optional.ofNullable(po).map(this::toEntity);
    }

    @Override
    public Optional<CustomerAccount> findByUserPersonId(Long userPersonId) {
        CustomerAccountPO po = customerAccountMapper.selectByUserPersonId(userPersonId);
        return Optional.ofNullable(po).map(this::toEntity);
    }

    @Override
    public boolean updateUserStatus(Long id, String userStatus) {
        return customerAccountMapper.updateUserStatus(id, userStatus) > 0;
    }

    @Override
    public boolean updatePassword(Long id, String password) {
        return customerAccountMapper.updatePassword(id, password) > 0;
    }

    @Override
    public boolean updateNickName(Long id, String nickName) {
        return customerAccountMapper.updateNickName(id, nickName) > 0;
    }

    @Override
    public boolean updateAvatar(Long id, String avatar) {
        return customerAccountMapper.updateAvatar(id, avatar) > 0;
    }

    @Override
    public boolean deleteById(Long id) {
        return customerAccountMapper.deleteById(id) > 0;
    }

    @Override
    public boolean logicDeleteById(Long id) {
        return customerAccountMapper.logicDeleteById(id) > 0;
    }

    @Override
    public boolean updateGender(Long id, String gender) {
        return customerAccountMapper.updateGender(id, gender) > 0;
    }

    @Override
    public boolean updateBirthday(Long id, LocalDate birthday) {
        return customerAccountMapper.updateBirthday(id, birthday) > 0;
    }

    @Override
    public boolean updateAddress(Long id, String address, String provinceCode, String cityCode, String regionCode,
                               String provinceName, String cityName, String regionName,
                               BigDecimal longitude, BigDecimal latitude) {
        return customerAccountMapper.updateAddress(id, address, provinceCode, cityCode, regionCode,
                provinceName, cityName, regionName, longitude, latitude) > 0;
    }


    /**
     * 将领域对象转换为持久化对象
     *
     * @param entity 领域对象
     * @return 持久化对象
     */
    private CustomerAccountPO toPO(CustomerAccount entity) {
        if (entity == null) {
            return null;
        }
        CustomerAccountPO po = new CustomerAccountPO();
        BeanUtils.copyProperties(entity, po);
        return po;
    }

    /**
     * 将持久化对象转换为领域对象
     *
     * @param po 持久化对象
     * @return 领域对象
     */
    private CustomerAccount toEntity(CustomerAccountPO po) {
        if (po == null) {
            return null;
        }
        CustomerAccount entity = new CustomerAccount();
        BeanUtils.copyProperties(po, entity);
        return entity;
    }
}
