package com.yuanchuan.user.infrastructure.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 用户(自然人)账号信息变更记录表
 * @TableName user_change_logs
 */
@Data
@TableName(value = "user_change_logs")
public class UserChangeLogsPO {
    /**
     * 主键，自增ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 旧值，可为空{"user_id":xx,"mobile":"xxx"}
     */
    private Object oldValue;

    /**
     * 新值，可为空
     */
    private Object newValue;

    /**
     * 操作IP地址
     */
    private String deviceId;

    /**
     * 来源：CUSTOMER_LOGIN - c端用户登录 , SHOP_LOGIN  - 商户登录
     */
    private String source;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 更新时间
     */
    private Date updatedAt;

    /**
     * 是否启用 0-禁用(删除) 1-启用
     */
    private Integer active;
}