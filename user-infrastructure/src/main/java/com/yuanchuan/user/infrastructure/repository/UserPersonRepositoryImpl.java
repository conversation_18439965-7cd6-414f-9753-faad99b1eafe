package com.yuanchuan.user.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yuanchuan.common.enums.users.login.PlatformType;
import com.yuanchuan.user.domain.model.UserPerson;
import com.yuanchuan.user.domain.repository.UserPersonRepository;
import com.yuanchuan.user.infrastructure.mapper.UserPersonMapper;
import com.yuanchuan.user.infrastructure.po.UserPersonPO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 自然人仓储实现类
 */
@Repository
public class UserPersonRepositoryImpl implements UserPersonRepository {
    
    @Autowired
    private UserPersonMapper userPersonMapper;
    
    @Override
    public UserPerson save(UserPerson userPerson) {
        UserPersonPO po = toPO(userPerson);
        if (po.getId() == null) {
            userPersonMapper.insert(po);
        } else {
            userPersonMapper.updateById(po);
        }
        return toEntity(po);
    }
    
    @Override
    public Optional<UserPerson> findById(Long id) {
        LambdaQueryWrapper<UserPersonPO> query = new LambdaQueryWrapper<>();
        query.eq(UserPersonPO::getId, id)
                .eq(UserPersonPO::getActive, 1);

        UserPersonPO po = userPersonMapper.selectOne(query);
        return Optional.ofNullable(po).map(this::toEntity);
    }
    
    @Override
    public Optional<UserPerson> findByPhoneAndSource(String phone,String source) {
        UserPersonPO po = userPersonMapper.selectByPhoneAndSource(phone,source);
        return Optional.ofNullable(po).map(this::toEntity);
    }
    
    @Override
    public Optional<UserPerson> findByEmailAndSource(String email,String source) {
        UserPersonPO po = userPersonMapper.selectByEmailAndSource(email,source);
        return Optional.ofNullable(po).map(this::toEntity);
    }
    
    @Override
    public boolean updatePhone(Long id, String phone) {
        return userPersonMapper.updatePhone(id, phone) > 0;
    }
    
    @Override
    public boolean updateEmail(Long id, String email) {
        return userPersonMapper.updateEmail(id, email) > 0;
    }
    
    @Override
    public boolean deleteById(Long id) {
        return userPersonMapper.deleteById(id) > 0;
    }

    @Override
    public boolean logicDeleteById(Long id) {
        return userPersonMapper.logicDeleteById(id) > 0;
    }


    /**
     * 将领域对象转换为持久化对象
     *
     * @param entity 领域对象
     * @return 持久化对象
     */
    private UserPersonPO toPO(UserPerson entity) {
        if (entity == null) {
            return null;
        }
        UserPersonPO po = new UserPersonPO();
        BeanUtils.copyProperties(entity, po);
        return po;
    }
    
    /**
     * 将持久化对象转换为领域对象
     *
     * @param po 持久化对象
     * @return 领域对象
     */
    private UserPerson toEntity(UserPersonPO po) {
        if (po == null) {
            return null;
        }
        UserPerson entity = new UserPerson();
        BeanUtils.copyProperties(po, entity);
        return entity;
    }
}
