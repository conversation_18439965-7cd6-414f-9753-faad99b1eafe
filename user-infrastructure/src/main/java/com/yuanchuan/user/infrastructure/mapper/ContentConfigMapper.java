package com.yuanchuan.user.infrastructure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuanchuan.user.infrastructure.po.ContentConfigPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 内容配置Mapper接口
 */
@Mapper
public interface ContentConfigMapper extends BaseMapper<ContentConfigPO> {

    /**
     * 根据类型查询内容配置列表
     *
     * @param type 内容类型
     * @return 内容配置列表
     */
    @Select("SELECT * FROM content_configs WHERE type = #{type} AND active = 1 ORDER BY sort_order ASC")
    List<ContentConfigPO> selectByType(@Param("type") String type);

    /**
     * 根据类型和标题查询内容配置
     *
     * @param type  内容类型
     * @param title 标题
     * @return 内容配置
     */
    @Select("SELECT * FROM content_configs WHERE type = #{type} AND title = #{title} AND active = 1 LIMIT 1")
    ContentConfigPO selectByTypeAndTitle(@Param("type") String type, @Param("title") String title);

    /**
     * 查询所有启用的内容配置
     *
     * @return 内容配置列表
     */
    @Select("SELECT * FROM content_configs WHERE is_enabled = 1 AND active = 1 ORDER BY type, sort_order ASC")
    List<ContentConfigPO> selectAllEnabled();

    /**
     * 根据类型查询所有启用的内容配置
     *
     * @param type 内容类型
     * @return 内容配置列表
     */
    @Select("SELECT * FROM content_configs WHERE type = #{type} AND is_enabled = 1 AND active = 1 ORDER BY sort_order ASC")
    List<ContentConfigPO> selectEnabledByType(@Param("type") String type);

    /**
     * 根据父级ID查询内容配置列表
     *
     * @param parentId 父级内容ID
     * @return 内容配置列表
     */
    @Select("SELECT * FROM content_configs WHERE parent_id = #{parentId} AND active = 1 ORDER BY sort_order ASC")
    List<ContentConfigPO> selectByParentId(@Param("parentId") Long parentId);

    /**
     * 根据父级ID查询所有启用的内容配置
     *
     * @param parentId 父级内容ID
     * @return 内容配置列表
     */
    @Select("SELECT * FROM content_configs WHERE parent_id = #{parentId} AND is_enabled = 1 AND active = 1 ORDER BY sort_order ASC")
    List<ContentConfigPO> selectEnabledByParentId(@Param("parentId") Long parentId);



    /**
     * 根据父级ID列表查询所有启用的内容配置
     *
     * @param parentIds 父级内容ID列表
     * @return 内容配置列表
     */
    @Select("<script>SELECT * FROM content_configs WHERE parent_id IN <foreach collection='parentIds' item='id' open='(' separator=',' close=')'>#{id}</foreach> AND is_enabled = 1 AND active = 1 ORDER BY sort_order ASC</script>")
    List<ContentConfigPO> selectEnabledByParentIds(@Param("parentIds") List<Long> parentIds);

    /**
     * 查询所有一级内容（父级ID为null或0的内容）
     *
     * @return 内容配置列表
     */
    @Select("SELECT * FROM content_configs WHERE (parent_id IS NULL OR parent_id = 0) AND active = 1 ORDER BY type, sort_order ASC")
    List<ContentConfigPO> selectRootContents();

    /**
     * 查询所有启用的一级内容（父级ID为null或0的内容）
     *
     * @return 内容配置列表
     */
    @Select("SELECT * FROM content_configs WHERE (parent_id IS NULL OR parent_id = 0) AND is_enabled = 1 AND active = 1 ORDER BY type, sort_order ASC")
    List<ContentConfigPO> selectEnabledRootContents();

    /**
     * 根据类型查询所有一级内容（父级ID为null或0的内容）
     *
     * @param type 内容类型
     * @return 内容配置列表
     */
    @Select("SELECT * FROM content_configs WHERE type = #{type} AND (parent_id IS NULL OR parent_id = 0) AND active = 1 ORDER BY sort_order ASC")
    List<ContentConfigPO> selectRootContentsByType(@Param("type") String type);

    /**
     * 根据类型查询所有启用的一级内容（父级ID为null或0的内容）
     *
     * @param type 内容类型
     * @return 内容配置列表
     */
    @Select("SELECT * FROM content_configs WHERE type = #{type} AND (parent_id IS NULL OR parent_id = 0) AND is_enabled = 1 AND active = 1 ORDER BY sort_order ASC")
    List<ContentConfigPO> selectEnabledRootContentsByType(@Param("type") String type);

    /**
     * 动态查询内容配置
     *
     * @param condition 查询条件对象，包含类型、启用状态、父级ID等
     * @return 内容配置列表
     */
    List<ContentConfigPO> selectByCondition(@Param("condition") ContentConfigPO condition);
}
