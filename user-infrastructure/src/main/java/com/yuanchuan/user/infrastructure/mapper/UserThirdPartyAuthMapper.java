package com.yuanchuan.user.infrastructure.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuanchuan.user.infrastructure.po.UserThirdPartyAuthPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【user_third_party_auth(用户第三方账号授权表)】的数据库操作Mapper
* @createDate 2025-05-13 16:15:21
* @Entity generator.domain.UserThirdPartyAuth
*/
public interface UserThirdPartyAuthMapper extends BaseMapper<UserThirdPartyAuthPO> {


    List<UserThirdPartyAuthPO> findByUserId(@Param("userId") Long userId);

    /**
     * 根据主键批量删除(逻辑）
     * @param partyAuthIds
     * @return
     */
    int logicDeleteByIds(@Param("partyAuthIds") List<Long> partyAuthIds);

    UserThirdPartyAuthPO queryByBindingId(@Param("bindingId") Long bindingId);

    /**
     * 根据第三方平台的 accessToken 查询用户第三方绑定信息
     *
     * @param accessToken 第三方平台的 accessToken
     * @return 用户第三方绑定信息
     */
    UserThirdPartyAuthPO selectByAccessToken(@Param("accessToken") String accessToken);

}




