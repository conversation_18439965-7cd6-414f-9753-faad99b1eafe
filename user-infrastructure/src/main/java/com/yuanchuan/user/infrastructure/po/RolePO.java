package com.yuanchuan.user.infrastructure.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 角色表持久化对象
 */
@Data
@TableName("role")
public class RolePO {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 角色编码，唯一，例如：ADMIN、SHOP_MANAGER
     */
    private String roleCode;
    
    /**
     * 角色名称
     */
    private String roleName;


    /**
     * 所属组织ID
     */
    private Long orgId;

    /**
     * 角色描述
     */
    private String description;


    /**
     * 是否启用 0-禁用 1-启用
     */
    private Integer status;

    /**
     * 是否启用 0-禁用(删除) 1-启用
     */
    private Integer active;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 创建人
     */
    private String createdBy;
    
    /**
     * 更新人
     */
    private String updatedBy;

    @TableField(exist = false)
    private String permissionIds;
}
