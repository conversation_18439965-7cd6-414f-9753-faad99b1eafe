package com.yuanchuan.user.infrastructure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuanchuan.user.infrastructure.po.BusinessAccountRolePO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * 商户账户角色关联Mapper接口
 */
@Mapper
public interface BusinessAccountRoleMapper extends BaseMapper<BusinessAccountRolePO> {
    
    /**
     * 根据商户账户ID删除商户账户角色关联
     *
     * @param businessAccountId 商户账户ID
     * @return 影响行数
     */
    @Update("UPDATE business_account_role SET active = 0, updated_at = NOW() WHERE business_account_id = #{businessAccountId}")
    int deleteByBusinessAccountId(@Param("businessAccountId") Long businessAccountId);
    
    /**
     * 根据商户账户ID和角色ID删除商户账户角色关联
     *
     * @param businessAccountId 商户账户ID
     * @param roleId 角色ID
     * @return 影响行数
     */
    @Update("UPDATE business_account_role SET active = 0, updated_at = NOW() WHERE business_account_id = #{businessAccountId} AND role_id = #{roleId}")
    int deleteByBusinessAccountIdAndRoleId(@Param("businessAccountId") Long businessAccountId, @Param("roleId") Long roleId);
}
