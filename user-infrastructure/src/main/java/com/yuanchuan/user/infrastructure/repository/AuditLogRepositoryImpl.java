package com.yuanchuan.user.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yuanchuan.user.domain.model.AuditLog;
import com.yuanchuan.user.domain.repository.AuditLogRepository;
import com.yuanchuan.user.infrastructure.mapper.AuditLogMapper;
import com.yuanchuan.user.infrastructure.po.AuditLogPO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 审计日志仓储实现类
 */
@Repository
public class AuditLogRepositoryImpl implements AuditLogRepository {

    @Autowired
    private AuditLogMapper auditLogMapper;

    @Override
    public AuditLog save(AuditLog auditLog) {
        AuditLogPO po = toPO(auditLog);
        if (po.getId() == null) {
            auditLogMapper.insert(po);
        } else {
            auditLogMapper.updateById(po);
        }
        return toEntity(po);
    }

    @Override
    public Optional<AuditLog> findById(Long id) {
        LambdaQueryWrapper<AuditLogPO> query = new LambdaQueryWrapper<>();
        query.eq(AuditLogPO::getId, id)
                .eq(AuditLogPO::getActive, 1);
        AuditLogPO po = auditLogMapper.selectOne(query);
        return Optional.ofNullable(po).map(this::toEntity);
    }

    @Override
    public List<AuditLog> findByBizTypeAndBizId(String bizType, Long bizId) {
        LambdaQueryWrapper<AuditLogPO> query = new LambdaQueryWrapper<>();
        query.eq(AuditLogPO::getBizType, bizType)
                .eq(AuditLogPO::getBizId, bizId)
                .eq(AuditLogPO::getActive, 1)
                .orderByDesc(AuditLogPO::getOperateTime);
        List<AuditLogPO> poList = auditLogMapper.selectList(query);
        return poList.stream().map(this::toEntity).collect(Collectors.toList());
    }

    @Override
    public List<AuditLog> findByOperatorId(Long operatorId) {
        LambdaQueryWrapper<AuditLogPO> query = new LambdaQueryWrapper<>();
        query.eq(AuditLogPO::getOperatorId, operatorId)
                .eq(AuditLogPO::getActive, 1)
                .orderByDesc(AuditLogPO::getOperateTime);
        List<AuditLogPO> poList = auditLogMapper.selectList(query);
        return poList.stream().map(this::toEntity).collect(Collectors.toList());
    }

    @Override
    public List<AuditLog> findByConditions(String bizType, Long operatorId, Integer offset, Integer limit) {
        LambdaQueryWrapper<AuditLogPO> query = new LambdaQueryWrapper<>();
        query.eq(AuditLogPO::getActive, 1);
        
        if (StringUtils.hasText(bizType)) {
            query.eq(AuditLogPO::getBizType, bizType);
        }
        
        if (operatorId != null) {
            query.eq(AuditLogPO::getOperatorId, operatorId);
        }
        
        query.orderByDesc(AuditLogPO::getOperateTime)
                .last("LIMIT " + offset + ", " + limit);
        
        List<AuditLogPO> poList = auditLogMapper.selectList(query);
        return poList.stream().map(this::toEntity).collect(Collectors.toList());
    }

    @Override
    public Long countByConditions(String bizType, Long operatorId) {
        LambdaQueryWrapper<AuditLogPO> query = new LambdaQueryWrapper<>();
        query.eq(AuditLogPO::getActive, 1);
        
        if (StringUtils.hasText(bizType)) {
            query.eq(AuditLogPO::getBizType, bizType);
        }
        
        if (operatorId != null) {
            query.eq(AuditLogPO::getOperatorId, operatorId);
        }
        
        return auditLogMapper.selectCount(query);
    }

    /**
     * 将领域对象转换为持久化对象
     */
    private AuditLogPO toPO(AuditLog auditLog) {
        AuditLogPO po = new AuditLogPO();
        BeanUtils.copyProperties(auditLog, po);
        return po;
    }

    /**
     * 将持久化对象转换为领域对象
     */
    private AuditLog toEntity(AuditLogPO po) {
        AuditLog auditLog = new AuditLog();
        BeanUtils.copyProperties(po, auditLog);
        return auditLog;
    }
}
