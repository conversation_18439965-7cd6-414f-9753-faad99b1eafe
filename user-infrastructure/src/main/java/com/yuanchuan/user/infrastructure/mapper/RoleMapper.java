package com.yuanchuan.user.infrastructure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuanchuan.common.domain.query.PageQueryV;
import com.yuanchuan.user.domain.model.Role;
import com.yuanchuan.user.infrastructure.po.RolePO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 角色Mapper接口
 */
@Mapper
public interface RoleMapper extends BaseMapper<RolePO> {
    
    /**
     * 根据商户账户ID查询角色列表
     *
     * @param businessAccountId 商户账户ID
     * @return 角色列表
     */
    @Select("SELECT r.* FROM role r " +
            "JOIN business_account_role bar ON r.id = bar.role_id " +
            "WHERE bar.business_account_id = #{businessAccountId} " +
            "AND r.active = 1 AND bar.active = 1")
    List<RolePO> findByBusinessAccountId(@Param("businessAccountId") Long businessAccountId);

    List<RolePO> findByConditions(@Param("roleId") String roleId, @Param("status") Integer status, @Param("keyword")String keyword,
                                @Param("usePaging") boolean usePaging, @Param("offset") Integer offset, @Param("limit") Integer limit);


    Long findByConditionsCount(@Param("roleId") String roleId, @Param("status") Integer status, @Param("keyword")String keyword);

    RolePO queryRoleById(@Param("roleId") Long roleId);
}
