package com.yuanchuan.user.infrastructure.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 行政区划表
 * @TableName taiwan_region
 */
@Data
@TableName("taiwan_region")
public class TaiwanRegionPO {
    /**
     * 行政区划ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 区域名称
     */
    private String name;

    /**
     * 行政区划代码
     */
    private String code;

    /**
     * 父级行政区划ID
     */
    private Long parentId;

    /**
     * 层级：1-国家，2-省/直辖市，3-市，4-区/县
     */
    private Integer level;

    /**
     * 中心点经度
     */
    private BigDecimal centerLongitude;

    /**
     * 中心点纬度
     */
    private BigDecimal centerLatitude;

    /**
     * 边界多边形（WGS84坐标系）
     */
    private Object boundary;

    /**
     * 数据来源：SYSTEM-系统，GOOGLE-谷歌地图，MANUAL-手动
     */
    private String source;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 更新时间
     */
    private Date updatedAt;
}