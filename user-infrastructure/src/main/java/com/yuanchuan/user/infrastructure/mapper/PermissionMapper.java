package com.yuanchuan.user.infrastructure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuanchuan.user.infrastructure.po.PermissionPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【permission(权限表，支持菜单、页面、按钮、API权限)】的数据库操作Mapper
* @createDate 2025-05-28 11:06:39
* @Entity generator.domain.Permission
*/
public interface PermissionMapper extends BaseMapper<PermissionPO> {

    /**
     * 根据账户ID查询权限列表
     *
     * @param accountId 账户ID
     * @return 权限列表
     */
    List<PermissionPO> findByAccountId(@Param("accountId") Long accountId);

    /**
     * 根据角色ID查询权限列表
     *
     * @param roleId 角色ID
     * @return 权限列表
     */
    List<PermissionPO> findByRoleId(@Param("roleId") Long roleId);

    /**
     * 分页查询权限列表
     *
     * @param page 分页参数
     * @param parentId 父权限ID
     * @param status 状态
     * @param keyword 关键词
     * @param type 类型
     * @return 权限分页结果
     */
    IPage<PermissionPO> findByConditionsWithPage(
            IPage<PermissionPO> page,
            @Param("parentId") Long parentId,
            @Param("status") Integer status,
            @Param("keyword") String keyword,
            @Param("type") String type
    );

    /**
     * 查询权限列表（分页）
     *
     * @param permissionMenu 父权限ID
     * @param status 状态
     * @param keyword 关键词
     * @return 权限列表
     */
    List<PermissionPO> findByConditions(
            @Param("permissionMenu") String permissionMenu,
            @Param("status") Integer status,
            @Param("keyword") String keyword,
            @Param("usePaging") Boolean usePaging,
            @Param("offset") Integer offset,
            @Param("limit") Integer pageSize
    );

    /**
     * 统计权限数量
     *
     * @param permissionMenu 父权限ID
     * @param status 状态
     * @param keyword 关键词
     * @return 权限数量
     */
    Long countByConditions(
            @Param("permissionMenu") String permissionMenu,
            @Param("status") Integer status,
            @Param("keyword") String keyword
    );

    List<PermissionPO> findAll();


    PermissionPO findByPermissionId(@Param("permissionId") Long permissionId);
}




