package com.yuanchuan.user.infrastructure.repository;

import com.yuanchuan.user.domain.model.UserThirdPartyAuth;
import com.yuanchuan.user.domain.repository.UserThirdPartyAuthRespoitory;
import com.yuanchuan.user.infrastructure.mapper.UserThirdPartyAuthMapper;
import com.yuanchuan.user.infrastructure.po.UserThirdPartyAuthPO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-05-13 16:18:16:18
 */
@Repository
public class UserThirdPartyAuthRespoitoryImpl implements UserThirdPartyAuthRespoitory {


    @Autowired
    private UserThirdPartyAuthMapper userThirdPartyAuthMapper;;

    @Override
    public List<UserThirdPartyAuth> findByUserId(Long userId) {
        List<UserThirdPartyAuthPO> authPOList = userThirdPartyAuthMapper.findByUserId(userId);
        return authPOList.stream().map(UserThirdPartyAuthPO::convertToUserThirdPartyAuth).toList();

    }

    @Override
    public boolean logicDeleteByIds(List<Long> partyAuthIds) {
        return userThirdPartyAuthMapper.logicDeleteByIds(partyAuthIds) > 0;
    }

    @Override
    public UserThirdPartyAuth queryByBindingId(Long bindingId) {
        UserThirdPartyAuthPO authPO = userThirdPartyAuthMapper.queryByBindingId(bindingId);
        return authPO.convertToUserThirdPartyAuth();
    }

    @Override
    public UserThirdPartyAuth save(UserThirdPartyAuth userThirdPartyAuth) {
        UserThirdPartyAuthPO userThirdPartyAuthPO = toPO(userThirdPartyAuth);
        userThirdPartyAuthMapper.insert(userThirdPartyAuthPO);

        return userThirdPartyAuthPO.convertToUserThirdPartyAuth();
    }


    @Override
    public UserThirdPartyAuth findByAccessToken(String accessToken) {
        UserThirdPartyAuthPO bindingPO =  userThirdPartyAuthMapper.selectByAccessToken(accessToken);
        if(bindingPO == null){
            return null;
        }
        return bindingPO.convertToUserThirdPartyAuth();
    }

    private UserThirdPartyAuthPO toPO(UserThirdPartyAuth userThirdPartyAuth){
        UserThirdPartyAuthPO userThirdPartyAuthPO = new UserThirdPartyAuthPO();
        BeanUtils.copyProperties(userThirdPartyAuth,userThirdPartyAuthPO);
        return userThirdPartyAuthPO;
    }


}
