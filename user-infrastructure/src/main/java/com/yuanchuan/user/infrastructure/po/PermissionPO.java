package com.yuanchuan.user.infrastructure.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 权限表，支持菜单、页面、按钮、API权限
 * @TableName permission
 */
@Data
@TableName("permission")
public class PermissionPO {
    /**
     * 权限ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 权限唯一编码，支持两种格式：1.模块:动作(如USER:READ) 2.功能编码(如MENU_DASHBOARD)
     */
    private String permissionCode;

    /**
     * 权限名称
     */
    private String permissionName;

    /**
     * 权限类型：MENU-菜单, PAGE-页面, BUTTON-按钮, API-接口
     */
    private String type;

    /**
     * 父权限ID，构成树形结构
     */
    private Long parentId;

    /**
     * 页面路径或API路径，菜单和页面用
     */
    private String url;

    /**
     * HTTP方法：GET、POST、PUT、DELETE等
     */
    private String httpMethod;

    /**
     * 菜单图标（菜单类型时）
     */
    private String icon;

    /**
     * 组件路径（页面类型时）
     */
    private String componentPath;

    /**
     * 排序字段
     */
    private Integer orderNum;

    /**
     * 是否可见：0-隐藏，1-显示
     */
    private Integer isVisible;

    /**
     * 权限描述
     */
    private String description;

    /**
     * 是否启用 0-禁用 1-启用
     */
    private Integer status;

    /**
     * 是否启用 0-禁用(删除) 1-启用
     */
    private Integer active;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 更新时间
     */
    private Date updatedAt;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新人
     */
    private String updatedBy;


    /**
     * 非数据库字段
     */

    @TableField(exist = false)
    private String permissionMenu;

    @TableField(exist = false)
    private String roleNames;

    @TableField(exist = false)
    private String roleIds;
}