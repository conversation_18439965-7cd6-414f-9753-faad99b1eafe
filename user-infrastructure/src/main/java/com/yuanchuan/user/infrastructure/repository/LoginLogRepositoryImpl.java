package com.yuanchuan.user.infrastructure.repository;

import com.yuanchuan.user.domain.model.LoginLogDomian;
import com.yuanchuan.user.domain.repository.LoginLogRepository;
import com.yuanchuan.user.infrastructure.mapper.LoginLogMapper;
import com.yuanchuan.user.infrastructure.po.LoginLogPO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 登录日志仓储实现类
 */
@Slf4j
@Repository
public class LoginLogRepositoryImpl implements LoginLogRepository {

    @Autowired
    private LoginLogMapper loginLogMapper;

    @Override
    public LoginLogDomian save(LoginLogDomian loginLog) {
        LoginLogPO po = toPO(loginLog);

        if (po.getId() == null) {
            po.setCreatedAt(LocalDateTime.now());
            po.setUpdatedAt(LocalDateTime.now());
            po.setCreatedBy("system");
            po.setUpdatedBy("system");
            po.setActive(true); // 设置默认值
            if (po.getAccountId() == null) {
                log.error("警告: 即将插入的LoginLogPO中accountId为null!");
                return toEntity(po);
            }

            loginLogMapper.insert(po);
        } else {
            loginLogMapper.updateById(po);
        }
        return toEntity(po);
    }

    @Override
    public Optional<LoginLogDomian> findById(Long id) {
        LoginLogPO po = loginLogMapper.selectById(id);
        return Optional.ofNullable(po).map(this::toEntity);
    }

    @Override
    public List<LoginLogDomian> findByAccountId(Long accountId, Integer limit) {
        List<LoginLogPO> poList = loginLogMapper.selectByAccountId(accountId, limit);
        return poList.stream().map(this::toEntity).collect(Collectors.toList());
    }

    @Override
    public Optional<LoginLogDomian> findLastLoginByAccountId(Long accountId) {
        LoginLogPO po = loginLogMapper.selectLastLoginByAccountId(accountId);
        return Optional.ofNullable(po).map(this::toEntity);
    }

    @Override
    public List<LoginLogDomian> findFailLoginsByAccountId(Long accountId, LocalDateTime startTime, LocalDateTime endTime) {
        List<LoginLogPO> poList = loginLogMapper.selectFailLoginsByAccountId(accountId, startTime, endTime);
        return poList.stream().map(this::toEntity).collect(Collectors.toList());
    }

    @Override
    public int countFailLoginsByAccountId(Long accountId, LocalDateTime startTime, LocalDateTime endTime) {
        return loginLogMapper.countFailLoginsByAccountId(accountId, startTime, endTime);
    }

    /**
     * 将领域对象转换为持久化对象
     *
     * @param entity 领域对象
     * @return 持久化对象
     */
    private LoginLogPO toPO(LoginLogDomian entity) {
        if (entity == null) {
            return null;
        }
        LoginLogPO po = new LoginLogPO();
        BeanUtils.copyProperties(entity, po);
        return po;
    }

    /**
     * 将持久化对象转换为领域对象
     *
     * @param po 持久化对象
     * @return 领域对象
     */
    private LoginLogDomian toEntity(LoginLogPO po) {
        if (po == null) {
            return null;
        }
        LoginLogDomian entity = new LoginLogDomian();
        BeanUtils.copyProperties(po, entity);
        return entity;
    }
}
