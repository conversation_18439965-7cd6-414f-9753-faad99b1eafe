package com.yuanchuan.user.infrastructure.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用户(自然人)账号表持久化对象
 */
@Data
@TableName("customer_account")
public class CustomerAccountPO {
    /**
     * 用户ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userPersonId;

    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 用户头像URL
     */
    private String avatar;

    /**
     * 性别：UNKNOWN-未知，MALE-男，FEMALE-女
     */
    private String gender;

    /**
     * 生日
     */
    private LocalDate birthday;

    /**
     * 密码哈希
     */
    private String password;

    /**
     * 密码更新时间
     */
    private LocalDateTime passwordUpdatedAt;

    /**
     * 常居地
     */
    private String address;

    /**
     * 省(常居地)编码
     */
    private String provinceCode;

    /**
     * 市(常居地)编码
     */
    private String cityCode;

    /**
     * 地区(常居地)编码
     */
    private String regionCode;

    /**
     * 省(常居地)名称
     */
    private String provinceName;

    /**
     * 市(常居地)名称
     */
    private String cityName;

    /**
     * 地区(常居地)名称
     */
    private String regionName;

    /**
     * 中心点经度
     */
    private BigDecimal centerLongitude;

    /**
     * 中心点纬度
     */
    private BigDecimal centerLatitude;

    /**
     * 注册来源：小程序，应用,H5等
     */
    private String registerSource;

    /**
     * 注册类型：手机号，邮箱，第三方
     */
    private String registerType;

    /**
     * 注册IP地址
     */
    private String registerIp;

    /**
     * 注册设备信息
     */
    private String registerDevice;

    /**
     * 注册时间
     */
    private LocalDateTime registerTime;

    /**
     * 首次APP登录时间
     */
    private LocalDateTime firstAppLoginAt;

    /**
     * 账户状态：ACTIVE, LOCKED, DISABLED
     */
    private String userStatus;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 是否启用 0-禁用(删除) 1-启用
     */
    private Boolean active;
}
