package com.yuanchuan.user.infrastructure.repository;

import com.yuanchuan.user.context.enums.ContentType;
import com.yuanchuan.user.domain.model.ContentConfig;
import com.yuanchuan.user.domain.repository.ContentConfigRepository;
import com.yuanchuan.user.infrastructure.assembler.ContentConfigPOAssembler;
import com.yuanchuan.user.infrastructure.mapper.ContentConfigMapper;
import com.yuanchuan.user.infrastructure.po.ContentConfigPO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 内容配置仓储实现类
 */
@Repository
public class ContentConfigRepositoryImpl implements ContentConfigRepository {

    @Autowired
    private ContentConfigMapper contentConfigMapper;

    @Autowired
    private ContentConfigPOAssembler contentConfigPOAssembler;

    @Override
    @CacheEvict(value = "contentConfig", allEntries = true)
    public ContentConfig save(ContentConfig contentConfig) {
        ContentConfigPO po = contentConfigPOAssembler.toPO(contentConfig);
        if (po.getId() == null) {
            contentConfigMapper.insert(po);
        } else {
            contentConfigMapper.updateById(po);
        }
        return contentConfigPOAssembler.toEntity(po);
    }

    @Override
    @Cacheable(value = "contentConfig", key = "'id_' + #id", unless = "#result == null")
    public Optional<ContentConfig> findById(Long id) {
        ContentConfigPO po = contentConfigMapper.selectById(id);
        return Optional.ofNullable(po).map(contentConfigPOAssembler::toEntity);
    }

    @Override
    @Cacheable(value = "contentConfig", key = "'type_' + #type", unless = "#result.isEmpty()")
    public List<ContentConfig> findByType(ContentType type) {
        List<ContentConfigPO> poList = contentConfigMapper.selectByType(type.name());
        return poList.stream().map(contentConfigPOAssembler::toEntity).collect(Collectors.toList());
    }

    @Override
    @Cacheable(value = "contentConfig", key = "'type_' + #type + '_title_' + #title", unless = "#result == null")
    public Optional<ContentConfig> findByTypeAndTitle(ContentType type, String title) {
        ContentConfigPO po = contentConfigMapper.selectByTypeAndTitle(type.name(), title);
        return Optional.ofNullable(po).map(contentConfigPOAssembler::toEntity);
    }

    @Override
    @Cacheable(value = "contentConfig", key = "'all'", unless = "#result.isEmpty()")
    public List<ContentConfig> findAll() {
        List<ContentConfigPO> poList = contentConfigMapper.selectList(null);
        return poList.stream().map(contentConfigPOAssembler::toEntity).collect(Collectors.toList());
    }

    @Override
    @Cacheable(value = "contentConfig", key = "'all_enabled'", unless = "#result.isEmpty()")
    public List<ContentConfig> findAllEnabled() {
        List<ContentConfigPO> poList = contentConfigMapper.selectAllEnabled();
        return poList.stream().map(contentConfigPOAssembler::toEntity).collect(Collectors.toList());
    }

    @Override
    @Cacheable(value = "contentConfig", key = "'condition_' + #condition", unless = "#result.isEmpty()")
    public List<ContentConfig> findByCondition(ContentConfig condition) {
        // 将领域对象转换为PO对象
        ContentConfigPO conditionPO = contentConfigPOAssembler.toPO(condition);

        // 调用Mapper的动态查询方法
        List<ContentConfigPO> poList = contentConfigMapper.selectByCondition(conditionPO);

        // 将PO对象转换为领域对象
        return poList.stream().map(contentConfigPOAssembler::toEntity).collect(Collectors.toList());
    }

    @Override
    @Cacheable(value = "contentConfig", key = "'type_' + #type + '_enabled'", unless = "#result.isEmpty()")
    public List<ContentConfig> findEnabledByType(ContentType type) {
        List<ContentConfigPO> poList = contentConfigMapper.selectEnabledByType(type.name());
        return poList.stream().map(contentConfigPOAssembler::toEntity).collect(Collectors.toList());
    }

    @Override
    @Cacheable(value = "contentConfig", key = "'parentId_' + #parentId", unless = "#result.isEmpty()")
    public List<ContentConfig> findByParentId(Long parentId) {
        List<ContentConfigPO> poList = contentConfigMapper.selectByParentId(parentId);
        return poList.stream().map(contentConfigPOAssembler::toEntity).collect(Collectors.toList());
    }

    @Override
    @Cacheable(value = "contentConfig", key = "'parentId_' + #parentId + '_enabled'", unless = "#result.isEmpty()")
    public List<ContentConfig> findEnabledByParentId(Long parentId) {
        List<ContentConfigPO> poList = contentConfigMapper.selectEnabledByParentId(parentId);
        return poList.stream().map(contentConfigPOAssembler::toEntity).collect(Collectors.toList());
    }

    @Override
    @Cacheable(value = "contentConfig", key = "'parentId_' + #parentId + '_enabled'", unless = "#result.isEmpty()")
    public List<ContentConfig> findEnabledByParentIds(List<Long> parentIds) {
        List<ContentConfigPO> poList = contentConfigMapper.selectEnabledByParentIds(parentIds);
        return poList.stream().map(contentConfigPOAssembler::toEntity).collect(Collectors.toList());
    }


    @Override
    @Cacheable(value = "contentConfig", key = "'root_contents'", unless = "#result.isEmpty()")
    public List<ContentConfig> findRootContents() {
        List<ContentConfigPO> poList = contentConfigMapper.selectRootContents();
        return poList.stream().map(contentConfigPOAssembler::toEntity).collect(Collectors.toList());
    }

    @Override
    @Cacheable(value = "contentConfig", key = "'root_contents_enabled'", unless = "#result.isEmpty()")
    public List<ContentConfig> findEnabledRootContents() {
        List<ContentConfigPO> poList = contentConfigMapper.selectEnabledRootContents();
        return poList.stream().map(contentConfigPOAssembler::toEntity).collect(Collectors.toList());
    }

    @Override
    @Cacheable(value = "contentConfig", key = "'type_' + #type + '_root_contents'", unless = "#result.isEmpty()")
    public List<ContentConfig> findRootContentsByType(ContentType type) {
        List<ContentConfigPO> poList = contentConfigMapper.selectRootContentsByType(type.name());
        return poList.stream().map(contentConfigPOAssembler::toEntity).collect(Collectors.toList());
    }

    @Override
    @Cacheable(value = "contentConfig", key = "'type_' + #type + '_root_contents_enabled'", unless = "#result.isEmpty()")
    public List<ContentConfig> findEnabledRootContentsByType(ContentType type) {
        List<ContentConfigPO> poList = contentConfigMapper.selectEnabledRootContentsByType(type.name());
        return poList.stream().map(contentConfigPOAssembler::toEntity).collect(Collectors.toList());
    }

    @Override
    @CacheEvict(value = "contentConfig", allEntries = true)
    public boolean deleteById(Long id) {
        return contentConfigMapper.deleteById(id) > 0;
    }
}
