package com.yuanchuan.user.infrastructure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuanchuan.user.infrastructure.po.AccountDevicePO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 账户设备表Mapper接口
 */
@Mapper
public interface AccountDeviceMapper extends BaseMapper<AccountDevicePO> {
    /**
     * 根据账户ID查询设备列表
     *
     * @param accountId 账户ID
     * @return 设备列表
     */
    List<AccountDevicePO> selectByAccountId(@Param("accountId") Long accountId);
    
    /**
     * 根据账户ID和设备ID查询设备
     *
     * @param accountId 账户ID
     * @param deviceId 设备ID
     * @return 设备信息
     */
    AccountDevicePO selectByAccountIdAndDeviceId(@Param("accountId") Long accountId, @Param("deviceId") String deviceId);
    
    /**
     * 更新设备活跃状态
     *
     * @param id 设备ID
     * @param isActive 是否活跃
     * @return 影响行数
     */
    int updateActiveStatus(@Param("id") Long id, @Param("isActive") Boolean isActive);
    
    /**
     * 更新设备验证状态
     *
     * @param id 设备ID
     * @param isVerified 是否已验证
     * @return 影响行数
     */
    int updateVerifiedStatus(@Param("id") Long id, @Param("isVerified") Boolean isVerified);
    
    /**
     * 查询用户的活跃设备数量
     *
     * @param accountId 账户ID
     * @return 活跃设备数量
     */
    int countActiveDevicesByAccountId(@Param("accountId") Long accountId);

    /**
     * 根据id主键批量上传（逻辑）
     * @param deviceIds
     */
    int logicDeleteByIds(@Param("deviceIds") List<Long> deviceIds);
}
