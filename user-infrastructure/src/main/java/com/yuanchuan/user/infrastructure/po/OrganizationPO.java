package com.yuanchuan.user.infrastructure.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 组织表
 * @TableName organization
 */
@Data
@TableName("organization")
public class OrganizationPO {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 上级组织ID
     */
    private Long parentId;

    /**
     * 组织名称
     */
    private String orgName;

    /**
     * 唯一编码
     */
    private String orgCode;

    /**
     * 组织类型（如公司、部门）
     */
    private String orgType;

    /**
     * 组织层级，从1开始
     */
    private Integer orgLevel;

    /**
     * 排序顺序
     */
    private Integer sortOrder;

    /**
     * 组织描述
     */
    private String description;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 更新时间
     */
    private Date updatedAt;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 是否启用 0-禁用(删除) 1-启用
     */
    private Integer active;
}