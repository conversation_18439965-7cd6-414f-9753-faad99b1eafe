package com.yuanchuan.user.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yuanchuan.user.domain.model.Role;
import com.yuanchuan.user.domain.model.RolePermission;
import com.yuanchuan.user.domain.repository.RolePermissionRepository;
import com.yuanchuan.user.infrastructure.mapper.RolePermissionMapper;
import com.yuanchuan.user.infrastructure.po.RolePermissionPO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 角色权限关联仓储实现类
 */
@Repository
public class RolePermissionRepositoryImpl implements RolePermissionRepository {

    @Autowired
    private RolePermissionMapper rolePermissionMapper;

    @Override
    public RolePermission save(RolePermission rolePermission) {
        RolePermissionPO po = toPO(rolePermission);
        if (po.getId() == null) {
            rolePermissionMapper.insert(po);
        } else {
            rolePermissionMapper.updateById(po);
        }
        return toEntity(po);
    }

    @Override
    public Optional<RolePermission> findById(Long id) {
        LambdaQueryWrapper<RolePermissionPO> query = new LambdaQueryWrapper<>();
        query.eq(RolePermissionPO::getId, id)
                .eq(RolePermissionPO::getActive, 1);
        RolePermissionPO po = rolePermissionMapper.selectOne(query);
        return Optional.ofNullable(po).map(this::toEntity);
    }

    @Override
    public List<RolePermission> findByRoleId(Long roleId) {
        LambdaQueryWrapper<RolePermissionPO> query = new LambdaQueryWrapper<>();
        query.eq(RolePermissionPO::getRoleId, roleId)
                .eq(RolePermissionPO::getActive, 1);
        List<RolePermissionPO> poList = rolePermissionMapper.selectList(query);
        return poList.stream().map(this::toEntity).collect(Collectors.toList());
    }

    @Override
    public List<RolePermission> findByPermissionId(Long permissionId) {
        LambdaQueryWrapper<RolePermissionPO> query = new LambdaQueryWrapper<>();
        query.eq(RolePermissionPO::getPermissionId, permissionId)
                .eq(RolePermissionPO::getActive, 1);
        List<RolePermissionPO> poList = rolePermissionMapper.selectList(query);
        return poList.stream().map(this::toEntity).collect(Collectors.toList());
    }

    @Override
    public Optional<RolePermission> findByRoleIdAndPermissionId(Long roleId, Long permissionId) {
        LambdaQueryWrapper<RolePermissionPO> query = new LambdaQueryWrapper<>();
        query.eq(RolePermissionPO::getRoleId, roleId)
                .eq(RolePermissionPO::getPermissionId, permissionId)
                .eq(RolePermissionPO::getActive, 1);
        RolePermissionPO po = rolePermissionMapper.selectOne(query);
        return Optional.ofNullable(po).map(this::toEntity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchSave(List<RolePermission> rolePermissions) {
        try {
            for (RolePermission rolePermission : rolePermissions) {
                save(rolePermission);
            }
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer deleteByRoleId(Long roleId) {
        LambdaQueryWrapper<RolePermissionPO> query = new LambdaQueryWrapper<>();
        query.eq(RolePermissionPO::getRoleId, roleId);
        
        RolePermissionPO po = new RolePermissionPO();
        po.setActive(0);
        
        return rolePermissionMapper.update(po, query);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer deleteByPermissionId(Long permissionId) {
        LambdaQueryWrapper<RolePermissionPO> query = new LambdaQueryWrapper<>();
        query.eq(RolePermissionPO::getPermissionId, permissionId);
        
        RolePermissionPO po = new RolePermissionPO();
        po.setActive(0); // 逻辑删除
        
        return rolePermissionMapper.update(po, query);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteByRoleIdAndPermissionId(Long roleId, Long permissionId) {
        LambdaQueryWrapper<RolePermissionPO> query = new LambdaQueryWrapper<>();
        query.eq(RolePermissionPO::getRoleId, roleId)
                .eq(RolePermissionPO::getPermissionId, permissionId);
        
        RolePermissionPO po = new RolePermissionPO();
        po.setActive(0); // 逻辑删除
        
        return rolePermissionMapper.update(po, query) > 0;
    }


    /**
     * 将领域对象转换为持久化对象
     */
    private RolePermissionPO toPO(RolePermission rolePermission) {
        RolePermissionPO po = new RolePermissionPO();
        BeanUtils.copyProperties(rolePermission, po);
        return po;
    }

    /**
     * 将持久化对象转换为领域对象
     */
    private RolePermission toEntity(RolePermissionPO po) {
        RolePermission rolePermission = new RolePermission();
        BeanUtils.copyProperties(po, rolePermission);
        return rolePermission;
    }
}
