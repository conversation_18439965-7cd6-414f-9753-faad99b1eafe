# 使用自定义日志格式化
logMessageFormat=com.p6spy.engine.spy.appender.CustomLineFormat
# 自定义日志格式
customLogMessageFormat=%(currentTime) | SQL耗时： %(executionTime)ms | 连接信息： %(category)-%(connectionId) | 执行语句： %(sql)
# 设置使用p6spy driver来做代理
driverlist=com.mysql.cj.jdbc.Driver
# 日期格式
dateformat=yyyy-MM-dd HH:mm:ss
# 实际驱动
appender=com.p6spy.engine.spy.appender.Slf4JLogger
# 是否开启慢SQL记录
outagedetection=true
# 慢SQL记录标准，单位秒
outagedetectioninterval=2
# 是否开启日志过滤
filter=true
# 包含QRTZ的语句不打印
exclude=QRTZ