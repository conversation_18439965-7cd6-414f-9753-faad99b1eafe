<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuanchuan.user.infrastructure.mapper.UserThirdPartyAuthMapper">

    <resultMap id="BaseResultMap" type="com.yuanchuan.user.infrastructure.po.UserThirdPartyAuthPO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="bindingId" column="binding_id" jdbcType="BIGINT"/>
            <result property="customerAccountId" column="customer_account_id" jdbcType="BIGINT"/>
            <result property="platform" column="platform" jdbcType="VARCHAR"/>
            <result property="accessToken" column="access_token" jdbcType="VARCHAR"/>
            <result property="refreshToken" column="refresh_token" jdbcType="VARCHAR"/>
            <result property="tokenType" column="token_type" jdbcType="VARCHAR"/>
            <result property="scope" column="scope" jdbcType="VARCHAR"/>
            <result property="expiresAt" column="expires_at" jdbcType="INTEGER"/>
            <result property="refreshExpiresAt" column="refresh_expires_at" jdbcType="INTEGER"/>
            <result property="status" column="status" jdbcType="VARCHAR"/>
            <result property="lastRefreshTime" column="last_refresh_time" jdbcType="TIMESTAMP"/>
            <result property="refreshCount" column="refresh_count" jdbcType="INTEGER"/>
            <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
            <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
            <result property="createdAt" column="created_at" jdbcType="TIMESTAMP"/>
            <result property="updatedAt" column="updated_at" jdbcType="TIMESTAMP"/>
            <result property="active" column="active" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,binding_id,customer_account_id,
        platform,access_token,refresh_token,
        token_type,`scope`,expires_at,
        refresh_expires_at,status,last_refresh_time,
        refresh_count,created_by,updated_by,
        created_at,updated_at,active
    </sql>

    <select id="findByUserId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from user_third_party_auth
        where customer_account_id = #{userId}
        and `active` = 1
    </select>

    <update id="logicDeleteByIds" parameterType="java.util.List">
        UPDATE user_third_party_auth
        SET active = 0
        WHERE id IN
        <foreach collection="partyAuthIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <select id="queryByBindingId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from user_third_party_auth
        where binding_id = #{bindingId}
        and `active` = 1
    </select>

    <select id="selectByAccessToken" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from user_third_party_auth
        where access_token = #{accessToken}
        and `active` = 1
    </select>
</mapper>
