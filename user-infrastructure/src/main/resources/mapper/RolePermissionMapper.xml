<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuanchuan.user.infrastructure.mapper.RolePermissionMapper">

    <resultMap id="BaseResultMap" type="com.yuanchuan.user.infrastructure.po.RolePermissionPO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="roleId" column="role_id" jdbcType="BIGINT"/>
            <result property="permissionId" column="permission_id" jdbcType="BIGINT"/>
            <result property="grantType" column="grant_type" jdbcType="VARCHAR"/>
            <result property="active" column="active" jdbcType="TINYINT"/>
            <result property="createdAt" column="created_at" jdbcType="TIMESTAMP"/>
            <result property="updatedAt" column="updated_at" jdbcType="TIMESTAMP"/>
            <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
            <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,role_id,permission_id,
        grant_type,active,created_at,
        updated_at,created_by,updated_by
    </sql>
</mapper>
