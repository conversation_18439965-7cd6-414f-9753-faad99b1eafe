<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuanchuan.user.infrastructure.mapper.UserPersonMapper">
    <resultMap id="BaseResultMap" type="com.yuanchuan.user.infrastructure.po.UserPersonPO">
        <id column="id" property="id"/>
        <result column="phone" property="phone"/>
        <result column="email" property="email"/>
        <result column="source" property="source"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="created_by" property="createdBy"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="active" property="active"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, phone, email, source, created_at, updated_at, created_by, updated_by, active
    </sql>



    <select id="selectByPhoneAndSource" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM user_person
        WHERE phone = #{phone}
          AND  source = #{source}
          AND active = 1
        LIMIT 1
    </select>

    <select id="selectByEmailAndSource" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM user_person
        WHERE email = #{email}
          AND  source = #{source}
          AND active = 1
        LIMIT 1
    </select>


    <update id="updatePhone">
        UPDATE user_person
        SET phone = #{phone},
            updated_at = NOW()
        WHERE id = #{id} AND active = 1
    </update>

    <update id="updateEmail">
        UPDATE user_person
        SET email = #{email},
            updated_at = NOW()
        WHERE id = #{id} and active = 1
    </update>

    <update id="logicDeleteById" parameterType="java.lang.Long">
        UPDATE user_person set `active` = 0 where id = #{id}
    </update>
</mapper>
