<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuanchuan.user.infrastructure.mapper.AuditLogMapper">

    <resultMap id="BaseResultMap" type="com.yuanchuan.user.infrastructure.po.AuditLogPO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="bizType" column="biz_type" jdbcType="VARCHAR"/>
            <result property="bizId" column="biz_id" jdbcType="BIGINT"/>
            <result property="actionType" column="action_type" jdbcType="VARCHAR"/>
            <result property="operatorId" column="operator_id" jdbcType="BIGINT"/>
            <result property="operatorName" column="operator_name" jdbcType="VARCHAR"/>
            <result property="operateTime" column="operate_time" jdbcType="TIMESTAMP"/>
            <result property="changeFields" column="change_fields" jdbcType="OTHER"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="active" column="active" jdbcType="TINYINT"/>
            <result property="createdAt" column="created_at" jdbcType="TIMESTAMP"/>
            <result property="updatedAt" column="updated_at" jdbcType="TIMESTAMP"/>
            <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
            <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,biz_type,biz_id,
        action_type,operator_id,operator_name,
        operate_time,change_fields,remark,
        active,created_at,updated_at,
        created_by,updated_by
    </sql>
</mapper>
