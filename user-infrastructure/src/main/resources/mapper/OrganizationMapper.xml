<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuanchuan.user.infrastructure.mapper.OrganizationMapper">

    <resultMap id="BaseResultMap" type="com.yuanchuan.user.infrastructure.po.OrganizationPO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="parentId" column="parent_id" jdbcType="BIGINT"/>
            <result property="orgName" column="org_name" jdbcType="VARCHAR"/>
            <result property="orgCode" column="org_code" jdbcType="VARCHAR"/>
            <result property="orgType" column="org_type" jdbcType="VARCHAR"/>
            <result property="orgLevel" column="org_level" jdbcType="INTEGER"/>
            <result property="sortOrder" column="sort_order" jdbcType="INTEGER"/>
            <result property="description" column="description" jdbcType="VARCHAR"/>
            <result property="createdAt" column="created_at" jdbcType="TIMESTAMP"/>
            <result property="updatedAt" column="updated_at" jdbcType="TIMESTAMP"/>
            <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
            <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
            <result property="active" column="active" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,parent_id,org_name,
        org_code,org_type,org_level,
        sort_order,description,created_at,
        updated_at,created_by,updated_by,
        active
    </sql>
</mapper>
