<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuanchuan.user.infrastructure.mapper.LoginLogMapper">
    <resultMap id="BaseResultMap" type="com.yuanchuan.user.infrastructure.po.LoginLogPO">
        <id column="id" property="id"/>
        <result column="account_id" property="accountId"/>
        <result column="device_id" property="deviceId"/>
        <result column="login_type" property="loginType"/>
        <result column="login_status" property="loginStatus"/>
        <result column="ip_address" property="ipAddress"/>
        <result column="user_agent" property="userAgent"/>
        <result column="location" property="location"/>
        <result column="login_at" property="loginAt"/>
        <result column="source" property="source"/>
        <result column="created_by" property="createdBy"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="active" property="active"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, account_id, device_id, login_type, login_status, ip_address, user_agent,
        location, login_at, source, created_by, updated_by, created_at, updated_at, active
    </sql>

    <select id="selectByAccountId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM login_log
        WHERE account_id = #{accountId} AND active = 1
        ORDER BY login_at DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <select id="selectLastLoginByAccountId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM login_log
        WHERE account_id = #{accountId} AND login_status = 'SUCCESS' AND active = 1
        ORDER BY login_at DESC
        LIMIT 1
    </select>

    <select id="selectFailLoginsByAccountId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM login_log
        WHERE account_id = #{accountId}
          AND login_status = 'FAIL'
          AND login_at BETWEEN #{startTime} AND #{endTime}
          AND active = 1
        ORDER BY login_at DESC
    </select>

    <select id="countFailLoginsByAccountId" resultType="int">
        SELECT COUNT(*)
        FROM login_log
        WHERE account_id = #{accountId}
          AND login_status = 'FAIL'
          AND login_at BETWEEN #{startTime} AND #{endTime}
          AND active = 1
    </select>
</mapper>
