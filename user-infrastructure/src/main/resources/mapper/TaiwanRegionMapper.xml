<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuanchuan.user.infrastructure.mapper.TaiwanRegionMapper">

    <resultMap id="BaseResultMap" type="com.yuanchuan.user.infrastructure.po.TaiwanRegionPO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="code" column="code" jdbcType="VARCHAR"/>
            <result property="parentId" column="parent_id" jdbcType="BIGINT"/>
            <result property="level" column="level" jdbcType="TINYINT"/>
            <result property="centerLongitude" column="center_longitude" jdbcType="DECIMAL"/>
            <result property="centerLatitude" column="center_latitude" jdbcType="DECIMAL"/>
            <result property="boundary" column="boundary" jdbcType="OTHER"/>
            <result property="source" column="source" jdbcType="VARCHAR"/>
            <result property="createdAt" column="created_at" jdbcType="TIMESTAMP"/>
            <result property="updatedAt" column="updated_at" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,name,code,
        parent_id,level,center_longitude,
        center_latitude,boundary,source,
        created_at,updated_at
    </sql>

    <select id="findNearestByCoordinate" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from taiwan_region
        where ST_Contains(boundary,  ST_GeomFromText(CONCAT('POINT(', #{latitude}, ' ', #{longitude}, ')')))
    </select>
</mapper>
