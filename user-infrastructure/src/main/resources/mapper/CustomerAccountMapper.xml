<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuanchuan.user.infrastructure.mapper.CustomerAccountMapper">
    <resultMap id="BaseResultMap" type="com.yuanchuan.user.infrastructure.po.CustomerAccountPO">
        <id column="id" property="id"/>
        <result column="user_person_id" property="userPersonId"/>
        <result column="nick_name" property="nickName"/>
        <result column="avatar" property="avatar"/>
        <result column="gender" property="gender"/>
        <result column="birthday" property="birthday"/>
        <result column="password" property="password"/>
        <result column="password_updated_at" property="passwordUpdatedAt"/>
        <result column="address" property="address"/>
        <result column="province_code" property="provinceCode"/>
        <result column="city_code" property="cityCode"/>
        <result column="region_code" property="regionCode"/>
        <result column="province_name" property="provinceName"/>
        <result column="city_name" property="cityName"/>
        <result column="region_name" property="regionName"/>
        <result column="center_longitude" property="centerLongitude"/>
        <result column="center_latitude" property="centerLatitude"/>
        <result column="register_source" property="registerSource"/>
        <result column="register_type" property="registerType"/>
        <result column="register_ip" property="registerIp"/>
        <result column="register_device" property="registerDevice"/>
        <result column="register_time" property="registerTime"/>
        <result column="first_app_login_at" property="firstAppLoginAt"/>
        <result column="user_status" property="userStatus"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="created_by" property="createdBy"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="active" property="active"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, user_person_id, nick_name, avatar, gender, birthday, password, password_updated_at,
        address, province_code, city_code, region_code, province_name, city_name, region_name,
        center_longitude, center_latitude, register_source, register_type,first_app_login_at,register_ip, register_device,
        register_time, user_status, created_at, updated_at, created_by, updated_by, active
    </sql>

    <select id="selectByUserPersonId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM customer_account
        WHERE user_person_id = #{userPersonId} AND active = 1
        LIMIT 1
    </select>

    <update id="updateUserStatus">
        UPDATE customer_account
        SET user_status = #{userStatus},
            updated_at = NOW()
        WHERE id = #{id}
    </update>

    <update id="updatePassword">
        UPDATE customer_account
        SET password = #{password},
            password_updated_at = NOW(),
            updated_at = NOW()
        WHERE id = #{id} and `active` = 1
    </update>

    <update id="updateNickName">
        UPDATE customer_account
        SET nick_name = #{nickName},
            updated_at = NOW()
        WHERE id = #{id} and `active` = 1
    </update>

    <update id="updateAvatar">
        UPDATE customer_account
        SET avatar = #{avatar},
            updated_at = NOW()
        WHERE id = #{id} and `active` = 1
    </update>

    <update id="logicDeleteById" parameterType="java.lang.Long">
        update customer_account set `active` = 0 where id = #{id}
    </update>

    <update id="updateGender">
        UPDATE customer_account
        SET gender = #{gender},
            updated_at = NOW()
        WHERE id = #{id} and `active` = 1
    </update>

    <update id="updateBirthday">
        UPDATE customer_account
        SET birthday = #{birthday},
            updated_at = NOW()
        WHERE id = #{id} and `active` = 1
    </update>

    <update id="updateAddress">
        UPDATE customer_account
        SET address = #{address},
            province_code = #{provinceCode},
            city_code = #{cityCode},
            region_code = #{regionCode},
            province_name = #{provinceName},
            city_name = #{cityName},
            region_name = #{regionName},
            center_longitude = #{longitude},
            center_latitude = #{latitude},
            updated_at = NOW()
        WHERE id = #{id} and `active` = 1
    </update>
</mapper>
