<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuanchuan.user.infrastructure.mapper.ContentConfigMapper">

    <!-- 动态查询内容配置 -->
    <select id="selectByCondition" resultType="com.yuanchuan.user.infrastructure.po.ContentConfigPO">
        SELECT * FROM content_configs
        WHERE active = 1
        <if test="condition.type != null and condition.type != ''">
            AND type = #{condition.type}
        </if>
        <if test="condition.isEnabled != null">
            AND is_enabled = #{condition.isEnabled}
        </if>
        <if test="condition.parentId != null">
            <choose>
                <when test="condition.parentId == 0">
                    AND (parent_id IS NULL OR parent_id = 0)
                </when>
                <otherwise>
                    AND parent_id = #{condition.parentId}
                </otherwise>
            </choose>
        </if>
        <if test="condition.title != null and condition.title != ''">
            AND title LIKE CONCAT('%', #{condition.title}, '%')
        </if>
        <if test="condition.content != null and condition.content != ''">
            AND content LIKE CONCAT('%', #{condition.content}, '%')
        </if>
        <if test="condition.createdBy != null and condition.createdBy != ''">
            AND created_by = #{condition.createdBy}
        </if>
        <if test="condition.updatedBy != null and condition.updatedBy != ''">
            AND updated_by = #{condition.updatedBy}
        </if>
        <if test="condition.createdAt != null">
            AND created_at >= #{condition.createdAt}
        </if>
        <if test="condition.updatedAt != null">
            AND updated_at >= #{condition.updatedAt}
        </if>
        ORDER BY type, sort_order ASC
    </select>

</mapper>
