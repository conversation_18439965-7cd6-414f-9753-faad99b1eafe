<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuanchuan.user.infrastructure.mapper.PermissionMapper">

    <resultMap id="BaseResultMap" type="com.yuanchuan.user.infrastructure.po.PermissionPO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="permissionCode" column="permission_code" jdbcType="VARCHAR"/>
            <result property="permissionName" column="permission_name" jdbcType="VARCHAR"/>
            <result property="type" column="type" jdbcType="VARCHAR"/>
            <result property="parentId" column="parent_id" jdbcType="BIGINT"/>
            <result property="url" column="url" jdbcType="VARCHAR"/>
            <result property="httpMethod" column="http_method" jdbcType="VARCHAR"/>
            <result property="icon" column="icon" jdbcType="VARCHAR"/>
            <result property="componentPath" column="component_path" jdbcType="VARCHAR"/>
            <result property="orderNum" column="order_num" jdbcType="INTEGER"/>
            <result property="isVisible" column="is_visible" jdbcType="TINYINT"/>
            <result property="description" column="description" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="TINYINT"/>
            <result property="active" column="active" jdbcType="TINYINT"/>
            <result property="createdAt" column="created_at" jdbcType="TIMESTAMP"/>
            <result property="updatedAt" column="updated_at" jdbcType="TIMESTAMP"/>
            <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
            <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,permission_code,permission_name,
        `type`,parent_id,url,
        http_method,icon,component_path,
        order_num,is_visible,description,
        `status`,active,created_at,
        updated_at,created_by,updated_by
    </sql>

    <!-- 查询条件SQL片段 -->
    <sql id="Query_Conditions">
        <where>
            AND active = 0
            <if test="parentId != null">
                AND parent_id = #{parentId}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="type != null and type != ''">
                AND type = #{type}
            </if>
            <if test="keyword != null and keyword != ''">
                AND (permission_name LIKE CONCAT('%', #{keyword}, '%')
                     OR permission_code LIKE CONCAT('%', #{keyword}, '%'))
            </if>
        </where>
    </sql>

    <!-- 根据账户ID查询权限列表 -->
    <select id="findByAccountId" resultMap="BaseResultMap">
        SELECT DISTINCT p.id,p.permission_code,p.permission_name,
        p.`type`,p.parent_id,p.url,
        p.http_method,p.icon,p.component_path,
        p. order_num,p.is_visible,p.description,
        p.`status`,p.active,p.created_at,
        p.updated_at,p.created_by,p.updated_by
        FROM permission p
        INNER JOIN role_permission rp ON p.id = rp.permission_id AND rp.active = 1
        INNER JOIN business_account_role bar ON rp.role_id = bar.role_id AND bar.active = 1
        WHERE p.active = 1
          AND p.status = 1
        <if test="accountId != null and accountId != ''">
            AND bar.business_account_id = #{accountId}
        </if>
        ORDER BY p.order_num ASC
    </select>

    <!-- 根据角色ID查询权限列表 -->
    <select id="findByRoleId" resultMap="BaseResultMap">
        SELECT p.<include refid="Base_Column_List"/>
        FROM permission p
        INNER JOIN role_permission rp ON p.id = rp.permission_id
        WHERE p.active = 1
          AND p.status = 1
          AND rp.active = 1
          AND rp.role_id = #{roleId}
        ORDER BY p.order_num ASC
    </select>

    <!-- 分页查询权限列表 -->
    <select id="findByConditionsWithPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM permission
        <include refid="Query_Conditions"/>
        ORDER BY order_num ASC
    </select>

    <!-- 查询权限列表（分页） -->
    <select id="findByConditions" resultType="com.yuanchuan.user.infrastructure.po.PermissionPO">
        SELECT
        p2.id,
        IF(p1.permission_name IS NULL, '', p1.permission_name) AS permissionMenu,
        p2.permission_name,
        p2.permission_code,
        p2.description,
        p2.updated_at,
        p2.updated_by,
        GROUP_CONCAT(DISTINCT r.role_name ORDER BY r.role_name SEPARATOR ', ') AS roleNames,
        GROUP_CONCAT(DISTINCT r.id ORDER BY r.id SEPARATOR ', ') AS roleIds
        FROM
        permission p2
        LEFT JOIN
        permission p1 ON p2.parent_id = p1.id AND p1.type = 'MENU'
        LEFT JOIN
        role_permission rp ON rp.permission_id = p2.id AND rp.active = 1 AND rp.grant_type = 'GRANT'
        LEFT JOIN
        role r ON r.id = rp.role_id AND r.status = 1 AND r.active = 1
        WHERE
        p2.type = 'BUTTON' AND p2.active = 1
        <if test="permissionMenu != null and permissionMenu != ''">
            AND p1.permission_name = #{permissionMenu}
        </if>
        <if test="status != null">
            AND p2.status = #{status}
        </if>
        <if test="keyword != null and keyword != ''">
            AND (
            p2.permission_name LIKE CONCAT('%', #{keyword}, '%')
            OR EXISTS (
            SELECT 1 FROM role_permission rp2
            JOIN role r2 ON rp2.role_id = r2.id
            WHERE rp2.permission_id = p2.id
            AND rp2.active = 1 AND rp2.grant_type = 'GRANT'
            AND r2.status = 1 AND r2.active = 1
            AND r2.role_name LIKE CONCAT('%', #{keyword}, '%')
            )
            )
        </if>
        GROUP BY
        p2.id,
        p1.permission_name,
        p2.permission_name,
        p2.permission_code,
        p2.description,
        p1.order_num,
        p2.order_num
        ORDER BY
        p1.order_num,
        p2.order_num
        <if test="usePaging">
            LIMIT #{offset}, #{limit};
        </if>

    </select>

    <!-- 统计权限数量 -->
    <select id="countByConditions" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM (
        SELECT 1
        FROM permission p2
        LEFT JOIN permission p1 ON p2.parent_id = p1.id AND p1.type = 'MENU'
        LEFT JOIN role_permission rp ON rp.permission_id = p2.id AND rp.active = 1 AND rp.grant_type = 'GRANT'
        LEFT JOIN role r ON r.id = rp.role_id AND r.status = 1 AND r.active = 1
        WHERE p2.type = 'BUTTON' AND p2.active = 1
        <if test="permissionMenu != null and permissionMenu != ''">
            AND p1.permission_name = #{permissionMenu}
        </if>
        <if test="status != null">
            AND p2.status = #{status}
        </if>
        <if test="keyword != null and keyword != ''">
            AND (
            p2.permission_name LIKE CONCAT('%', #{keyword}, '%')
            OR EXISTS (
            SELECT 1 FROM role_permission rp2
            JOIN role r2 ON rp2.role_id = r2.id
            WHERE rp2.permission_id = p2.id
            AND rp2.active = 1 AND rp2.grant_type = 'GRANT'
            AND r2.status = 1 AND r2.active = 1
            AND r2.role_name LIKE CONCAT('%', #{keyword}, '%')
            )
            )
        </if>
        GROUP BY p2.id
        ) tmp;

    </select>

    <select id="findAll" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM permission
        WHERE active = 1
    </select>

    <select id="findByPermissionId" resultType="com.yuanchuan.user.infrastructure.po.PermissionPO">
        SELECT
            p2.id,
            IFNULL(p1.permission_name, '') AS permissionMenu,
            p2.permission_name,
            p2.permission_code,
            p2.description,
            p2.updated_at,
            p2.updated_by,
            GROUP_CONCAT(DISTINCT r.role_name ORDER BY r.role_name SEPARATOR ', ') AS roleNames,
            GROUP_CONCAT(DISTINCT r.id ORDER BY r.id SEPARATOR ', ') AS roleIds
        FROM
            permission p2
                LEFT JOIN permission p1
                          ON p2.parent_id = p1.id AND p1.type = 'MENU'
                LEFT JOIN role_permission rp
                          ON rp.permission_id = p2.id AND rp.active = 1 AND rp.grant_type = 'GRANT'
                LEFT JOIN role r
                          ON r.id = rp.role_id AND r.status = 1 AND r.active = 1
        WHERE
            p2.id = #{permissionId}
            AND p2.active = 1
        GROUP BY
            p2.id,
            p1.permission_name,
            p2.permission_name,
            p2.permission_code,
            p2.description,
            p1.order_num,
            p2.order_num
        ORDER BY
            p1.order_num,
            p2.order_num
    </select>

</mapper>
