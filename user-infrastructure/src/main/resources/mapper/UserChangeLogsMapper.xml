<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuanchuan.user.infrastructure.mapper.UserChangeLogsMapper">

    <resultMap id="BaseResultMap" type="com.yuanchuan.user.infrastructure.po.UserChangeLogsPO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="userId" column="user_id" jdbcType="BIGINT"/>
        <result property="oldValue" column="old_value" jdbcType="OTHER"/>
        <result property="newValue" column="new_value" jdbcType="OTHER"/>
        <result property="deviceId" column="device_id" jdbcType="VARCHAR"/>
        <result property="source" column="source" jdbcType="VARCHAR"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="createdAt" column="created_at" jdbcType="TIMESTAMP"/>
        <result property="updatedAt" column="updated_at" jdbcType="TIMESTAMP"/>
        <result property="active" column="active" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,user_id,old_value,
        new_value,device_id,source,
        created_by,updated_by,created_at,
        updated_at,active
    </sql>
</mapper>
