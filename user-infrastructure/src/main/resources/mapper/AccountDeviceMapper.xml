<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuanchuan.user.infrastructure.mapper.AccountDeviceMapper">
    <resultMap id="BaseResultMap" type="com.yuanchuan.user.infrastructure.po.AccountDevicePO">
        <id column="id" property="id"/>
        <result column="account_id" property="accountId"/>
        <result column="device_id" property="deviceId"/>
        <result column="device_type" property="deviceType"/>
        <result column="device_name" property="deviceName"/>
        <result column="os_version" property="osVersion"/>
        <result column="app_version" property="appVersion"/>
        <result column="ip_address" property="ipAddress"/>
        <result column="user_agent" property="userAgent"/>
        <result column="is_verified" property="isVerified"/>
        <result column="is_active" property="isActive"/>
        <result column="last_login_at" property="lastLoginAt"/>
        <result column="source" property="source"/>
        <result column="created_by" property="createdBy"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="active" property="active"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, account_id, device_id, device_type, device_name, os_version, app_version,
        ip_address, user_agent, is_verified, is_active, last_login_at, source,
        created_by, updated_by, created_at, updated_at, active
    </sql>

    <select id="selectByAccountId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM account_device
        WHERE account_id = #{accountId} AND active = 1
    </select>

    <select id="selectByAccountIdAndDeviceId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM account_device
        WHERE account_id = #{accountId} AND device_id = #{deviceId} AND active = 1
        LIMIT 1
    </select>

    <update id="updateActiveStatus">
        UPDATE account_device
        SET is_active = #{isActive},
            updated_at = NOW()
        WHERE id = #{id}
    </update>

    <update id="updateVerifiedStatus">
        UPDATE account_device
        SET is_verified = #{isVerified},
            updated_at = NOW()
        WHERE id = #{id}
    </update>

    <select id="countActiveDevicesByAccountId" resultType="int">
        SELECT COUNT(*)
        FROM account_device
        WHERE account_id = #{accountId} AND is_active = 1 AND active = 0
    </select>

    <update id="logicDeleteByIds" parameterType="java.util.List">
        update account_device set `active` = 0
        where id in
        <foreach collection="deviceIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
</mapper>
