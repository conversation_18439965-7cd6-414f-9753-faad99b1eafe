<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuanchuan.user.infrastructure.mapper.OperationAccountMapper">

    <!-- 查询运营账号列表（关联用户信息和角色信息） -->
    <select id="queryOperationAccountsWithUserInfo" resultType="com.yuanchuan.user.infrastructure.po.BusinessAccountPO">
        SELECT DISTINCT
            ba.id,
            ba.user_person_id,
            ba.account_name,
            ba.account_code,
            ba.account_type,
            ba.account_status,
            ba.created_at,
            ba.updated_at,
            ba.created_by,
            ba.updated_by,
            ba.active,
            ba.org_id
        FROM business_account ba
        INNER JOIN user_person up ON ba.user_person_id = up.id
        <if test="roleIds != null and roleIds != ''">
            INNER JOIN business_account_role bar ON ba.id = bar.business_account_id
            INNER JOIN role r ON bar.role_id = r.id
        </if>
        WHERE ba.account_type = 2
        AND ba.active = 1
        AND up.active = 1
        <if test="accountStatus != null">
            AND ba.account_status = #{accountStatus}
        </if>
        <if test="roleIds != null and roleIds != ''">
            AND r.id in
          <foreach collection="roleIds" item="item" open="(" separator="," close=")">
              #{item}
          </foreach>
            AND r.active = 1
            AND r.status = 1
        </if>
        <if test="keyword != null and keyword != ''">
            AND (
                up.phone LIKE CONCAT('%', #{keyword}, '%')
                OR up.email LIKE CONCAT('%', #{keyword}, '%')
                OR ba.account_name LIKE CONCAT('%', #{keyword}, '%')
            )
        </if>
        ORDER BY ba.created_at DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <!-- 统计运营账号数量 -->
    <select id="countOperationAccountsWithUserInfo" resultType="java.lang.Long">
        SELECT COUNT(DISTINCT ba.id)
        FROM business_account ba
        INNER JOIN user_person up ON ba.user_person_id = up.id
        <if test="roleIds != null and roleIds != ''">
            INNER JOIN business_account_role bar ON ba.id = bar.business_account_id
            INNER JOIN role r ON bar.role_id = r.id
        </if>
        WHERE ba.account_type = 2
        AND ba.active = 1
        AND up.active = 1
        <if test="accountStatus != null">
            AND ba.account_status = #{accountStatus}
        </if>
        <if test="roleIds != null and roleIds != ''">
            AND r.id in
            <foreach collection="roleIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            AND r.active = 1
            AND r.status = 1
        </if>
        <if test="keyword != null and keyword != ''">
            AND (
                up.phone LIKE CONCAT('%', #{keyword}, '%')
                OR up.email LIKE CONCAT('%', #{keyword}, '%')
                OR ba.account_name LIKE CONCAT('%', #{keyword}, '%')
            )
        </if>
    </select>

</mapper>
