<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuanchuan.user.infrastructure.mapper.UserThirdPartyBindingMapper">

    <resultMap id="BaseResultMap" type="com.yuanchuan.user.infrastructure.po.UserThirdPartyBindingPO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="customerAccountId" column="customer_account_id" jdbcType="BIGINT"/>
            <result property="platform" column="platform" jdbcType="VARCHAR"/>
            <result property="externalUserId" column="external_user_id" jdbcType="VARCHAR"/>
            <result property="nickName" column="nick_name" jdbcType="VARCHAR"/>
            <result property="avatar" column="avatar" jdbcType="VARCHAR"/>
            <result property="gender" column="gender" jdbcType="VARCHAR"/>
            <result property="bindingStatus" column="binding_status" jdbcType="VARCHAR"/>
            <result property="bindTime" column="bind_time" jdbcType="TIMESTAMP"/>
            <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
            <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
            <result property="createdAt" column="created_at" jdbcType="TIMESTAMP"/>
            <result property="updatedAt" column="updated_at" jdbcType="TIMESTAMP"/>
            <result property="active" column="active" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,customer_account_id,platform,
        external_user_id,nick_name,
        avatar,gender,binding_status,
        bind_time,created_by,updated_by,
        created_at,updated_at,active
    </sql>

    <select id="findByUserId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from user_third_party_binding
        where customer_account_id = #{userId}
        and `active` = 1
    </select>

    <update id="logicDeleteByIds" parameterType="java.util.List">
        update user_third_party_binding set `active` = 0
        where id
        <foreach collection="bingdingIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="logicDeleteById" parameterType="java.lang.Long">
        update user_third_party_binding set `active` = 0
        where id = #{id}
    </update>

    <select id="selectByPlatformAndExternalUserId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from user_third_party_binding
        where platform = #{platform}
        and external_user_id = #{externalUserId}
        and `active` = 1
        limit 1
    </select>

    <select id="selectByCustomerAccountIdAndExternalUserId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from user_third_party_binding
        where customer_account_id = #{customerAccountId}
        and external_user_id = #{externalUserId}
    </select>
</mapper>
