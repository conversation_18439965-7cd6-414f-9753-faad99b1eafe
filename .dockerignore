# 排除不需要的文件和目錄

# 源代碼（JAR已經構建好了）
*/src/
.mvn/
mvnw
mvnw.cmd
pom.xml
*/pom.xml

# 構建過程文件（除了bootstrap模块的JAR）
*/target/classes/
*/target/generated-sources/
*/target/maven-archiver/
*/target/maven-status/
*/target/test-classes/
*/target/surefire-reports/
user-api/target/
user-application/target/
user-domain/target/
user-infrastructure/target/
user-common/target/

# 保留 bootstrap 模块的 JAR
!user-bootstrap/target/*.jar

# 文檔和配置文件
*.md
HELP.md
README.md
手動部署操作指南-完整版.md
自託管代理設置指南.md
CI-CD-流程文档.md
MAVEN_GITHUB_PACKAGES.md
frch*.md
Azure*.md
Windows*.md
MySQL*.md
GoogleSearchTextReturn.md
*.sql

# 部署腳本和配置（安全考慮）
deploy-config.sh
setup-acr-cleanup.sh
deployment-log.txt
settings.xml.example
connect-azure-redis.sh

# Azure DevOps配置
azure-pipelines.yml
acr-service-connection.json

# Kubernetes配置（在部署时单独管理）
k8s/

# Git和IDE文件
.git/
.gitignore
.idea/
.vscode/
*.iml

# 其他项目文件
shop-api/
elasticsearch-azure-deployment/
代码转换/
ddd-design-docs/

# 日誌文件
*.log
logs/

# 臨時文件
*.tmp
*.temp
.DS_Store
Thumbs.db 