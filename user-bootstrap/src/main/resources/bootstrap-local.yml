server:
  port: 8082

spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
#    url: ******************************************************************************************************************************
    url: *******************************************************************************************************************************
    username: liutao
    password: 4@1CS4mW
  data:
    redis:
      host: *************
      port: 16379
      password: O*mZz1rrA!S5q
      database: 0

zookeeper:
  address: **************:2181
  name: com.yuanchuan.user

dubbo:
  application:
    name: com.yuanchuan.user
    qos-enable: true
    qos-port: 22222
    qos-accept-foreign-ip: false
  protocol:
    name: dubbo
    port: 20801
    host: 0.0.0.0

  scan:
    base-packages: com.yuanchuan.user
  registry:
    address: zookeeper://**************:2181
    register: true
    timeout: 60000
    parameters:
      blockUntilConnectedWait: 60000
      retryIntervalMillis: 5000
      retryTimes: 5
      sessionTimeoutMs: 180000
      connectionTimeoutMs: 30000
    client: curator
#  group: user-local
  group: user-local-juno
  provider:
    payload: 83886080
  consumer:
    timeout: 600000
    check: false


mybatis-plus:
  mapper-locations: classpath*:mapper/**/*.xml
  type-aliases-package: com.yuanchuan.user.dto
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

logging:
  level:
    org.apache.dubbo: DEBUG
    org.apache.zookeeper: DEBUG
    # 登录日志相关类的日志级别配置
    com.yuanchuan.user.application.aspect.LoginLogAspect: DEBUG
    com.yuanchuan.user.application.service.impl.LoginLogApplicationServiceImpl: DEBUG
    com.yuanchuan.user.domain.service.impl.LoginLogDomainServiceImpl: DEBUG


justauth:
  enabled: true
  extend:
    enum-class: com.yuanchuan.user.domain.auth.FetnetAuthSource
    config:
      TEST:
        request-class: com.yuanchuan.user.domain.auth.FetnetAuthRequest
        client-id: xxxxxx
        client-secret: xxxxxxxx
        redirect-uri: http://oauth.xkcoding.com/demo/oauth/test/callback
  type:
    GOOGLE:
      client-id: 172554427456-iovuuuhn6ul33osjo39jnj6k45opiqnl.apps.googleusercontent.com
      client-secret: 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
      redirect-uri: http://dev.goldenmilestech.com/api/v1/google/callback
#      client-id: 172554427456-6hq3n9qa8dtpv3v093bdvl1g9bvsquk3.apps.googleusercontent.com
#      client-secret: 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
#      redirect-uri: http://localhost:8091/api/v1/google/callback

    APPLE:
      client-id: 10**********6
      client-secret: 1f7d08**********5b7**********29e
      redirect-uri: http://localhost:8091/api/v1/google/callback
    LINE:
      client-id: 2007426546
      client-secret: 2193b13d2194d19301fa7813eadd869f
      redirect-uri: http://localhost:8091/api/v1/line/callback
# 心生活
fetnet:
  base-url: https://login2-test.fetnet.net
  authorize-url: ${fetnet.base-url}/mga/sps/oauth/oauth20/authorize
  token-url: ${fetnet.base-url}/mga/sps/oauth/oauth20/token
  revoke-url: ${fetnet.base-url}/mga/sps/oauth/oauth20/revoke





# ============================================================================
# 登录日志功能配置（本地环境）
# ============================================================================
app:
  login-log:
    # 是否启用登录日志记录功能（总开关）
    enabled: true

    # 是否启用AOP方式记录（通过@LoginLog注解自动记录）
    aop-enabled: true

    # 是否异步记录日志（推荐开启，避免影响登录性能）
    async: true

    # 是否记录登录成功日志
    record-success: true

    # 是否记录登录失败日志
    record-failure: true

    # 是否启用IP地址解析（本地环境禁用，避免网络请求）
    ip-location-enabled: false

    # 异常登录检测阈值（本地环境宽松设置，方便调试）
    abnormal-threshold: 10

    # 异常登录检测时间窗口（小时）
    abnormal-time-window: 1

    # 登录日志数据保留天数（本地环境较短，节省存储空间）
    retention-days: 7

    # 线程池配置（本地环境使用较小的线程池）
    thread-pool:
      core-size: 2
      max-size: 4
      queue-capacity: 50
      keep-alive-seconds: 60
      thread-name-prefix: "LoginLog-Local-"
      wait-for-tasks-to-complete-on-shutdown: true
      await-termination-seconds: 30

# ============================================================================
# dubbo 消费者group配置
# ============================================================================
#dubbo.consumer.group.user: user-local
dubbo.consumer.group.user: user-local-juno
dubbo.consumer.group.shop: shop-local
dubbo.consumer.group.order: order-local
dubbo.consumer.group.reservation: reservation-local
dubbo.consumer.group.review: review-local
dubbo.consumer.group.marketing: marketing-local
#dubbo.consumer.group.authentication: authentication-local
dubbo.consumer.group.authentication: authentication-local-juno
