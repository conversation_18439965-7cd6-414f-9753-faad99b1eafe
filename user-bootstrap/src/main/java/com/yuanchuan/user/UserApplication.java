package com.yuanchuan.user;

import org.apache.curator.RetryPolicy;
import org.apache.curator.framework.CuratorFramework;
import org.apache.curator.framework.CuratorFrameworkFactory;
import org.apache.curator.framework.recipes.cache.NodeCache;
import org.apache.curator.retry.ExponentialBackoffRetry;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;


@MapperScan(basePackages = {
        "com.yuanchuan.user.infrastructure.mapper"})
@EnableDubbo
@ComponentScan(
        basePackages ={
            "com.yuanchuan.user.*",
            "com.yuanchuan.user.domain.repository",
            "com.yuanchuan.user.infrastructure.repository",
            "com.yuanchuan.user.application.service.impl",
            "com.yuanchuan.common.*"},
        excludeFilters = {
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.yuanchuan.common.event.*")})
@SpringBootApplication
public class UserApplication {

    @Value("${zookeeper.address}")
    private String zookeeperAddress;

    @Bean(initMethod = "start", destroyMethod = "close")
    public CuratorFramework curatorFramework() {
        RetryPolicy retryPolicy = new ExponentialBackoffRetry(1000, 3);
        return CuratorFrameworkFactory.builder()
                .connectString(zookeeperAddress)
                .sessionTimeoutMs(60000)
                .connectionTimeoutMs(15000)
                .retryPolicy(retryPolicy)
                .build();
    }

    @Bean(destroyMethod = "close")
    public NodeCache nodeCache(CuratorFramework curatorFramework) throws Exception {
        NodeCache nodeCache = new NodeCache(curatorFramework, "/config");
        nodeCache.start();
        return nodeCache;
    }

    public static void main(String[] args) {
        SpringApplication.run(UserApplication.class, args);
    }
}
