package com.yuanchuan.user.context.enums;

/**
 * 内容类型枚举
 * 用于区分不同功能类型的内容
 */
public enum ContentType {
    /**
     * 协议类型 - 包含各种法律协议、服务条款等
     */
    AGREEMENT("协议"),

    /**
     * 帮助类型 - 包含常见问题、操作指南等
     */
    HELP("帮助内容"),

    /**
     * 公告类型 - 包含系统公告、活动通知等
     */
    ANNOUNCEMENT("公告"),

    /**
     * 关于类型 - 包含关于我们、公司介绍等
     */
    ABOUT("关于我们"),

    /**
     * 反馈类型 - 包含用户反馈、建议等
     */
    FEEDBACK("反馈与建议"),

    /**
     * 其他类型 - 不属于上述类型的其他内容
     */
    OTHER("其他内容");

    private final String description;

    ContentType(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }
}
