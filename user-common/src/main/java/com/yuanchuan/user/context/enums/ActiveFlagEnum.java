package com.yuanchuan.user.context.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 注册类型枚举
 */
@Schema(description = "注册类型")
public enum ActiveFlagEnum {
    /**
     * 禁用(删除)
     */
    DISABLE(0,"禁用"),

    /**
     * 启用(未删除）
     */
    ENABLE(1,"启用"),
    ;

    /**
     * 编码
     */
    private final int code;

    /**
     * 中文描述
     */
    private final String description;

    ActiveFlagEnum(int code,String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 获取编码
     */
    @JsonValue
    public int getCode() {
        return code;
    }

    /**
     * 获取中文描述
     */
    public String getDescription() {
        return description;
    }

    /**
     * 根据编码获取枚举实例
     */
    @JsonCreator
    public static ActiveFlagEnum fromCode(int code) {
        for (ActiveFlagEnum type : ActiveFlagEnum.values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("无效的注册类型编码: " + code);
    }
}
