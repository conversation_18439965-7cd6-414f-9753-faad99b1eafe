package com.yuanchuan.user.context.enums;

/**
 * @ClassName AccountType
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/5/16 17:23
 * @Version 1.0
 */

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 账户类型枚举
 */
@Schema(description = "账户类型枚举")
public enum AccountType {
    /**
     * 商户
     */
    SHOP(1, "商户"),

    /**
     * 运营
     */
    ADMIN(2, "运营");

    private final int code;
    private final String description;

    AccountType(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}