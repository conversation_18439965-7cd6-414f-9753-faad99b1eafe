package com.yuanchuan.user.context.enums;

/**
 * @ClassName AccountStatus
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/5/16 17:24
 * @Version 1.0
 */

/**
 * 账户状态枚举
 */
public enum AccountStatus {
    /**
     * 正常
     */
    ACTIVE(1, "正常"),

    /**
     * 锁定
     */
    LOCKED(2, "锁定"),

    /**
     * 禁用
     */
    DISABLED(3, "禁用");

    private final int code;
    private final String description;

    AccountStatus(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}