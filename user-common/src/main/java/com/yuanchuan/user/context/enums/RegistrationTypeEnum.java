package com.yuanchuan.user.context.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 注册类型枚举
 */
@Schema(description = "注册类型")
public enum RegistrationTypeEnum {
    /**
     * 手机号注册
     */
    PHONE_REGISTRATION(0, "PHONE", "手机号注册"),

    /**
     * 邮箱注册绑定手机号
     */
    EMAIL_BINDING(1, "EMAIL", "邮箱注册绑定手机号"),

    /**
     * 第三方注册绑定手机号
     */
    THIRD_BINDING(2, "THIRD", "第三方注册绑定手机号");

    /**
     * 编码
     */
    private final int code;

    /**
     * 英文名称
     */
    private final String name;

    /**
     * 中文描述
     */
    private final String description;

    RegistrationTypeEnum(int code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }

    /**
     * 获取编码
     */
    @JsonValue
    public int getCode() {
        return code;
    }

    /**
     * 获取英文名称
     */
    public String getName() {
        return name;
    }

    /**
     * 获取中文描述
     */
    public String getDescription() {
        return description;
    }

    /**
     * 根据编码获取枚举实例
     */
    @JsonCreator
    public static RegistrationTypeEnum fromCode(int code) {
        for (RegistrationTypeEnum type : RegistrationTypeEnum.values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("无效的注册类型编码: " + code);
    }

    /**
     * 根据英文名称获取枚举实例
     */
    public static RegistrationTypeEnum fromName(String name) {
        if (name == null) {
            return null;
        }

        for (RegistrationTypeEnum type : RegistrationTypeEnum.values()) {
            if (type.getName().equalsIgnoreCase(name)) {
                return type;
            }
        }
        throw new IllegalArgumentException("无效的注册类型名称: " + name);
    }
}
