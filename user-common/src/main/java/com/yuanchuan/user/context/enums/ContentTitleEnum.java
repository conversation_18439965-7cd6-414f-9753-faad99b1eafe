package com.yuanchuan.user.context.enums;

/**
 * 内容标题枚举
 * 用于规范固定类型内容的标题
 * 注意：帮助类型的内容标题不在此定义，而是通过数据库配置
 */
public enum ContentTitleEnum {
    // 协议类型相关标题
    AGREEMENT_USER("用户协议"),
    AGREEMENT_SERVICE("服务条款"),
    AGREEMENT_PRIVACY("隐私政策"),
    AGREEMENT_DATA_COLLECTION("数据收集说明"),
    AGREEMENT_DATA_USAGE("数据使用说明"),

    // 公告类型相关标题
    ANNOUNCEMENT_SYSTEM("系统公告"),
    ANNOUNCEMENT_ACTIVITY("活动通知"),
    ANNOUNCEMENT_MAINTENANCE("维护通知"),

    // 关于类型相关标题
    ABOUT_US("关于我们"),
    ABOUT_COMPANY("公司介绍"),
    ABOUT_CONTACT("联系我们"),

    // 反馈类型相关标题
    FEEDBACK_SUGGESTION("建议反馈"),
    FEEDBACK_BUG("问题报告"),
    FEEDBACK_COMPLAINT("投诉建议");

    private final String title;

    ContentTitleEnum(String title) {
        this.title = title;
    }

    public String getTitle() {
        return title;
    }

    /**
     * 根据标题文本获取枚举值
     *
     * @param title 标题文本
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static ContentTitleEnum fromTitle(String title) {
        for (ContentTitleEnum titleEnum : ContentTitleEnum.values()) {
            if (titleEnum.getTitle().equals(title)) {
                return titleEnum;
            }
        }
        return null;
    }

    /**
     * 获取与指定内容类型相关的所有标题枚举
     * 注意：帮助类型的内容标题不在此定义，而是通过数据库配置
     *
     * @param contentType 内容类型
     * @return 与指定内容类型相关的标题枚举数组
     */
    public static ContentTitleEnum[] getByContentType(ContentType contentType) {
        if (contentType == null) {
            return new ContentTitleEnum[0];
        }

        switch (contentType) {
            case AGREEMENT:
                return new ContentTitleEnum[]{
                        AGREEMENT_USER,
                        AGREEMENT_SERVICE,
                        AGREEMENT_PRIVACY,
                        AGREEMENT_DATA_COLLECTION,
                        AGREEMENT_DATA_USAGE
                };
            case HELP:
                // 帮助类型的内容标题不在此定义，而是通过数据库配置
                return new ContentTitleEnum[0];
            case ANNOUNCEMENT:
                return new ContentTitleEnum[]{
                        ANNOUNCEMENT_SYSTEM,
                        ANNOUNCEMENT_ACTIVITY,
                        ANNOUNCEMENT_MAINTENANCE
                };
            case ABOUT:
                return new ContentTitleEnum[]{
                        ABOUT_US,
                        ABOUT_COMPANY,
                        ABOUT_CONTACT
                };
            case FEEDBACK:
                return new ContentTitleEnum[]{
                        FEEDBACK_SUGGESTION,
                        FEEDBACK_BUG,
                        FEEDBACK_COMPLAINT
                };
            default:
                return new ContentTitleEnum[0];
        }
    }
}
