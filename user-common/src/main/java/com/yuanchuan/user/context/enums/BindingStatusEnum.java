package com.yuanchuan.user.context.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * @ClassName UserSource
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/5/12 16:37
 * @Version 1.0
 */

/**
 * 三方绑定枚举
 */
@Schema(description = "三方绑定枚举")
public enum BindingStatusEnum {


    /**
     * C端用户登录
     */
    BINDING("BINDING", "绑定"),

    /**
     * 商户登录
     */
    UNBIND("UNBIND", "解绑");

    /**
     * 编码
     */
    private final String code;

    /**
     * 中文描述
     */
    private final String description;

    BindingStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 获取编码
     */
    @JsonValue
    public String getCode() {
        return code;
    }


    /**
     * 获取中文描述
     */
    public String getDescription() {
        return description;
    }

    /**
     * 根据编码获取枚举实例
     */
    @JsonCreator
    public static BindingStatusEnum fromCode(String code) {
        for (BindingStatusEnum source : BindingStatusEnum.values()) {
            if (source.getCode() == code) {
                return source;
            }
        }
        throw new IllegalArgumentException("无效的用户来源编码: " + code);
    }
}
