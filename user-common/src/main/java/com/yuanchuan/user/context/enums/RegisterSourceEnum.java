package com.yuanchuan.user.context.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 注册来源枚举
 * 表示用户从哪个平台/渠道注册（app、H5、小程序等）
 */
@Schema(description = "注册来源")
public enum RegisterSourceEnum {
    /**
     * APP应用
     */
    APP(0, "APP", "APP应用"),

    /**
     * H5网页
     */
    H5(1, "H5", "H5网页"),

    /**
     * 小程序
     */
    MINI_PROGRAM(2, "MINI_PROGRAM", "小程序"),

    /**
     * 网站
     */
    WEB(3, "WEB", "网站"),

    /**
     * 管理后台
     */
    ADMIN(4, "ADMIN", "管理后台");

    /**
     * 编码
     */
    private final int code;

    /**
     * 英文名称
     */
    private final String name;

    /**
     * 中文描述
     */
    private final String description;

    RegisterSourceEnum(int code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }

    /**
     * 获取编码
     */
    @JsonValue
    public int getCode() {
        return code;
    }

    /**
     * 获取英文名称
     */
    public String getName() {
        return name;
    }

    /**
     * 获取中文描述
     */
    public String getDescription() {
        return description;
    }

    /**
     * 根据编码获取枚举实例
     */
    @JsonCreator
    public static RegisterSourceEnum fromCode(int code) {
        for (RegisterSourceEnum source : RegisterSourceEnum.values()) {
            if (source.getCode() == code) {
                return source;
            }
        }
        throw new IllegalArgumentException("无效的注册来源编码: " + code);
    }

    /**
     * 根据英文名称获取枚举实例
     */
    public static RegisterSourceEnum fromName(String name) {
        if (name == null) {
            return null;
        }

        for (RegisterSourceEnum source : RegisterSourceEnum.values()) {
            if (source.getName().equalsIgnoreCase(name)) {
                return source;
            }
        }
        throw new IllegalArgumentException("无效的注册来源名称: " + name);
    }
}
