package com.yuanchuan.user.context.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 短信业务类型枚举
 */
@Schema(description = "业务类型用来发送手机短信或者邮箱key区分")
public enum SmsAndEmailBusinessType {
    /**
     * 注册/登录
     */
    REGISTER_LOGIN_CODE(0, "REGISTER_LOGIN_CODE", "C端注册"),

    /**
     * C端换绑
     */
    PHONE_UNBIND_CODE(1, "PHONE_UNBIND_CODE", "C端换绑"),

    /**
     * 商户
     */
    MERCHANT_CODE(2, "MERCHANT_CODE", "商户"),

    /**
     * 商户换绑
     */
    MERCHANT_UNBIND_CODE(3, "MERCHANT_UNBIND_CODE", "商户换绑"),

    /**
     * 商户上传资料
     */
    MERCHANT_UPLOAD_CODE(4, "MERCHANT_UPLOAD_CODE", "商户上传资料"),

    /**
     * 二次验证-手机号
     */
    SECONDARY_PHONE_VERIFICATION(5, "SECONDARY_PHONE_VERIFICATION", "二次验证-手机号"),

    /**
     * 二次验证-邮箱
     */
    SECONDARY_EMAIL_VERIFICATION(6, "SECONDARY_EMAIL_VERIFICATION", "二次验证-邮箱");

    /**
     * 编码
     */
    private final int code;

    /**
     * 英文名称
     */
    private final String name;

    /**
     * 中文描述
     */
    private final String description;

    SmsAndEmailBusinessType(int code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }

    /**
     * 获取编码
     */
    @JsonValue
    public int getCode() {
        return code;
    }

    /**
     * 获取英文名称
     */
    public String getName() {
        return name;
    }

    /**
     * 获取中文描述
     */
    public String getDescription() {
        return description;
    }

    /**
     * 根据编码获取枚举实例
     */
    @JsonCreator
    public static SmsAndEmailBusinessType fromCode(int code) {
        for (SmsAndEmailBusinessType type : SmsAndEmailBusinessType.values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("无效的短信业务类型编码: " + code);
    }

    /**
     * 根据英文名称获取枚举实例
     */
    public static SmsAndEmailBusinessType fromName(String name) {
        if (name == null) {
            return null;
        }

        for (SmsAndEmailBusinessType type : SmsAndEmailBusinessType.values()) {
            if (type.getName().equalsIgnoreCase(name)) {
                return type;
            }
        }
        throw new IllegalArgumentException("无效的短信业务类型名称: " + name);
    }
}
