package com.yuanchuan.user.application.service.impl;

import com.yuanchuan.common.exception.BusinessException;
import com.yuanchuan.common.enums.users.UsersErrorCode;
import com.yuanchuan.common.utils.RedisUtil;
import com.yuanchuan.common.constant.user.UserLoginRedisKeys;
import com.yuanchuan.user.application.service.DeviceManagementApplicationService;
import com.yuanchuan.user.api.dto.AccountDeviceDTO;
import com.yuanchuan.user.application.service.assembler.AccountDeviceDTOAssembler;
import com.yuanchuan.user.domain.model.UserPerson;
import com.yuanchuan.user.domain.model.AccountDevice;
import com.yuanchuan.user.domain.repository.AccountDeviceRepository;
import com.yuanchuan.user.domain.service.UserPersonDomainService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 设备管理应用服务实现类
 * 
 * 设备管理相关的逻辑，
 * 。
 * 
 * <AUTHOR>
 * @date 2025/1/20
 * @version 1.0
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class DeviceManagementApplicationServiceImpl implements DeviceManagementApplicationService {

    @Autowired
    private UserPersonDomainService userPersonDomainService;
    
    @Autowired
    private AccountDeviceRepository accountDeviceRepository;
    
    @Autowired
    private RedisUtil redisUtil;

    @Override
    public boolean checkIsNewDevice(Long accountId, String deviceId) {
        // 验证设备ID有效性
        if (StringUtils.isBlank(deviceId)) {
            throw new BusinessException(UsersErrorCode.DEVICE_ID_REQUIRED.getCode(), 
                                      UsersErrorCode.DEVICE_ID_REQUIRED.getMsg());
        }

        try {
            // 1. 先检查Redis缓存
            String cacheKey = UserLoginRedisKeys.getDeviceLoginCacheKey(accountId, deviceId);
            String cachedValue = redisUtil.get(cacheKey);
            if (StringUtils.isNotBlank(cachedValue)) {
                log.debug("设备状态缓存命中: accountId={}, deviceId={}, cached={}", 
                        accountId, deviceId, cachedValue);
                return false; // 缓存中存在，说明是已知设备
            }

            // 2. 查询数据库中的设备记录
            List<AccountDevice> devices = accountDeviceRepository.findByAccountId(accountId);
            boolean deviceExists = devices.stream()
                    .anyMatch(device -> deviceId.equals(device.getDeviceId()));

            if (deviceExists) {
                // 设备存在，更新缓存
                cacheDeviceLogin(accountId, deviceId);
                return false;
            }

            log.info("检测到新设备: accountId={}, deviceId={}", accountId, deviceId);
            return true;

        } catch (Exception e) {
            log.error("检查设备状态失败: accountId={}, deviceId={}", accountId, deviceId, e);
            // 出现异常时，为了安全起见，认为是新设备
            return true;
        }
    }

    @Override
    public void bindUserDevice(UserPerson userPerson, AccountDeviceDTO deviceInfo) {
        if (userPerson == null || deviceInfo == null || deviceInfo.getDeviceId() == null) {
            // 如果用户信息或设备信息为空，则不进行设备绑定
            return;
        }

        try {
            // 将DTO转换为领域对象
            AccountDevice accountDevice = AccountDeviceDTOAssembler.convertToDomain(deviceInfo);
            accountDevice.setAccountId(userPerson.getCustomerAccount().getId());
            accountDevice.setSource(userPerson.getSource());
            
            // 调用领域服务绑定设备
            userPersonDomainService.bindDevice(accountDevice);

            // 绑定成功后缓存设备状态
            //cacheDeviceLogin(userPerson.getCustomerAccount().getId(), deviceInfo.getDeviceId());

            log.info("用户设备绑定成功，用户ID: {}, 设备ID: {}", userPerson.getId(), deviceInfo.getDeviceId());
            
        } catch (Exception e) {
            // 设备绑定失败不应影响登录流程，启动异步重试
            log.error("用户设备绑定失败，将异步重试，用户ID: {}, 设备ID: {}, 错误信息: {}",
                    userPerson.getId(), deviceInfo.getDeviceId(), e.getMessage(), e);
            
            // 异步重试设备绑定
            asyncRetryDeviceBinding(userPerson, deviceInfo);
            
            // 临时缓存设备状态，避免下次立即触发二次验证
            cacheDeviceLoginTemporary(userPerson.getCustomerAccount().getId(), deviceInfo.getDeviceId());
        }
    }

    @Override
    @Async
    public void asyncRetryDeviceBinding(UserPerson userPerson, AccountDeviceDTO deviceInfo) {
        final int maxRetries = 3;
        final long[] retryDelays = {5000, 15000, 60000}; // 5秒、15秒、60秒
        
        for (int attempt = 0; attempt < maxRetries; attempt++) {
            try {
                // 等待一段时间后重试
                if (attempt > 0) {
                    Thread.sleep(retryDelays[attempt - 1]);
                }
                
                log.info("开始第{}次重试设备绑定，用户ID: {}, 设备ID: {}", 
                        attempt + 1, userPerson.getId(), deviceInfo.getDeviceId());
                
                // 重新尝试绑定设备
                AccountDevice accountDevice = AccountDeviceDTOAssembler.convertToDomain(deviceInfo);
                accountDevice.setAccountId(userPerson.getCustomerAccount().getId());
                accountDevice.setSource(userPerson.getSource());
                userPersonDomainService.bindDevice(accountDevice);
                
                // 绑定成功，更新缓存
                cacheDeviceLogin(userPerson.getCustomerAccount().getId(), deviceInfo.getDeviceId());
                
                log.info("设备绑定重试成功，用户ID: {}, 设备ID: {}, 重试次数: {}", 
                        userPerson.getId(), deviceInfo.getDeviceId(), attempt + 1);
                return; // 成功后退出
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("设备绑定重试被中断，用户ID: {}, 设备ID: {}", 
                        userPerson.getId(), deviceInfo.getDeviceId());
                return;
            } catch (Exception e) {
                log.error("第{}次设备绑定重试失败，用户ID: {}, 设备ID: {}, 错误: {}", 
                        attempt + 1, userPerson.getId(), deviceInfo.getDeviceId(), e.getMessage());
                
                // 如果是最后一次重试，记录最终失败
                if (attempt == maxRetries - 1) {
                    log.error("设备绑定最终失败，已经重试{}次，用户ID: {}, 设备ID: {}", 
                            maxRetries, userPerson.getId(), deviceInfo.getDeviceId(), e);
                }
            }
        }
    }

    @Override
    public void cacheDeviceLogin(Long accountId, String deviceId) {
        try {
            String cacheKey = UserLoginRedisKeys.getDeviceLoginCacheKey(accountId, deviceId);
            // 设置较长的过期时间，比如30天
            redisUtil.set(cacheKey, "1", 30, TimeUnit.DAYS);
            log.debug("缓存设备登录状态，账户ID: {}, 设备ID: {}", accountId, deviceId);
        } catch (Exception e) {
            log.error("缓存设备登录状态失败，账户ID: {}, 设备ID: {}", accountId, deviceId, e);
        }
    }

    @Override
    public void cacheDeviceLoginTemporary(Long accountId, String deviceId) {
        try {
            String cacheKey = UserLoginRedisKeys.getDeviceLoginCacheKey(accountId, deviceId);
            // 设置较短的过期时间，比如1小时
            redisUtil.set(cacheKey, "1", 1, TimeUnit.HOURS);
            log.debug("临时缓存设备状态，账户ID: {}, 设备ID: {}", accountId, deviceId);
        } catch (Exception e) {
            log.error("临时缓存设备状态失败，账户ID: {}, 设备ID: {}", accountId, deviceId, e);
        }
    }

    @Override
    public void sendSecurityNotification(String phone, AccountDeviceDTO deviceInfo, String clientIp) {
        try {
            // 这里可以实现安全通知逻辑，比如：
            // 1. 发送短信通知
            // 2. 发送邮件通知
            // 3. 站内消息通知
            // 4. 推送通知
            
            log.info("发送安全通知，手机号: {}, 设备ID: {}, IP: {}", 
                    phone, deviceInfo.getDeviceId(), clientIp);
            
            // 暂时只记录日志，实际项目中可以集成具体的通知服务
            
        } catch (Exception e) {
            log.error("发送安全通知失败，手机号: {}", phone, e);
        }
    }

    @Override
    public Map<String, Boolean> batchCheckDeviceStatus(Long accountId, List<String> deviceIds) {
        return deviceIds.stream()
                .collect(Collectors.toMap(
                        deviceId -> deviceId,
                        deviceId -> {
                            try {
                                return checkIsNewDevice(accountId, deviceId);
                            } catch (Exception e) {
                                log.error("批量检查设备状态失败: accountId={}, deviceId={}", accountId, deviceId, e);
                                return true; // 异常时认为是新设备
                            }
                        }
                ));
    }

    @Override
    public void unbindUserDevice(Long accountId, String deviceId) {
        try {
            // 从数据库删除设备记录
            //accountDeviceRepository.deleteByAccountIdAndDeviceId(accountId, deviceId);
            
            // 清除缓存
            String cacheKey = UserLoginRedisKeys.getDeviceLoginCacheKey(accountId, deviceId);
            redisUtil.delete(cacheKey);
            
            log.info("解绑用户设备成功，账户ID: {}, 设备ID: {}", accountId, deviceId);
            
        } catch (Exception e) {
            log.error("解绑用户设备失败，账户ID: {}, 设备ID: {}", accountId, deviceId, e);
            throw new BusinessException(UsersErrorCode.DEVICE_BINDING_FAILED.getCode(),
                    UsersErrorCode.DEVICE_BINDING_FAILED.getMsg());
        }
    }

    @Override
    public List<AccountDeviceDTO> getUserDevices(Long accountId) {
        try {
            List<AccountDevice> devices = accountDeviceRepository.findByAccountId(accountId);
            return devices.stream()
                    .map(AccountDeviceDTOAssembler::convertToDTO)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取用户设备列表失败，账户ID: {}", accountId, e);
            throw new BusinessException(UsersErrorCode.SYSTEM_ERROR.getCode(),
                    UsersErrorCode.SYSTEM_ERROR.getMsg());
        }
    }

    @Override
    public int cleanExpiredDeviceCache(int expiredDays) {
        try {
            // 这里可以实现清理过期设备缓存的逻辑
            // 实际实现需要根据Redis的具体情况来设计
            
            log.info("清理过期设备缓存，过期天数: {}", expiredDays);
            
            // 暂时返回0，实际项目中需要实现具体的清理逻辑
            return 0;
            
        } catch (Exception e) {
            log.error("清理过期设备缓存失败", e);
            return 0;
        }
    }
}
