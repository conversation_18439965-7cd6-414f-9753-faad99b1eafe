package com.yuanchuan.user.application.service;

import com.yuanchuan.user.api.dto.AccountDeviceDTO;
import com.yuanchuan.user.domain.model.UserPerson;


import java.util.List;
import java.util.Map;

/**
 * 设备管理应用服务接口
 * 
 * 此应用服务负责：
 * - 协调设备相关的领域服务
 * - 处理设备管理的应用层业务流程
 * - 管理设备绑定的事务边界
 * - 协调基础设施服务（缓存、异步任务等）
 * 
 * 设备管理应用服务专门处理用户设备相关的业务用例，
 * 包括设备检测、绑定、缓存管理、重试机制等。
 * 
 * <AUTHOR>
 * @date 2025/1/20
 * @version 1.0
 */
public interface DeviceManagementApplicationService {

    /**
     * 检查是否为新设备应用用例
     * 
     * 通过协调缓存服务和领域服务来判断设备是否为用户的新设备。
     * 
     * 应用流程：
     * 1. 验证设备ID有效性
     * 2. 检查缓存中的设备状态
     * 3. 查询领域层的设备记录
     * 4. 返回检测结果
     * 
     * @param accountId 账户ID
     * @param deviceId 设备ID
     * @return true表示是新设备，false表示是已知设备
     * @throws com.yuanchuan.common.exception.BusinessException 当设备ID为空时抛出异常
     */
    boolean checkIsNewDevice(Long accountId, String deviceId);

    /**
     * 绑定用户设备应用用例
     * 
     * 协调领域服务和基础设施服务完成设备绑定，
     * 如果绑定失败会启动异步重试机制。
     * 
     * 应用流程：
     * 1. 调用领域服务进行设备绑定
     * 2. 更新设备状态缓存
     * 3. 如果失败，启动异步重试
     * 4. 记录操作日志
     * 
     * @param userPerson 用户领域对象
     * @param deviceInfo 设备信息DTO
     */
    void bindUserDevice(UserPerson userPerson, AccountDeviceDTO deviceInfo);

    /**
     * 异步重试设备绑定应用用例
     * 
     * 当设备绑定失败时，通过异步机制进行重试。
     * 重试策略：最多3次，间隔时间递增（5秒、15秒、60秒）
     * 
     * 应用流程：
     * 1. 按照重试策略进行延时
     * 2. 重新调用领域服务绑定设备
     * 3. 更新缓存状态
     * 4. 记录重试结果
     * 
     * @param userPerson 用户领域对象
     * @param deviceInfo 设备信息DTO
     */
    void asyncRetryDeviceBinding(UserPerson userPerson, AccountDeviceDTO deviceInfo);

    /**
     * 缓存设备登录状态应用用例
     * 
     * 将设备登录状态缓存到基础设施层，用于快速检测已知设备。
     * 
     * @param accountId 账户ID
     * @param deviceId 设备ID
     */
    void cacheDeviceLogin(Long accountId, String deviceId);

    /**
     * 临时缓存设备状态应用用例
     * 
     * 当设备绑定失败时，临时缓存设备状态以避免频繁触发二次验证。
     * 缓存时间较短（1小时），给重试机制留出时间。
     * 
     * @param accountId 账户ID
     * @param deviceId 设备ID
     */
    void cacheDeviceLoginTemporary(Long accountId, String deviceId);

    /**
     * 发送安全通知应用用例
     * 
     * 当检测到仅手机号用户在新设备登录时，发送安全通知。
     * 协调通知服务完成消息发送。
     * 
     * @param phone 手机号
     * @param deviceInfo 设备信息DTO
     * @param clientIp 客户端IP地址
     */
    void sendSecurityNotification(String phone, AccountDeviceDTO deviceInfo, String clientIp);

    /**
     * 批量检查设备状态应用用例
     * 
     * 批量检查多个设备的状态，用于批量操作场景。
     * 
     * @param accountId 账户ID
     * @param deviceIds 设备ID列表
     * @return 设备状态映射（设备ID -> 是否为新设备）
     */
    Map<String, Boolean> batchCheckDeviceStatus(Long accountId, List<String> deviceIds);

    /**
     * 解绑用户设备应用用例
     * 
     * 解除用户与设备的绑定关系，清理相关缓存。
     * 
     * @param accountId 账户ID
     * @param deviceId 设备ID
     */
    void unbindUserDevice(Long accountId, String deviceId);

    /**
     * 获取用户设备列表应用用例
     * 
     * 获取用户绑定的所有设备信息。
     * 
     * @param accountId 账户ID
     * @return 设备信息列表
     */
    List<AccountDeviceDTO> getUserDevices(Long accountId);

    /**
     * 清理过期设备缓存应用用例
     * 
     * 定期清理过期的设备缓存数据。
     * 
     * @param expiredDays 过期天数
     * @return 清理的设备数量
     */
    int cleanExpiredDeviceCache(int expiredDays);
}
