package com.yuanchuan.user.application.service.impl;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

import com.yuanchuan.common.enums.users.UsersErrorCode;
import com.yuanchuan.common.exception.BusinessException;
import com.yuanchuan.user.api.dto.AccountDeviceDTO;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.yuanchuan.user.api.dto.UserDeviceOperationDTO;
import com.yuanchuan.user.api.service.UserDeviceService;
import com.yuanchuan.user.domain.model.AccountDevice;
import com.yuanchuan.user.domain.repository.AccountDeviceRepository;

/**
 * 用户设备应用服务实现类
 */
@Service
@DubboService(version = "1.0", group = "${dubbo.group}", delay = -1, retries = -1, timeout = 600000)
public class UserDeviceApplicationServiceImpl implements UserDeviceService {

    @Autowired
    private AccountDeviceRepository accountDeviceRepository;

    @Override
    @Transactional
    public boolean registerDevice(AccountDeviceDTO deviceInfoDTO) {
        if (deviceInfoDTO == null) {
            throw new BusinessException(UsersErrorCode.DEVICE_INFO_EMPTY.getCode(), UsersErrorCode.DEVICE_INFO_EMPTY.getMsg());
        }

        // 创建设备信息
        AccountDevice device = convertToDomain(deviceInfoDTO);
        device.setCreatedAt(LocalDateTime.now());
        device.setUpdatedAt(LocalDateTime.now());
        device.setCreatedBy("system");
        device.setUpdatedBy("system");
        device.setActive(true);

        // 保存设备信息
        accountDeviceRepository.save(device);

        return true;
    }

    @Override
    @Transactional
    public boolean bindDevice(AccountDeviceDTO deviceInfoDTO) {
        if (deviceInfoDTO == null) {
            throw new BusinessException(UsersErrorCode.DEVICE_INFO_EMPTY.getCode(), UsersErrorCode.DEVICE_INFO_EMPTY.getMsg());
        }

        if (deviceInfoDTO.getAccountId() == null) {
            throw new BusinessException(UsersErrorCode.USER_ID_EMPTY.getCode(), UsersErrorCode.USER_ID_EMPTY.getMsg());
        }

        // 检查设备是否已存在
        AccountDevice existingDevice = null;
        if (deviceInfoDTO.getDeviceId() != null) {
            existingDevice = accountDeviceRepository.findByAccountIdAndDeviceId(deviceInfoDTO.getAccountId(), deviceInfoDTO.getDeviceId())
                    .orElse(null);
        }

        if (existingDevice != null) {
            // 更新设备信息
            existingDevice.setDeviceType(deviceInfoDTO.getDeviceType());
            existingDevice.setDeviceName(deviceInfoDTO.getDeviceName());
            //existingDevice.setOsVersion(deviceInfoDTO.getOsVersion());
            //existingDevice.setAppVersion(deviceInfoDTO.getAppVersion());
            //existingDevice.setIpAddress(deviceInfoDTO.getIpAddress());
            //existingDevice.setUserAgent(deviceInfoDTO.getUserAgent());
            existingDevice.setIsActive(true);
            existingDevice.setLastLoginAt(LocalDateTime.now());
            existingDevice.setUpdatedAt(LocalDateTime.now());
            existingDevice.setUpdatedBy("system");

            // 保存设备信息
            accountDeviceRepository.save(existingDevice);
        } else {
            // 创建新设备信息
            AccountDevice device = convertToDomain(deviceInfoDTO);
            device.setIsActive(true);
            device.setIsVerified(true);
            device.setLastLoginAt(LocalDateTime.now());
            device.setCreatedAt(LocalDateTime.now());
            device.setUpdatedAt(LocalDateTime.now());
            device.setCreatedBy("system");
            device.setUpdatedBy("system");
            device.setActive(true);

            // 保存设备信息
            accountDeviceRepository.save(device);
        }

        return true;
    }



    @Override
    @Transactional
    public boolean unbindDevice(UserDeviceOperationDTO operationDTO) {
        validateOperationDTO(operationDTO);

        // 查询设备信息
        AccountDevice device = accountDeviceRepository.findById(operationDTO.getAccountDeviceId())
                .orElseThrow(() -> new IllegalArgumentException("设备不存在"));

        // 检查设备是否属于该用户
        if (!device.getAccountId().equals(operationDTO.getUserId())) {
            throw new IllegalArgumentException("设备不属于该用户");
        }

        // 删除设备信息
        return accountDeviceRepository.deleteById(device.getId());
    }

    @Override
    @Transactional
    public boolean disableDevice(UserDeviceOperationDTO operationDTO) {
        validateOperationDTO(operationDTO);

        // 查询设备信息
        AccountDevice device = accountDeviceRepository.findById(operationDTO.getAccountDeviceId())
                .orElseThrow(() -> new IllegalArgumentException("设备不存在"));

        // 检查设备是否属于该用户
        if (!device.getAccountId().equals(operationDTO.getUserId())) {
            throw new IllegalArgumentException("设备不属于该用户");
        }

        // 禁用设备
        return accountDeviceRepository.updateActiveStatus(device.getId(), false);
    }

    @Override
    @Transactional
    public boolean renameDevice(UserDeviceOperationDTO operationDTO) {
        validateOperationDTO(operationDTO);

        if (operationDTO.getNewDeviceName() == null || operationDTO.getNewDeviceName().isEmpty()) {
            throw new IllegalArgumentException("新设备名称不能为空");
        }

        // 查询设备信息
        AccountDevice device = accountDeviceRepository.findById(operationDTO.getAccountDeviceId())
                .orElseThrow(() -> new IllegalArgumentException("设备不存在"));

        // 检查设备是否属于该用户
        if (!device.getAccountId().equals(operationDTO.getUserId())) {
            throw new IllegalArgumentException("设备不属于该用户");
        }

        // 更新设备名称
        device.setDeviceName(operationDTO.getNewDeviceName());
        device.setUpdatedAt(LocalDateTime.now());
        device.setUpdatedBy("system");

        // 保存设备信息
        accountDeviceRepository.save(device);

        return true;
    }

    @Override
    public AccountDeviceDTO getDeviceInfo(Long deviceId) {
        if (deviceId == null) {
            throw new IllegalArgumentException("设备ID不能为空");
        }

        // 查询设备信息
        AccountDevice device = accountDeviceRepository.findById(deviceId)
                .orElseThrow(() -> new IllegalArgumentException("设备不存在"));

        // 返回设备信息
        return convertToDTO(device);
    }

    @Override
    public List<AccountDeviceDTO> getUserDevices(Long userId) {
        if (userId == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }

        // 查询用户设备列表
        List<AccountDevice> devices = accountDeviceRepository.findByAccountId(userId);

        // 返回设备信息列表
        return devices.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 验证操作参数
     *
     * @param operationDTO 操作参数
     */
    private void validateOperationDTO(UserDeviceOperationDTO operationDTO) {
        if (operationDTO == null) {
            throw new IllegalArgumentException("操作参数不能为空");
        }

        if (operationDTO.getDeviceId() == null) {
            throw new IllegalArgumentException("设备ID不能为空");
        }

        if (operationDTO.getUserId() == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
    }

    /**
     * 将DTO转换为领域对象
     *
     * @param dto DTO
     * @return 领域对象
     */
    private AccountDevice convertToDomain(AccountDeviceDTO dto) {
        AccountDevice domain = new AccountDevice();
        //domain.setId(dto.getId());
        domain.setAccountId(dto.getAccountId());
        domain.setDeviceId(dto.getDeviceId());
        domain.setDeviceType(dto.getDeviceType());
        domain.setDeviceName(dto.getDeviceName());
        //domain.setOsVersion(dto.getOsVersion());
        //domain.setAppVersion(dto.getAppVersion());
        //domain.setIpAddress(dto.getIpAddress());
        //domain.setUserAgent(dto.getUserAgent());
        //domain.setIsVerified(dto.getIsVerified());
        //domain.setIsActive(dto.getIsActive());
        //domain.setLastLoginAt(dto.getLastLoginAt());
        domain.setSource("CUSTOMER_LOGIN");
        return domain;
    }

    /**
     * 将领域对象转换为DTO
     *
     * @param domain 领域对象
     * @return DTO
     */
    private AccountDeviceDTO convertToDTO(AccountDevice domain) {
        AccountDeviceDTO dto = new AccountDeviceDTO();
        //dto.setId(domain.getId());
        dto.setAccountId(domain.getAccountId());
        dto.setDeviceId(domain.getDeviceId());
        dto.setDeviceType(domain.getDeviceType());
        dto.setDeviceName(domain.getDeviceName());
        //dto.setOsVersion(domain.getOsVersion());
        //dto.setAppVersion(domain.getAppVersion());
        //dto.setIpAddress(domain.getIpAddress());
        //dto.setUserAgent(domain.getUserAgent());
        //dto.setIsVerified(domain.getIsVerified());
        //dto.setIsActive(domain.getIsActive());
        //dto.setLastLoginAt(domain.getLastLoginAt());
        dto.setStatus(domain.getIsActive() ? 1 : 0);
        return dto;
    }
}
