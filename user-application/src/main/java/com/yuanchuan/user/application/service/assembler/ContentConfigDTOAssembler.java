package com.yuanchuan.user.application.service.assembler;

import cn.hutool.core.stream.CollectorUtil;
import com.yuanchuan.user.api.dto.ContentConfigDTO;
import com.yuanchuan.user.api.dto.ContentConfigDetailDTO;
import com.yuanchuan.user.api.request.ContentConfigModifyRequest;
import com.yuanchuan.user.api.request.ContentConfigQueryRequest;
import com.yuanchuan.user.domain.model.ContentConfig;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 内容配置DTO转换器
 */
@Component
public class ContentConfigDTOAssembler {


    /**
     * 将领域对象转换为DTO
     *
     * @param entity 领域对象
     * @return DTO
     */
    public static ContentConfigDTO toDTO(ContentConfig entity) {
        if (entity == null) {
            return null;
        }
        ContentConfigDTO dto = new ContentConfigDTO();
        BeanUtils.copyProperties(entity, dto);
        if (entity.getType() != null) {
            dto.setType(entity.getType());
        }
        if (entity.getTitleEnum() != null) {
            dto.setTitleEnum(entity.getTitleEnum());
        }

        // 如果有子内容，递归转换子内容
        if (entity.getChildren() != null && !entity.getChildren().isEmpty()) {
            List<ContentConfigDetailDTO> childrenDTOs = entity.getChildren().stream()
                    .map(ContentConfigDTOAssembler::toDetailDTO)
                    .collect(Collectors.toList());
            dto.setChildren(childrenDTOs);
        }
        return dto;
    }

    /**
     * 将领域对象转换为详情DTO
     *
     * @param entity 领域对象
     * @return 详情DTO
     */
    public static ContentConfigDetailDTO toDetailDTO(ContentConfig entity) {
        if (entity == null) {
            return null;
        }
        ContentConfigDetailDTO dto = new ContentConfigDetailDTO();
        BeanUtils.copyProperties(entity, dto);
        if (entity.getType() != null) {
            dto.setType(entity.getType());
            dto.setTypeDescription(entity.getType().getDescription());
        }

        // 如果有子内容，递归转换子内容
        if (!CollectionUtils.isEmpty(entity.getChildren())) {
            List<ContentConfigDetailDTO> childrenDTOs = entity.getChildren().stream()
                    .map(ContentConfigDTOAssembler::toDetailDTO)
                    .collect(Collectors.toList());
            dto.setChildren(childrenDTOs);
        }

        return dto;
    }

    /**
     * 将DTO转换为领域对象
     *
     * @param dto DTO
     * @return 领域对象
     */
    public static ContentConfig toDomain(ContentConfigDTO dto) {
        if (dto == null) {
            return null;
        }
        ContentConfig entity = new ContentConfig();
        BeanUtils.copyProperties(dto, entity);
        if (dto.getType() != null) {
            entity.setType(dto.getType());
        }
        if (dto.getTitleEnum() != null) {
            entity.setTitleEnum(dto.getTitleEnum());
        }
        return entity;
    }

    /**
     * 将ContentConfigModifyRequest转换为领域对象
     *
     * @param request
     * @return 领域对象
     */
    public static ContentConfig modifyReqToDomain(ContentConfigModifyRequest request) {
        if (request == null) {
            return null;
        }
        ContentConfig entity = new ContentConfig();
        BeanUtils.copyProperties(request, entity);
        if (request.getType() != null) {
            entity.setType(request.getType());
        }
        if (request.getTitleEnum() != null) {
            entity.setTitleEnum(request.getTitleEnum());
        }
        return entity;
    }

    /**
     * 将ContentConfigQueryRequest转换为领域对象
     *
     * @param request
     * @return 领域对象
     */
    public static ContentConfig queryReqtoDomain(ContentConfigQueryRequest request) {
        if (request == null) {
            return null;
        }
        ContentConfig entity = new ContentConfig();
        BeanUtils.copyProperties(request, entity);
        return entity;
    }



    /**
     * 将DTO列表转换为领域对象列表
     *
     * @param dtoList DTO列表
     * @return 领域对象列表
     */
    public static List<ContentConfig> toDomainList(List<ContentConfigDTO> dtoList) {
        if (dtoList == null) {
            return null;
        }
        return dtoList.stream().map(ContentConfigDTOAssembler::toDomain).collect(Collectors.toList());
    }

    /**
     * 将领域对象列表转换为DTO列表
     *
     * @param entityList 领域对象列表
     * @return DTO列表
     */
    public static List<ContentConfigDTO> toDTOList(List<ContentConfig> entityList) {
        if (entityList == null) {
            return null;
        }
        return entityList.stream().map(ContentConfigDTOAssembler::toDTO).collect(Collectors.toList());
    }

    /**
     * 将领域对象列表转换为详情DTO列表
     *
     * @param entityList 领域对象列表
     * @return 详情DTO列表
     */
    public static List<ContentConfigDetailDTO> toDetailDTOList(List<ContentConfig> entityList) {
        if (entityList == null) {
            return null;
        }
        return entityList.stream().map(ContentConfigDTOAssembler::toDetailDTO).collect(Collectors.toList());
    }
}
