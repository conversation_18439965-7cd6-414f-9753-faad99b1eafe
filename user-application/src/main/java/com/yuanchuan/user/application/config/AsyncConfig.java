package com.yuanchuan.user.application.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 异步配置类
 */
@Slf4j
@Configuration
@EnableAsync
public class AsyncConfig {

    @Autowired
    private LoginLogConfig loginLogConfig;

    /**
     * 登录日志异步执行器
     */
    @Bean("loginLogExecutor")
    public Executor loginLogExecutor() {
        LoginLogConfig.ThreadPool config = loginLogConfig.getThreadPool();

        // 验证线程池配置
        config.validate();

        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(config.getCoreSize());
        executor.setMaxPoolSize(config.getMaxSize());
        executor.setQueueCapacity(config.getQueueCapacity());
        executor.setKeepAliveSeconds(config.getKeepAliveSeconds());
        executor.setThreadNamePrefix(config.getThreadNamePrefix());
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(config.isWaitForTasksToCompleteOnShutdown());
        executor.setAwaitTerminationSeconds(config.getAwaitTerminationSeconds());
        executor.initialize();

        log.info("登录日志线程池初始化完成: coreSize={}, maxSize={}, queueCapacity={}, threadNamePrefix={}",
                config.getCoreSize(), config.getMaxSize(), config.getQueueCapacity(), config.getThreadNamePrefix());

        return executor;
    }
}
