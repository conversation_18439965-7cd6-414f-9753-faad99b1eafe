package com.yuanchuan.user.application.service.impl;

import com.yuanchuan.common.enums.users.login.LoginType;
import com.yuanchuan.common.enums.users.login.PlatformType;
import com.yuanchuan.user.api.dto.AccountDeviceDTO;
import com.yuanchuan.user.api.service.LoginLogApplicationService;
import com.yuanchuan.user.application.config.LoginLogConfig;
import com.yuanchuan.user.domain.service.LoginLogDomainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * 登录日志应用服务实现类
 */
@Slf4j
@Service
public class LoginLogApplicationServiceImpl implements LoginLogApplicationService {

    @Autowired
    private LoginLogDomainService loginLogDomainService;

    @Autowired
    private LoginLogConfig loginLogConfig;

    @Override
    public void recordLoginSuccess(Long accountId, LoginType loginType, PlatformType platform,
                                  AccountDeviceDTO deviceInfo, String ipAddress, String userAgent) {
        // 检查是否启用登录日志功能
        if (!loginLogConfig.isEnabled() || !loginLogConfig.isRecordSuccess()) {
            log.debug("登录成功日志记录已禁用: enabled={}, recordSuccess={}",
                     loginLogConfig.isEnabled(), loginLogConfig.isRecordSuccess());
            return;
        }

        if (loginLogConfig.isAsync()) {
            recordLoginSuccessAsync(accountId, loginType, platform, deviceInfo, ipAddress, userAgent);
        } else {
            recordLoginSuccessSync(accountId, loginType, platform, deviceInfo, ipAddress, userAgent);
        }
    }

    /**
     * 异步记录登录成功日志
     * 注意：@Async 注解不能作用于 private 方法，必须是 public 或 protected
     */
    @Async("loginLogExecutor")
    public void recordLoginSuccessAsync(Long accountId, LoginType loginType, PlatformType platform,
                                       AccountDeviceDTO deviceInfo, String ipAddress, String userAgent) {
        recordLoginSuccessSync(accountId, loginType, platform, deviceInfo, ipAddress, userAgent);
    }

    /**
     * 同步记录登录成功日志
     */
    private void recordLoginSuccessSync(Long accountId, LoginType loginType, PlatformType platform,
                                       AccountDeviceDTO deviceInfo, String ipAddress, String userAgent) {
        try {
            log.info("开始记录登录成功日志: accountId={}, loginType={}, platform={}, async={}",
                    accountId, loginType, platform, loginLogConfig.isAsync());

            if (accountId == null) {
                log.info("accountId为null，跳过登录成功日志记录: loginType={}, platform={}, ip={}", loginType, platform, ipAddress);
                return;
            }

            String deviceId = deviceInfo != null ? deviceInfo.getDeviceId() : null;
            String location = resolveLocation(ipAddress);

            log.debug("调用领域服务记录登录成功日志: accountId={}, deviceId={}", accountId, deviceId);
            loginLogDomainService.recordLoginSuccess(accountId, deviceId, loginType, platform,
                                                   ipAddress, userAgent, location);
            log.info("登录成功日志记录完成: accountId={}", accountId);
        } catch (Exception e) {
            log.error("记录登录成功日志失败: accountId={}, error={}", accountId, e.getMessage(), e);
        }
    }

    @Override
    public void recordLoginFailure(Long accountId, LoginType loginType, PlatformType platform,
                                  AccountDeviceDTO deviceInfo, String ipAddress, String userAgent) {
        // 检查是否启用登录日志功能
        if (!loginLogConfig.isEnabled() || !loginLogConfig.isRecordFailure()) {
            log.debug("登录失败日志记录已禁用: enabled={}, recordFailure={}",
                     loginLogConfig.isEnabled(), loginLogConfig.isRecordFailure());
            return;
        }

        if (loginLogConfig.isAsync()) {
            recordLoginFailureAsync(accountId, loginType, platform, deviceInfo, ipAddress, userAgent);
        } else {
            recordLoginFailureSync(accountId, loginType, platform, deviceInfo, ipAddress, userAgent);
        }
    }

    /**
     * 异步记录登录失败日志
     * 注意：@Async 注解不能作用于 private 方法，必须是 public 或 protected
     */
    @Async("loginLogExecutor")
    public void recordLoginFailureAsync(Long accountId, LoginType loginType, PlatformType platform,
                                       AccountDeviceDTO deviceInfo, String ipAddress, String userAgent) {
        recordLoginFailureSync(accountId, loginType, platform, deviceInfo, ipAddress, userAgent);
    }

    /**
     * 同步记录登录失败日志
     */
    private void recordLoginFailureSync(Long accountId, LoginType loginType, PlatformType platform,
                                       AccountDeviceDTO deviceInfo, String ipAddress, String userAgent) {
        try {
            log.info("开始记录登录失败日志: accountId={}, loginType={}, platform={}, async={}",
                    accountId, loginType, platform, loginLogConfig.isAsync());

            String deviceId = deviceInfo != null ? deviceInfo.getDeviceId() : null;
            String location = resolveLocation(ipAddress);

            loginLogDomainService.recordLoginFailure(accountId, deviceId, loginType, platform,
                                                   ipAddress, userAgent, location);
            log.info("登录失败日志记录完成: accountId={}", accountId);
        } catch (Exception e) {
            log.error("记录登录失败日志失败: accountId={}, error={}", accountId, e.getMessage(), e);
        }
    }

    @Override
    public boolean checkAbnormalLogin(Long accountId) {
        try {
            // 使用配置中的参数检查异常登录
            int timeWindow = loginLogConfig.getAbnormalTimeWindow();
            int threshold = loginLogConfig.getAbnormalThreshold();

            int failureCount = loginLogDomainService.countLoginFailures(accountId, timeWindow);

            if (failureCount >= threshold) {
                log.warn("检测到账户异常登录行为: accountId={}, failureCount={}, threshold={}, timeWindow={}h",
                        accountId, failureCount, threshold, timeWindow);
                return true;
            }

            log.debug("账户登录行为正常: accountId={}, failureCount={}, threshold={}, timeWindow={}h",
                     accountId, failureCount, threshold, timeWindow);
            return false;
        } catch (Exception e) {
            log.error("检查异常登录行为失败: accountId={}, error={}", accountId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 解析IP地址对应的地理位置
     * 这里可以集成第三方IP地址解析服务
     */
    private String resolveLocation(String ipAddress) {
        // 检查是否启用IP地址解析
        if (!loginLogConfig.isIpLocationEnabled()) {
            log.debug("IP地址解析已禁用，跳过解析: ip={}", ipAddress);
            return null;
        }

        if (ipAddress == null || ipAddress.trim().isEmpty()) {
            return null;
        }

        try {
            // TODO: 集成IP地址解析服务，如高德、百度等
            // 目前返回空，后续可以扩展
            log.debug("开始IP地址解析: ip={}", ipAddress);

            // 这里可以调用第三方IP解析服务
            // String location = thirdPartyIpService.resolveLocation(ipAddress);

            return null;
        } catch (Exception e) {
            log.error("IP地址解析失败: ip={}, error={}", ipAddress, e.getMessage());
            return null;
        }
    }
}
