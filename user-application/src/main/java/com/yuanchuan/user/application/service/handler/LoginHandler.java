package com.yuanchuan.user.application.service.handler;

import com.yuanchuan.authentication.context.enums.BindingPhoneVerifyStatus;
import com.yuanchuan.user.api.request.UnifiedLoginRequest;
import com.yuanchuan.user.api.request.UnifiedLoginResponse;
import com.yuanchuan.common.enums.users.login.LoginType;

/**
 * 登录处理器接口
 * 负责处理不同类型的登录请求
 */
public interface LoginHandler {
    
    /**
     * 处理登录请求
     *
     * @param request  统一登录请求
     * @return 统一登录响应
     */
    UnifiedLoginResponse handle(UnifiedLoginRequest request);
    
    /**
     * 判断是否支持处理该类型的登录请求
     *
     * @param loginType 登录类型
     * @param status    流程状态
     * @return 是否支持
     */
    boolean supports(LoginType loginType, BindingPhoneVerifyStatus status);
}
