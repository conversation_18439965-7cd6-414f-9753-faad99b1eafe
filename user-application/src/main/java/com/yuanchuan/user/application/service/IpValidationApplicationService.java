package com.yuanchuan.user.application.service;


import com.yuanchuan.common.utils.IpAddressUtil;

/**
 * IP地址验证应用服务接口
 *
 * 此应用服务负责：
 * - 协调IP地址相关的领域服务和基础设施服务
 * - 处理IP地址安全验证的应用层业务流程
 * - 管理IP地址安全事件的记录和通知
 * - 协调第三方IP地理位置服务
 *
 * IP地址验证应用服务专门处理IP地址相关的安全验证业务用例，
 * 包括IP一致性检查、风险评估、安全通知、白名单管理等。
 *
 * <AUTHOR>
 * @date 2025/1/20
 * @version 1.0
 */
public interface IpValidationApplicationService {

    /**
     * 验证IP地址一致性应用用例
     *
     * 在二次验证过程中，检查用户当前IP地址与触发验证时的IP地址是否一致，
     * 根据IP变化的风险等级采取不同的安全措施。
     *
     * 应用流程：
     * 1. 验证IP地址格式
     * 2. 计算IP变化风险等级
     * 3. 根据风险等级执行相应策略
     * 4. 记录安全事件
     * 5. 发送安全通知（如需要）
     *
     * @param originalIp 原始IP地址（触发二次验证时的IP）
     * @param currentIp 当前IP地址（进行二次验证时的IP）
     * @param userId 用户ID（用于日志记录和安全通知）
     */
    void validateIpAddressConsistency(String originalIp, String currentIp, Long userId);

    /**
     * 计算IP地址变化的风险等级应用用例
     *
     * 基于网络拓扑、地理位置和用户历史行为分析IP地址变化的安全风险。
     *
     * @param originalIp 原始IP地址
     * @param currentIp 当前IP地址
     * @return IP变化的风险等级
     */
    IpAddressUtil.IpChangeRiskLevel calculateIpChangeRisk(String originalIp, String currentIp);

    /**
     * 发送IP地址变化的安全通知应用用例
     *
     * 当检测到高风险的IP地址变化时，协调通知服务向用户发送安全通知。
     * 支持多种通知方式：短信、邮件、站内消息、推送通知。
     *
     * @param userId 用户ID
     * @param originalIp 原始IP地址
     * @param currentIp 当前IP地址
     */
    void sendIpChangeSecurityNotification(Long userId, String originalIp, String currentIp);

    /**
     * 记录IP地址安全事件应用用例
     *
     * 将IP地址变化事件记录到安全日志中，便于后续分析和审计。
     * 协调日志服务和安全事件存储。
     *
     * @param userId 用户ID
     * @param originalIp 原始IP地址
     * @param currentIp 当前IP地址
     * @param riskLevel 风险等级
     * @param eventType 事件类型（如：二次验证、登录等）
     */
    void recordIpSecurityEvent(Long userId, String originalIp, String currentIp,
                              IpAddressUtil.IpChangeRiskLevel riskLevel, String eventType);

    /**
     * 检查IP地址是否在白名单中应用用例
     *
     * 检查指定IP地址是否在用户的信任IP白名单中。
     * 白名单中的IP地址变化风险等级会被降低。
     *
     * @param userId 用户ID
     * @param ipAddress IP地址
     * @return true表示在白名单中，false表示不在
     */
    boolean isIpInWhitelist(Long userId, String ipAddress);

    /**
     * 获取IP地址的地理位置信息应用用例
     *
     * 通过协调第三方服务获取IP地址的地理位置信息，
     * 用于增强安全分析的准确性。
     *
     * @param ipAddress IP地址
     * @return 地理位置信息，获取失败时返回"未知"
     */
    String getIpLocation(String ipAddress);

    /**
     * 添加IP地址到白名单应用用例
     *
     * 将指定IP地址添加到用户的信任白名单中。
     *
     * @param userId 用户ID
     * @param ipAddress IP地址
     * @param description 描述信息
     */
    void addIpToWhitelist(Long userId, String ipAddress, String description);

    /**
     * 从白名单中移除IP地址应用用例
     *
     * 从用户的信任白名单中移除指定IP地址。
     *
     * @param userId 用户ID
     * @param ipAddress IP地址
     */
    void removeIpFromWhitelist(Long userId, String ipAddress);

    /**
     * 获取用户IP白名单应用用例
     *
     * 获取用户的所有信任IP地址列表。
     *
     * @param userId 用户ID
     * @return IP白名单列表
     */
    java.util.List<String> getUserIpWhitelist(Long userId);

    /**
     * 分析用户IP使用模式应用用例
     *
     * 分析用户的IP使用历史，识别异常模式。
     *
     * @param userId 用户ID
     * @param days 分析天数
     * @return IP使用模式分析结果
     */
    IpUsagePattern analyzeUserIpPattern(Long userId, int days);

    /**
     * 批量验证IP地址应用用例
     *
     * 批量验证多个IP地址的安全性。
     *
     * @param ipAddresses IP地址列表
     * @return IP地址安全性评估结果
     */
    java.util.Map<String, IpAddressUtil.IpChangeRiskLevel> batchValidateIpAddresses(java.util.List<String> ipAddresses);

    /**
     * IP使用模式分析结果
     */
    class IpUsagePattern {
        private final java.util.List<String> frequentIps;
        private final java.util.List<String> suspiciousIps;
        private final java.util.Map<String, Integer> ipFrequency;
        private final boolean hasAbnormalPattern;
        private final String analysisResult;

        public IpUsagePattern(java.util.List<String> frequentIps,
                             java.util.Map<String, Integer> ipFrequency,
                             boolean hasAbnormalPattern,
                             String analysisResult) {
            this.frequentIps = frequentIps;
            this.suspiciousIps = new java.util.ArrayList<>();
            this.ipFrequency = ipFrequency;
            this.hasAbnormalPattern = hasAbnormalPattern;
            this.analysisResult = analysisResult;
        }

        public IpUsagePattern(java.util.List<String> frequentIps,
                             java.util.List<String> suspiciousIps,
                             java.util.Map<String, Integer> ipFrequency,
                             boolean hasAbnormalPattern,
                             String analysisResult) {
            this.frequentIps = frequentIps;
            this.suspiciousIps = suspiciousIps;
            this.ipFrequency = ipFrequency;
            this.hasAbnormalPattern = hasAbnormalPattern;
            this.analysisResult = analysisResult;
        }

        public java.util.List<String> getFrequentIps() { return frequentIps; }
        public java.util.List<String> getSuspiciousIps() { return suspiciousIps; }
        public java.util.Map<String, Integer> getIpFrequency() { return ipFrequency; }
        public boolean isHasAbnormalPattern() { return hasAbnormalPattern; }
        public String getAnalysisResult() { return analysisResult; }
    }
}
