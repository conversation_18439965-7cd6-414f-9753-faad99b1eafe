package com.yuanchuan.user.application.service.impl;


import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSONObject;
import com.xkcoding.justauth.AuthRequestFactory;
import com.yuanchuan.authentication.api.dto.AuthThirdUserApiDTO;
import com.yuanchuan.authentication.api.dto.TokenDTO;
import com.yuanchuan.authentication.api.dto.TokenRequestDTO;
import com.yuanchuan.authentication.api.request.PhoneBindingRequest;
import com.yuanchuan.authentication.api.service.AuthenticationService;
import com.yuanchuan.authentication.content.model.UserContext;
import com.yuanchuan.authentication.content.utils.AuthUserContextUtils;
import com.yuanchuan.authentication.context.enums.BindingPhoneVerifyStatus;
import com.yuanchuan.common.enums.users.UsersErrorCode;
import com.yuanchuan.common.enums.users.login.LoginType;
import com.yuanchuan.common.enums.users.login.PlatformType;
import com.yuanchuan.common.exception.BusinessException;
import com.yuanchuan.user.api.dto.*;
import com.yuanchuan.user.api.request.*;
import com.yuanchuan.user.api.response.LoginResponse;
import com.yuanchuan.user.api.response.PasswordLoginResponse;
import com.yuanchuan.user.api.service.UserPersonService;
import com.yuanchuan.user.api.service.VerificationService;
import com.yuanchuan.user.application.annotation.LoginLog;
import com.yuanchuan.user.application.service.assembler.AccountDeviceDTOAssembler;
import com.yuanchuan.user.application.service.assembler.UserPersonDTOAssembler;
import com.yuanchuan.user.application.service.handler.LoginHandler;
import com.yuanchuan.user.domain.model.CustomerAccount;
import com.yuanchuan.user.domain.model.UserPerson;
import com.yuanchuan.user.domain.model.UserRegisterDomain;
import com.yuanchuan.user.domain.model.UserThirdPartyAuth;
import com.yuanchuan.user.domain.repository.CustomerAccountRepository;
import com.yuanchuan.user.domain.repository.UserPersonRepository;
import com.yuanchuan.user.domain.repository.UserThirdPartyAuthRespoitory;
import com.yuanchuan.user.domain.service.LogOffService;
import com.yuanchuan.user.domain.service.UserPersonDomainService;
import lombok.extern.slf4j.Slf4j;
import me.zhyd.oauth.model.AuthCallback;
import me.zhyd.oauth.model.AuthResponse;
import me.zhyd.oauth.request.AuthRequest;
import me.zhyd.oauth.utils.AuthStateUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 用户应用服务实现类
 */
@Slf4j
@Service
@DubboService(version = "1.0", group = "${dubbo.group}", delay = -1, retries = -1, timeout = 600000)
public class UserPersonApplicationServiceImpl implements UserPersonService {

    @DubboReference(version = "1.0", group = "${dubbo.consumer.group.authentication}")
    private AuthenticationService authenticationService;

    @Autowired
    private AuthRequestFactory factory;

    @Autowired
    private UserPersonDTOAssembler userPersonDTOAssembler;

    @Autowired
    private UserPersonDomainService userPersonDomainService;

    @Autowired
    private VerificationService verificationService;

    @Autowired
    private UserThirdPartyAuthRespoitory userThirdPartyAuthRespoitory;

    @Autowired
    private CustomerAccountRepository customerAccountRepository;
    @Autowired
    private LogOffService logOffService;

    @Autowired
    private UserPersonRepository userPersonRepository;

    @Autowired
    private List<LoginHandler> loginHandlers;

    @Autowired
    private UserLoginServiceImpl userLoginService;


    @Override
    public String login(String type) {
        // 直接调用领域服务进行三方登录授权
        // 实现三方登录逻辑
        AuthRequest authRequest = factory.get(type);
        return authRequest.authorize(AuthStateUtils.createState());
    }

    @Override
    public String loginCallback(String type, AuthCallbackRequest callback) {
        log.info("UserPersonApplicationServiceImpl#loginCallback type:{}", type);
        //三方token
        AuthUserResponse authUserResponse = queryAuthUser(type, callback);


        // 查询三方授权是否存在账号
        LoginResponse loginResponse = queryThirdPartyAccount(authUserResponse.getAccessToken());
        log.info("UserPersonApplicationServiceImpl#loginCallback loginResponse:{}", JSONObject.toJSONString(loginResponse));
        // 登录返回tokens
        if (!Objects.isNull(loginResponse)) {
            // 如果存在账号，生成tokens
            TokenRequestDTO tokenRequest = TokenRequestDTO.builder()
                    .userId(loginResponse.getUserId())
                    .businessAccountId(loginResponse.getBusinessAccountId())
                    .username(loginResponse.getUserInfo().getNickname())
                    .platform(PlatformType.CUSTOMER.getCode())
                    .deviceId(null)
                    .roles(new ArrayList<>())
                    .permissions(new ArrayList<>())
                    .build();

            // 调用AuthenticationService生成token
            TokenDTO token = authenticationService.generateToken(tokenRequest);
            // 重定向url
            return "/pages/redirect?accessToken=" + token.getAccessToken() + "&refreshToken=" + token.getRefreshToken() + "&expireTime=" + token.getExpiresAt() + "&flag=loginSuccess";

        } else {
            // 跳转绑定手机号页面
            PhoneBindingRequest request = new PhoneBindingRequest();

            // 三方认证信息
            AuthThirdUserApiDTO authThirdUserApiDTO = new AuthThirdUserApiDTO();
            BeanUtils.copyProperties(authUserResponse, authThirdUserApiDTO);

            // 邮箱未注册，需要绑定手机号
            // 使用 AuthenticationService 创建临时令牌
            PhoneBindingRequest bindingRequest = new PhoneBindingRequest();
            bindingRequest.setEmail(authUserResponse.getSub());
            bindingRequest.setDeviceId(null);
            bindingRequest.setUserAgent(request.getUserAgent());
            bindingRequest.setPlatform(request.getPlatform());
            bindingRequest.setAuthThirdUserDomainDTO(authThirdUserApiDTO);
            authenticationService.createPhoneBinding(bindingRequest);

            return authUserResponse.toBindUrl("/pages/redirect");
        }
    }

    @Override
    public void logout() {
        //  从上下文中获取用户信息
        UserContext userContext = AuthUserContextUtils.getCurrentUser();
        log.info("UserPersonApplicationServiceImpl#logout userContext:{}", JSONObject.toJSONString(userContext));
        if (Objects.isNull(userContext)) {
            throw new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg());
        }
        authenticationService.removeToken(userContext.getUserId(), userContext.getDeviceId());
    }

    @Override
    public void deletedUser() {
        //  从上下文中获取用户信息
        UserContext userContext = AuthUserContextUtils.getCurrentUser();
        log.info("UserPersonApplicationServiceImpl#deletedUser userContext:{}", JSONObject.toJSONString(userContext));
        if (Objects.isNull(userContext)) {
            throw new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg());
        }

        // todo 这里调用判断是否存在资产
       /* if(true){
            throw new BusinessException(UsersErrorCode.HAVE_PROPERTY.getCode(),UsersErrorCode.HAVE_PROPERTY.getMsg());
        }*/


        // 未存在资产进行注销逻辑
        boolean cancelFlag = logOffService.logOff(userContext.getUserId(), userContext.getDeviceId());

        // 4. 所有设备进行退登操作
        if (cancelFlag) {
            authenticationService.revokeAllTokens(userContext.getUserId());
        }
    }

    @Override
    @LoginLog(loginType = LoginType.SMS_CODE, autoInfer = true, description = "商户短信验证码注册登录")
    @Transactional(rollbackFor = Exception.class)
    public LoginResponse verifySmsCodeAndCreateMerchant(VerifySmsCodeAndBindPhoneRequest request, String clientIp) {
        try {
            // 处理设备信息
            AccountDeviceDTO deviceInfo = request.getDeviceInfo();
            if (deviceInfo == null) {
                deviceInfo = new AccountDeviceDTO();
            }
            deviceInfo.setIpAddress(clientIp);
            deviceInfo.setLoginTime(LocalDateTime.now());

            // 验证短信验证码
            boolean verified = verificationService.verifySmsCode(request.getPhone(), request.getCode(), request.getBusinessType());
            if (!verified) {
                throw new BusinessException(UsersErrorCode.SMS_CODE_CHECK_ERROR.getCode(), UsersErrorCode.SMS_CODE_CHECK_ERROR.getMsg());
            }
            // 创建领域对象
            UserRegisterDomain userRegisterDomain = new UserRegisterDomain();
            userRegisterDomain.setPhone(request.getPhone());
            userRegisterDomain.setEmail(request.getEmail());
            userRegisterDomain.setAccountDevice(AccountDeviceDTOAssembler.convertToDomain(deviceInfo));
            userRegisterDomain.setSource(request.getSource());
            //userRegisterDomain.setPassWord("123456"); // 默认密码，后续可以修改

            // 调用领域服务创建商户账户
            UserPerson userPerson = userPersonDomainService.createBusinessAccount(userRegisterDomain);
            if (Objects.isNull(userPerson)) {
                throw new BusinessException(UsersErrorCode.MERCHANT_ACCOUNT_CREATE_ERROR.getCode(), UsersErrorCode.MERCHANT_ACCOUNT_CREATE_ERROR.getMsg());
            }

            LoginResponse buildLoginResponse = userPersonDTOAssembler.buildLoginResponse(userPerson, request.getSource(), deviceInfo);


            return buildLoginResponse;
        } catch (Exception e) {
            log.error("验证短信验证码并创建商户账户失败", e);
            throw e;
        }
    }

    @Override
    @LoginLog(loginType = LoginType.EMAIL_CODE, autoInfer = true, description = "商户邮箱验证码注册登录")
    @Transactional(rollbackFor = Exception.class)
    public LoginResponse verifyEmailCodeAndCreateMerchant(VerifyEmailCodeAndLoginRequest request, String clientIp) {
        try {
            // 处理设备信息
            AccountDeviceDTO deviceInfo = request.getDeviceInfo();
            if (deviceInfo == null) {
                deviceInfo = new AccountDeviceDTO();
            }
            deviceInfo.setIpAddress(clientIp);
            deviceInfo.setLoginTime(LocalDateTime.now());

            // 验证邮箱验证码
            boolean verified = verificationService.verifyEmailCode(request.getEmail(), request.getCode(), request.getBusinessType());
            if (!verified) {
                throw new BusinessException(UsersErrorCode.EMAIL_CODE_CHECK_ERROR.getCode(), UsersErrorCode.EMAIL_CODE_CHECK_ERROR.getMsg());
            }
            // 创建领域对象
            UserRegisterDomain userRegisterDomain = new UserRegisterDomain();
            userRegisterDomain.setEmail(request.getEmail());
            userRegisterDomain.setAccountDevice(AccountDeviceDTOAssembler.convertToDomain(deviceInfo));
            userRegisterDomain.setSource(request.getPlatform());
            //userRegisterDomain.setPassWord("123456"); // 默认密码，后续可以修改

            // 调用领域服务创建商户账户
            UserPerson userPerson = userPersonDomainService.createBusinessAccount(userRegisterDomain);
            if (Objects.isNull(userPerson)) {
                throw new BusinessException(UsersErrorCode.MERCHANT_ACCOUNT_CREATE_ERROR.getCode(), UsersErrorCode.MERCHANT_ACCOUNT_CREATE_ERROR.getMsg());
            }

            LoginResponse buildLoginResponse = userPersonDTOAssembler.buildLoginResponse(userPerson, request.getPlatform(), deviceInfo);
            return buildLoginResponse;
        } catch (Exception e) {
            log.error("验证邮箱验证码并创建商户账户失败", e);
            throw e;
        }
    }


    private UserResponse convertToUserResponse(UserPerson userPerson, CustomerAccount account) {
        UserResponse userResponse = new UserResponse();
        userResponse.setId(userPerson.getId());
        userResponse.setUsername(account.getNickName());
        userResponse.setPhone(userPerson.getPhone());
        userResponse.setEmail(userPerson.getEmail());
        return userResponse;
    }


    /**
     * 查询三方用户
     *
     * @param type
     * @param callback
     * @return
     */
    private AuthUserResponse queryAuthUser(String type, AuthCallbackRequest callback) {
        // 直接调用领域服务处理三方登录回调
        AuthCallback authCallback = new AuthCallback();
        BeanUtil.copyProperties(callback, authCallback);

        // 转换为AuthCallbackRequest

        AuthRequest authRequest = factory.get(type);
        AuthResponse response = authRequest.login(authCallback);
        log.info("UserPersonApplicationServiceImpl#queryAuthUser【response】= {}", JSONObject.toJSONString(response));

        if (response == null || Objects.isNull(response.getData())) {
            throw new BusinessException(UsersErrorCode.USER_AUTHORIZATION_ERROR.getCode(), UsersErrorCode.USER_AUTHORIZATION_ERROR.getMsg());
        }

        String responseStr = JSONObject.toJSONString(response.getData());

        // 转换为AuthUserDto
        AuthUserDto authUserDto = JSONObject.parseObject(responseStr, AuthUserDto.class);
        return authUserDto.convertToAuthUserResponse();
    }

    /**
     * 查询三方账户是存在
     *
     * @param accessToken
     * @return
     */
    private LoginResponse queryThirdPartyAccount(String accessToken) {
        UserThirdPartyAuth userThirdPartyAuth = userThirdPartyAuthRespoitory.findByAccessToken(accessToken);

        if (userThirdPartyAuth != null) {
            // 查询用户账号信息
            CustomerAccount account = customerAccountRepository.findById(userThirdPartyAuth.getCustomerAccountId())
                    .orElseThrow(() -> new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg()));

            // 查询用户基本信息
            UserPerson userPerson = userPersonRepository.findById(account.getUserPersonId())
                    .orElseThrow(() -> new BusinessException(UsersErrorCode.USER_BASIC_INFO_NOT_EXIST.getCode(), UsersErrorCode.USER_BASIC_INFO_NOT_EXIST.getMsg()));

            // 转换为 UserResponse
            UserResponse UserInfoDTOResponse = convertToUserResponse(userPerson, account);

            // 构建返回
            LoginResponse response = new LoginResponse();
            response.setUserId(userPerson.getId());
            UserInfoDTO UserInfoDTO = new UserInfoDTO();
            UserInfoDTO.setUserId(UserInfoDTOResponse.getId());
            UserInfoDTO.setNickname(UserInfoDTOResponse.getNickname());
            UserInfoDTO.setPhone(UserInfoDTOResponse.getPhone());
            UserInfoDTO.setEmail(UserInfoDTOResponse.getEmail());
            UserInfoDTO.setBusinessAccountId(account.getId());
            response.setUserInfo(UserInfoDTO);
            return response;

        }
        return null;
    }


    @Override
    public UnifiedLoginResponse unifiedLogin(UnifiedLoginRequest request) {
        try {
            // 1. 预处理和验证
            preprocessLoginRequest(request);

            //  查找并执行处理器
            LoginHandler handler = findSuitableHandler(request);
            return handler.handle(request);

        } catch (BusinessException e) {
            log.error("统一登录失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("统一登录失败", e);
            throw new BusinessException(UsersErrorCode.LOGIN_FAILED.getCode(), UsersErrorCode.LOGIN_FAILED.getMsg());
        }
    }

    @Override
    @LoginLog(loginType = LoginType.PASSWORD, autoInfer = true, description = "管理员密码登录")
    public LoginResponse passwordLogin(PasswordLoginRequest request, String clientIp) {
        // 调用账号密码登录服务
        PasswordLoginResponse passwordLoginResponse = userLoginService.passwordLogin(request, clientIp);

        // 管理员登录不会触发二次验证，直接返回登录响应
        return passwordLoginResponse.getLoginResponse();
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 预处理登录请求
     */
    private void preprocessLoginRequest(UnifiedLoginRequest request) {
        log.info("统一登录请求: {}", JSONObject.toJSONString(request));

        // 设置默认流程状态
        if (request.getFlowStatus() == null) {
            request.setFlowStatus(BindingPhoneVerifyStatus.INIT);
        }

        // 设置客户端IP（如果未设置）
        if (request.getClientIp() == null) {
            // 可以从上下文中获取IP地址
            // request.setClientIp(getClientIpFromContext());
        }
    }

    /**
     * 查找合适的处理器
     */
    private LoginHandler findSuitableHandler(UnifiedLoginRequest request) {
        for (LoginHandler handler : loginHandlers) {
            if (handler.supports(request.getLoginType(), request.getFlowStatus())) {
                log.debug("找到处理器: {} 用于处理 loginType={}, flowStatus={}",
                        handler.getClass().getSimpleName(),
                        request.getLoginType(),
                        request.getFlowStatus());
                return handler;
            }
        }

        log.error("未找到合适的处理器: loginType={}, flowStatus={}",
                request.getLoginType(), request.getFlowStatus());
        throw new BusinessException(UsersErrorCode.UNSUPPORTED_LOGIN_TYPE_OR_STATUS.getCode(),
                UsersErrorCode.UNSUPPORTED_LOGIN_TYPE_OR_STATUS.getMsg());
    }
}
