package com.yuanchuan.user.application.service.impl;

import com.alibaba.fastjson2.JSON;
import com.yuanchuan.common.constant.user.UserLoginRedisKeys;
import com.yuanchuan.common.enums.users.UsersErrorCode;
import com.yuanchuan.common.enums.users.login.LoginType;
import com.yuanchuan.common.exception.BusinessException;
import com.yuanchuan.common.utils.DataMaskingUtil;
import com.yuanchuan.common.utils.RedisUtil;
import com.yuanchuan.common.utils.SecondaryVerificationTokenUtil;
import com.yuanchuan.user.api.dto.AccountDeviceDTO;
import com.yuanchuan.user.api.dto.NewDeviceVerificationState;
import com.yuanchuan.user.api.request.HandleSmsSecondaryVerificationRequest;
import com.yuanchuan.user.api.request.PasswordLoginRequest;
import com.yuanchuan.user.api.request.VerifyEmailCodeAndLoginRequest;
import com.yuanchuan.user.api.request.VerifySmsCodeAndBindPhoneRequest;
import com.yuanchuan.user.api.response.LoginResponse;
import com.yuanchuan.user.api.response.VerifySmsCodeAndLoginResponse;
import com.yuanchuan.user.api.service.VerificationService;
import com.yuanchuan.user.application.service.DeviceManagementApplicationService;
import com.yuanchuan.user.application.service.IpValidationApplicationService;
import com.yuanchuan.user.application.service.SecondaryVerificationApplicationService;
import com.yuanchuan.user.application.service.assembler.UserPersonDTOAssembler;
import com.yuanchuan.user.context.enums.RegisterSourceEnum;
import com.yuanchuan.user.context.enums.SmsAndEmailBusinessType;
import com.yuanchuan.user.domain.model.CustomerAccount;
import com.yuanchuan.user.domain.model.UserPerson;
import com.yuanchuan.user.domain.service.UserPersonDomainService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 二次验证应用服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/20
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class SecondaryVerificationApplicationServiceImpl implements SecondaryVerificationApplicationService {

    @Autowired
    private UserPersonDomainService userPersonDomainService;

    @Autowired
    private VerificationService verificationService;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private UserPersonDTOAssembler userPersonDTOAssembler;

    @Autowired
    private DeviceManagementApplicationService deviceManagementApplicationService;

    @Autowired
    private IpValidationApplicationService ipValidationApplicationService;

    @Override
    public LoginResponse handleSecondaryVerification(
            HandleSmsSecondaryVerificationRequest request,
            UserPerson userPerson,
            AccountDeviceDTO deviceInfo,
            String clientIp) {

        log.info("处理短信登录的二次验证: userId={}, deviceId={}",
                userPerson.getId(), deviceInfo.getDeviceId());

        // 使用分布式锁保护二次验证流程
        return processSecondaryVerificationWithLock(userPerson.getId(), deviceInfo.getDeviceId(), () -> {
            // 1. 获取验证状态
            NewDeviceVerificationState newDeviceState = getNewDeviceState(userPerson.getId(), deviceInfo.getDeviceId());
            if (Objects.isNull(newDeviceState)) {
                throw new BusinessException(UsersErrorCode.SECONDARY_VERIFICATION_STATE_EXPIRED.getCode(),
                        UsersErrorCode.SECONDARY_VERIFICATION_STATE_EXPIRED.getMsg());
            }

            //2. 验证IP地址一致性
            //ipValidationApplicationService.validateIpAddressConsistency(
            //        ne``wDeviceState.getOriginalIpAddress(), clientIp, userPerson.getId());

            boolean verified = false;
            // 3. 使用缓存中的真实手机号替换前端传来的，防止被篡改
            // 4. 验证短信验证码
            if (request.getBusinessType() == SmsAndEmailBusinessType.SECONDARY_EMAIL_VERIFICATION) {
                request.setEmail(newDeviceState.getTargetEmail());
                verified = verificationService.verifyEmailCode(request.getEmail(), request.getCode(), request.getBusinessType());
            } else {
                request.setPhone(newDeviceState.getTargetPhone());
                verified = verificationService.verifySmsCode(request.getPhone(), request.getCode(), request.getBusinessType());
            }
            log.info("二次验证使用缓存手机号: userId={}, deviceId={}, realPhone={}",
                    userPerson.getId(), deviceInfo.getDeviceId(), DataMaskingUtil.maskPhone(newDeviceState.getTargetPhone()));


            if (!verified) {
                throw new BusinessException(UsersErrorCode.SMS_CODE_CHECK_ERROR.getCode(),
                        UsersErrorCode.SMS_CODE_CHECK_ERROR.getMsg());
            }

            // 5. 绑定设备
            deviceManagementApplicationService.bindUserDevice(userPerson, deviceInfo);

            // 6. 清除验证状态
            clearNewDeviceState(userPerson.getId(), deviceInfo.getDeviceId());

            log.info("手机号二次验证成功，绑定新设备: userId={}, deviceId={}",
                    userPerson.getId(), deviceInfo.getDeviceId());


            // 7. 构建成功响应
            return buildSuccessfulEmailLoginResponse(userPerson, request, deviceInfo, clientIp);
        });
    }


    @Override
    public void triggerPhoneSecondaryVerification(
            UserPerson userPerson,
            VerifySmsCodeAndBindPhoneRequest request,
            AccountDeviceDTO deviceInfo,
            String clientIp) {

        log.info("触发手机号二次验证: userId={}, deviceId={}",
                userPerson.getId(), deviceInfo.getDeviceId());
        // 检查用户是否有手机号
        String userPhone = userPerson.getPhone();
        if (StringUtils.isBlank(userPhone)) {
            throw new BusinessException(UsersErrorCode.SECONDARY_VERIFICATION_PHONE_NOT_EXIST.getCode(),
                    UsersErrorCode.SECONDARY_VERIFICATION_PHONE_NOT_EXIST.getMsg());
        }

        // 存储新设备验证状态
        NewDeviceVerificationState state = new NewDeviceVerificationState();
        state.setUserId(userPerson.getId());
        state.setDeviceId(deviceInfo.getDeviceId());
        state.setOriginalLoginMethod(LoginType.SMS_CODE.getName());
        state.setTargetPhone(userPerson.getPhone()); // 前置确定手机号
        state.setTargetEmail(userPerson.getEmail()); // 保存邮箱作为备用
        state.setOriginalIpAddress(clientIp); // 记录原始IP地址
        state.setCreateTime(LocalDateTime.now());
        state.setExpireTime(LocalDateTime.now().plusMinutes(UserLoginRedisKeys.NEW_DEVICE_VERIFICATION_EXPIRE_MINUTES));

        saveNewDeviceState(state);


        // 发送手机号验证码
        //verificationService.sendSmsCode(userPhone, SmsAndEmailBusinessType.SECONDARY_PHONE_VERIFICATION);


    }

    @Override
    public void triggerEmailSecondaryVerification(
            UserPerson userPerson,
            VerifyEmailCodeAndLoginRequest request,
            AccountDeviceDTO deviceInfo,
            String clientIp) {

        log.info("触发邮箱二次验证: userId={}, deviceId={}",
                userPerson.getId(), deviceInfo.getDeviceId());
        // 检查用户是否有邮箱
        String userEmail = userPerson.getEmail();
        if (StringUtils.isBlank(userEmail)) {
            throw new BusinessException(UsersErrorCode.EMAIL_NOT_BINDING_PHONE.getCode(),
                    UsersErrorCode.EMAIL_NOT_BINDING_PHONE.getMsg());
        }


        // 存储新设备验证状态（前置确定验证目标）
        NewDeviceVerificationState state = new NewDeviceVerificationState();
        state.setUserId(userPerson.getId());
        state.setDeviceId(deviceInfo.getDeviceId());
        state.setOriginalLoginMethod(LoginType.EMAIL_CODE.getName());
        state.setTargetEmail(userPerson.getEmail()); // 前置确定邮箱地址
        state.setTargetPhone(userPerson.getPhone()); // 保存手机号作为备用
        state.setOriginalIpAddress(clientIp); // 记录原始IP地址
        state.setCreateTime(LocalDateTime.now());
        state.setExpireTime(LocalDateTime.now().plusMinutes(UserLoginRedisKeys.NEW_DEVICE_VERIFICATION_EXPIRE_MINUTES));

        saveNewDeviceState(state);


        // 发送邮箱验证码
        //verificationService.sendEmailCode(userEmail, SmsAndEmailBusinessType.SECONDARY_EMAIL_VERIFICATION);


    }

    @Override
    public void triggerPasswordSecondaryVerification(
            UserPerson userPerson,
            PasswordLoginRequest request,
            AccountDeviceDTO deviceInfo,
            String clientIp) {

        log.info("触发密码登录的二次验证: userId={}, deviceId={}",
                userPerson.getId(), deviceInfo.getDeviceId());

        // 存储新设备验证状态
        NewDeviceVerificationState state = new NewDeviceVerificationState();
        state.setUserId(userPerson.getId());
        state.setDeviceId(deviceInfo.getDeviceId());
        state.setOriginalLoginMethod(LoginType.PASSWORD.getName());
        state.setTargetPhone(userPerson.getPhone()); // 密码登录用户肯定有手机号
        state.setTargetEmail(userPerson.getEmail()); // 保存邮箱（可能为null）
        state.setOriginalIpAddress(clientIp); // 记录原始IP地址
        state.setCreateTime(LocalDateTime.now());
        state.setExpireTime(LocalDateTime.now().plusMinutes(UserLoginRedisKeys.NEW_DEVICE_VERIFICATION_EXPIRE_MINUTES));

        saveNewDeviceState(state);


    }

    @Override
    public NewDeviceVerificationState getNewDeviceState(Long userId, String deviceId) {
        String key = UserLoginRedisKeys.getNewDeviceVerificationKey(userId, deviceId);
        String stateJson = redisUtil.get(key);
        if (StringUtils.isNotBlank(stateJson)) {
            try {
                return JSON.parseObject(stateJson, NewDeviceVerificationState.class);
            } catch (Exception e) {
                log.error("解析新设备验证状态失败，用户ID: {}, 设备ID: {}", userId, deviceId, e);
                // 解析失败时清除损坏的数据
                redisUtil.delete(key);
            }
        }
        return null;
    }

    @Override
    public void saveNewDeviceState(NewDeviceVerificationState state) {
        String key = UserLoginRedisKeys.getNewDeviceVerificationKey(state.getUserId(), state.getDeviceId());
        String stateJson = JSON.toJSONString(state);
        // 使用统一的过期时间常量
        redisUtil.set(key, stateJson, UserLoginRedisKeys.NEW_DEVICE_VERIFICATION_EXPIRE_MINUTES, TimeUnit.MINUTES);

        log.info("保存新设备验证状态，用户ID: {}, 设备ID: {}, 原始登录方式: {}",
                state.getUserId(), state.getDeviceId(), state.getOriginalLoginMethod());
    }

    @Override
    public void clearNewDeviceState(Long userId, String deviceId) {
        try {
            String key = UserLoginRedisKeys.getNewDeviceVerificationKey(userId, deviceId);
            redisUtil.delete(key);
            log.debug("清除新设备验证状态: userId={}, deviceId={}", userId, deviceId);
        } catch (Exception e) {
            log.error("清除新设备验证状态失败: userId={}, deviceId={}", userId, deviceId, e);
        }
    }

    @Override
    public boolean isInSecondaryVerificationState(Long userId, String deviceId) {
        NewDeviceVerificationState state = getNewDeviceState(userId, deviceId);
        return state != null && state.getExpireTime().isAfter(LocalDateTime.now());
    }

    // ==================== 私有工具方法 ====================

    /**
     * 使用分布式锁保护二次验证状态操作
     */
    private <T> T processSecondaryVerificationWithLock(Long userId, String deviceId, java.util.function.Supplier<T> action) {
        String lockKey = "secondary_verification_lock:" + userId + ":" + deviceId;
        RLock lock = redissonClient.getLock(lockKey);

        try {
            // 尝试获取锁，最多等待3秒，锁定30秒后自动释放
            if (lock.tryLock(3, 30, TimeUnit.SECONDS)) {
                try {
                    return action.get();
                } finally {
                    // 只有当前线程持有锁时才释放
                    if (lock.isHeldByCurrentThread()) {
                        lock.unlock();
                    }
                }
            } else {
                throw new BusinessException(UsersErrorCode.OPERATION_IN_PROGRESS.getCode(),
                        UsersErrorCode.OPERATION_IN_PROGRESS.getMsg());
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new BusinessException(UsersErrorCode.SYSTEM_ERROR.getCode(), UsersErrorCode.SYSTEM_ERROR.getMsg());
        }
    }


    /**
     * 构建成功的登录响应
     */
    private LoginResponse buildSuccessfulEmailLoginResponse(
            UserPerson userPerson, HandleSmsSecondaryVerificationRequest request,
            AccountDeviceDTO deviceInfo, String clientIp) {
        LoginResponse loginResponse = userPersonDTOAssembler.buildLoginResponse(userPerson, null, deviceInfo);
        if (request.getBusinessType() == SmsAndEmailBusinessType.SECONDARY_PHONE_VERIFICATION) {
            // 检查跨平台用户
            boolean isCrossPlatformUser = false;
            if (request.getRegisterSource() == RegisterSourceEnum.APP) {
                CustomerAccount customerAccount = userPerson.getCustomerAccount();
                if (customerAccount != null) {
                    String userRegisterSource = customerAccount.getRegisterSource();
                    boolean isFirstAppLogin = customerAccount.getFirstAppLoginAt() == null;
                    boolean isNonAppUser = StringUtils.isNotBlank(userRegisterSource) &&
                            !RegisterSourceEnum.APP.getName().equalsIgnoreCase(userRegisterSource);

                    isCrossPlatformUser = isNonAppUser && isFirstAppLogin;

                    if (isFirstAppLogin) {
                        customerAccount.setFirstAppLoginAt(LocalDateTime.now());
                        userPersonDomainService.updateCustomerAccount(customerAccount);
                    }
                }
            }
            // 设置响应属性
            loginResponse.setIsCrossPlatformUser(isCrossPlatformUser);
        }
        return loginResponse;
    }


    /**
     * 构建需要二次验证的响应
     */
    private VerifySmsCodeAndLoginResponse buildSecondaryVerificationSmsResponse(
            UserPerson userPerson, AccountDeviceDTO deviceInfo) {

        // 生成加密token替代明文userId
        String secondaryVerificationToken = SecondaryVerificationTokenUtil.generateSecondaryVerificationToken(
                userPerson.getId(), deviceInfo.getDeviceId());

        VerifySmsCodeAndLoginResponse response = new VerifySmsCodeAndLoginResponse();
        LoginResponse loginResponse = new LoginResponse();
        loginResponse.setSecondaryVerificationToken(secondaryVerificationToken);
        //loginResponse.setNeedSecondaryVerification(true);

        response.setLoginResponse(loginResponse);
        response.setNeedSecondaryVerification(true);

        return response;
    }

    /**
     * 构建需要二次验证的邮箱响应
     */
    private LoginResponse buildSecondaryVerificationEmailResponse(
            UserPerson userPerson, AccountDeviceDTO deviceInfo) {

        // 生成加密token替代明文userId
        String secondaryVerificationToken = SecondaryVerificationTokenUtil.generateSecondaryVerificationToken(
                userPerson.getId(), deviceInfo.getDeviceId());

        LoginResponse response = new LoginResponse();
        response.setSecondaryVerificationToken(secondaryVerificationToken);
        //response.setNeedSecondaryVerification(true);

        return response;
    }
}
