package com.yuanchuan.user.application.service.handler;

import com.yuanchuan.authentication.context.enums.BindingPhoneVerifyStatus;
import com.yuanchuan.common.enums.users.UsersErrorCode;
import com.yuanchuan.common.enums.users.login.LoginType;
import com.yuanchuan.common.exception.BusinessException;
import com.yuanchuan.user.api.request.PasswordLoginRequest;
import com.yuanchuan.user.api.request.UnifiedLoginRequest;
import com.yuanchuan.user.api.request.UnifiedLoginResponse;
import com.yuanchuan.user.api.response.PasswordLoginResponse;
import com.yuanchuan.user.application.service.impl.UserLoginServiceImpl;
import com.yuanchuan.user.application.annotation.LoginLog;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 账号密码登录处理器
 */
@Component
public class PasswordLoginHandler implements LoginHandler {

    @Autowired
    private UserLoginServiceImpl userLoginService;

    @Override
    @LoginLog(loginType = LoginType.PASSWORD, autoInfer = true, description = "C端账号密码登录")
    public UnifiedLoginResponse handle(UnifiedLoginRequest request) {
        // 验证参数
        if (StringUtils.isBlank(request.getPhone()) && StringUtils.isBlank(request.getEmail())) {
            throw new BusinessException(UsersErrorCode.ACCOUNT_EMPTY.getCode(), UsersErrorCode.ACCOUNT_EMPTY.getMsg());
        }
        if (StringUtils.isEmpty(request.getPassword())) {
            throw new BusinessException(UsersErrorCode.PASSWORD_EMPTY.getCode(), UsersErrorCode.PASSWORD_EMPTY.getMsg());
        }

        // 构建账号密码登录请求
        PasswordLoginRequest passwordRequest = new PasswordLoginRequest();
        passwordRequest.setAccount(StringUtils.isNotEmpty(request.getPhone()) ? request.getPhone() : request.getEmail());
        passwordRequest.setPassword(request.getPassword());
        passwordRequest.setDeviceInfo(request.getDeviceInfo());
        passwordRequest.setPlatform(request.getPlatform());

        // 调用账号密码登录服务
        PasswordLoginResponse passwordLoginResponse = userLoginService.passwordLogin(passwordRequest, request.getClientIp());

        // 构建统一登录响应
        UnifiedLoginResponse.UnifiedLoginResponseBuilder builder = UnifiedLoginResponse.builder();
        builder.loginResponse(passwordLoginResponse.getLoginResponse())
                .needContinue(false);
        return builder.build();
    }

    @Override
    public boolean supports(LoginType loginType, BindingPhoneVerifyStatus status) {
        return loginType == LoginType.PASSWORD && status == BindingPhoneVerifyStatus.INIT;
    }
}
