package com.yuanchuan.user.application.service.assembler;

import cn.hutool.core.bean.BeanUtil;
import com.yuanchuan.authentication.api.dto.AuthAccountDeviceApiDTO;
import com.yuanchuan.user.api.dto.AccountDeviceDTO;
import com.yuanchuan.user.domain.model.AccountDevice;
import org.springframework.beans.BeanUtils;


public class AccountDeviceDTOAssembler {


    /**
     * 将领域对象转换为DTO
     */
    public static AccountDeviceDTO convertToDTO(AccountDevice device) {
        if (device == null) {
            return null;
        }
        AccountDeviceDTO deviceDTO = BeanUtil.copyProperties(device, AccountDeviceDTO.class);
        return deviceDTO;
    }




    public static AccountDevice convertToDomain(AccountDeviceDTO dto) {
        if (dto == null) {
            return null;
        }
        AccountDevice device = new AccountDevice();
        BeanUtils.copyProperties(dto, device);
        return device;
    }

    public static AccountDevice authDeviceConvertToDomain(AuthAccountDeviceApiDTO dto) {
        if (dto == null) {
            return null;
        }
        AccountDevice device = new AccountDevice();
        BeanUtils.copyProperties(dto, device);
        return device;
    }

    public static AccountDeviceDTO authDeviceConvertToDTO(AuthAccountDeviceApiDTO req) {
        if (req == null) {
            return null;
        }
        AccountDeviceDTO deviceDTO = new AccountDeviceDTO();
        BeanUtils.copyProperties(req, deviceDTO);
        return deviceDTO;
    }




}
