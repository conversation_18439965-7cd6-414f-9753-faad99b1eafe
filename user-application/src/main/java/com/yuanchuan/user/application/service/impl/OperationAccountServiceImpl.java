package com.yuanchuan.user.application.service.impl;

import com.yuanchuan.common.response.PageResult;
import com.yuanchuan.user.api.dto.OperationAccountDTO;
import com.yuanchuan.user.api.request.OperationAccountCreateRequest;
import com.yuanchuan.user.api.request.OperationAccountQueryRequest;
import com.yuanchuan.user.api.request.OperationAccountUpdateRequest;
import com.yuanchuan.user.api.service.OperationAccountService;
import com.yuanchuan.user.application.service.OperationAccountApplicationService;
import jakarta.annotation.Resource;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 运营账号管理服务实现
 */
@Service
@DubboService(version = "1.0", group = "${dubbo.group}", delay = -1, retries = -1, timeout = 600000)
public class OperationAccountServiceImpl implements OperationAccountService {

    @Autowired
    private OperationAccountApplicationService operationAccountApplicationService;

    @Override
    public PageResult<OperationAccountDTO> queryOperationAccounts(OperationAccountQueryRequest request) {
        return operationAccountApplicationService.queryOperationAccounts(request);
    }

    @Override
    public Long createOperationAccount(OperationAccountCreateRequest request) {
        return operationAccountApplicationService.createOperationAccount(request);
    }

    @Override
    public Boolean updateOperationAccount(OperationAccountUpdateRequest request) {
        return operationAccountApplicationService.updateOperationAccount(request);
    }

    @Override
    public OperationAccountDTO getOperationAccountById(Long id) {
        return operationAccountApplicationService.getOperationAccountById(id);
    }
}
