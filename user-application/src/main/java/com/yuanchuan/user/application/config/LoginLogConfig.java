package com.yuanchuan.user.application.config;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import org.springframework.util.StringUtils;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.validation.annotation.Validated;



/**
 * 登录日志配置
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "app.login-log")
@Validated
public class LoginLogConfig {

    /**
     * 是否启用登录日志记录
     */
    private boolean enabled = true;

    /**
     * 是否启用AOP方式记录
     */
    private boolean aopEnabled = true;

    /**
     * 是否异步记录
     */
    private boolean async = true;

    /**
     * 是否记录成功日志
     */
    private boolean recordSuccess = true;

    /**
     * 是否记录失败日志
     */
    private boolean recordFailure = true;

    /**
     * 是否启用IP地址解析
     */
    private boolean ipLocationEnabled = false;

    /**
     * 异常登录检测阈值（小时内失败次数）
     */
    @Min(value = 1, message = "异常登录检测阈值不能小于1")
    @Max(value = 100, message = "异常登录检测阈值不能大于100")
    private int abnormalThreshold = 5;

    /**
     * 异常登录检测时间窗口（小时）
     */
    @Min(value = 1, message = "异常登录检测时间窗口不能小于1小时")
    @Max(value = 24, message = "异常登录检测时间窗口不能大于24小时")
    private int abnormalTimeWindow = 1;

    /**
     * 日志保留天数
     */
    @Min(value = 1, message = "日志保留天数不能小于1天")
    @Max(value = 365, message = "日志保留天数不能大于365天")
    private int retentionDays = 90;

    /**
     * 线程池配置
     */
    @Valid
    @NotNull
    private ThreadPool threadPool = new ThreadPool();

    /**
     * 线程池配置内部类
     */
    @Data
    @Validated
    public static class ThreadPool {

        /**
         * 核心线程数
         */
        @Min(value = 1, message = "核心线程数不能小于1")
        @Max(value = 50, message = "核心线程数不能大于50")
        private int coreSize = 2;

        /**
         * 最大线程数
         */
        @Min(value = 1, message = "最大线程数不能小于1")
        @Max(value = 100, message = "最大线程数不能大于100")
        private int maxSize = 5;

        /**
         * 队列容量
         */
        @Min(value = 1, message = "队列容量不能小于1")
        @Max(value = 10000, message = "队列容量不能大于10000")
        private int queueCapacity = 100;

        /**
         * 线程存活时间（秒）
         */
        @Min(value = 1, message = "线程存活时间不能小于1秒")
        @Max(value = 3600, message = "线程存活时间不能大于3600秒")
        private int keepAliveSeconds = 60;

        /**
         * 线程名称前缀
         */
        private String threadNamePrefix = "LoginLog-";

        /**
         * 是否等待任务完成再关闭
         */
        private boolean waitForTasksToCompleteOnShutdown = true;

        /**
         * 等待终止的秒数
         */
        @Min(value = 1, message = "等待终止时间不能小于1秒")
        @Max(value = 300, message = "等待终止时间不能大于300秒")
        private int awaitTerminationSeconds = 60;

        /**
         * 验证线程池配置的合理性
         */
        public void validate() {
            if (maxSize < coreSize) {
                throw new IllegalArgumentException(
                    String.format("最大线程数(%d)不能小于核心线程数(%d)", maxSize, coreSize));
            }

            if (!StringUtils.hasText(threadNamePrefix)) {
                throw new IllegalArgumentException("线程名称前缀不能为空");
            }
        }
    }
}