package com.yuanchuan.user.application.service.assembler;

public class DeviceInfoConverter {
    //public static DeviceInfoDTO toDTO(DeviceInfo deviceInfo) {
    //    if (deviceInfo == null) {
    //        return null;
    //    }
    //    return DeviceInfoDTO.builder()
    //            .deviceId(deviceInfo.getDeviceId())
    //            .deviceType(deviceInfo.getDeviceType())
    //            .loginIp(deviceInfo.getLoginIp())
    //            .loginTime(deviceInfo.getLoginTime())
    //            .status(deviceInfo.getStatus())
    //            .userId(deviceInfo.getUserId())
    //            .build();
    //}
    //
    //public static DeviceInfo toDomain(DeviceInfoDTO dto) {
    //    if (dto == null) {
    //        return null;
    //    }
    //    return DeviceInfo.builder()
    //            .deviceId(dto.getDeviceId())
    //            .deviceType(dto.getDeviceType())
    //            .loginIp(dto.getLoginIp())
    //            .loginTime(dto.getLoginTime())
    //            .status(dto.getStatus())
    //            .userId(dto.getUserId())
    //            .build();
    //}
    //
    //public static DeviceInfo fromUserDeviceDTO(UserDeviceInfoDTO userDTO) {
    //    if (userDTO == null) {
    //        return null;
    //    }
    //    return DeviceInfo.builder()
    //            .deviceId(userDTO.getDeviceId())
    //            .deviceType(userDTO.getDeviceType())
    //            .loginIp(userDTO.getLastLoginIp())
    //            .loginTime(userDTO.getLastLoginTime())
    //            .status(userDTO.getStatus())
    //            .userId(userDTO.getUserId())
    //            .build();
    //}
    //
    //public static UserDeviceInfoDTO toUserDeviceDTO(DeviceInfoDTO deviceInfo) {
    //    if (deviceInfo == null) {
    //        return null;
    //    }
    //    return UserDeviceInfoDTO.builder()
    //            .deviceId(deviceInfo.getDeviceId())
    //            .deviceType(deviceInfo.getDeviceType())
    //            .deviceName(deviceInfo.getDeviceName())
    //            .deviceUUID(deviceInfo.getDeviceUUID())
    //            .lastLoginIp(deviceInfo.getLoginIp())
    //            .lastLoginTime(deviceInfo.getLoginTime())
    //            .status(deviceInfo.getStatus())
    //            .userId(deviceInfo.getUserId())
    //            .build();
    //}
}