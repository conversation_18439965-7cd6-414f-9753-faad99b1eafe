package com.yuanchuan.user.application.service.handler;

import com.yuanchuan.authentication.context.enums.BindingPhoneVerifyStatus;
import com.yuanchuan.common.enums.users.UsersErrorCode;
import com.yuanchuan.common.enums.users.login.LoginType;
import com.yuanchuan.common.exception.BusinessException;
import com.yuanchuan.user.api.request.UnifiedLoginRequest;
import com.yuanchuan.user.api.request.UnifiedLoginResponse;
import com.yuanchuan.user.api.request.VerifySmsCodeAndBindPhoneRequest;
import com.yuanchuan.user.api.response.VerifySmsCodeAndLoginResponse;
import com.yuanchuan.user.application.annotation.LoginLog;
import com.yuanchuan.user.application.service.impl.UserLoginServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 短信验证码登录处理器
 */
@Component
public class SmsLoginHandler implements LoginHandler {

    @Autowired
    private UserLoginServiceImpl userLoginService;

    @Override
    @LoginLog(loginType = LoginType.SMS_CODE, autoInfer = true, description = "C端短信验证码登录")
    public UnifiedLoginResponse handle(UnifiedLoginRequest request) {
        // 验证参数
        if (StringUtils.isEmpty(request.getPhone())) {
            throw new BusinessException(UsersErrorCode.PHONE_EMPTY.getCode(), UsersErrorCode.PHONE_EMPTY.getMsg());
        }
        if (StringUtils.isEmpty(request.getCode())) {
            throw new BusinessException(UsersErrorCode.VERIFICATION_CODE_EMPTY.getCode(), UsersErrorCode.VERIFICATION_CODE_EMPTY.getMsg());
        }

        // 构建短信验证码登录请求
        VerifySmsCodeAndBindPhoneRequest smsRequest = new VerifySmsCodeAndBindPhoneRequest();
        smsRequest.setPhone(request.getPhone());
        smsRequest.setCode(request.getCode());
        smsRequest.setDeviceInfo(request.getDeviceInfo());
        smsRequest.setSource(request.getPlatform());
        smsRequest.setBusinessType(request.getBusinessType());
        smsRequest.setRegisterSource(request.getRegisterSourceEnum());
        smsRequest.setSecondaryVerificationToken(request.getSecondaryVerificationToken());
        smsRequest.setPlatform(request.getPlatform());

        // 调用短信验证码登录服务
        VerifySmsCodeAndLoginResponse response = userLoginService.verifySmsCodeAndLogin(smsRequest, request.getClientIp());

        // 构建统一登录响应
        UnifiedLoginResponse unifiedResponse = new UnifiedLoginResponse();
        unifiedResponse.setLoginResponse(response.getLoginResponse());
        unifiedResponse.setNeedContinue(false);

        return unifiedResponse;
    }

    @Override
    public boolean supports(LoginType loginType, BindingPhoneVerifyStatus status) {
        return loginType == LoginType.SMS_CODE && status == BindingPhoneVerifyStatus.INIT;
    }
}
