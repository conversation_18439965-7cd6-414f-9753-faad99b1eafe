package com.yuanchuan.user.application.aspect;


import com.yuanchuan.common.enums.users.login.LoginLogCondition;
import com.yuanchuan.common.enums.users.login.LoginType;
import com.yuanchuan.common.enums.users.login.PlatformType;
import com.yuanchuan.user.api.dto.AccountDeviceDTO;
import com.yuanchuan.user.api.request.PasswordLoginRequest;
import com.yuanchuan.user.api.request.UnifiedLoginRequest;
import com.yuanchuan.user.api.request.VerifyEmailCodeAndLoginRequest;
import com.yuanchuan.user.api.request.VerifySmsCodeAndBindPhoneRequest;
import com.yuanchuan.user.api.service.LoginLogApplicationService;
import com.yuanchuan.user.application.annotation.LoginLog;
import com.yuanchuan.user.application.config.LoginLogConfig;

import jakarta.servlet.http.HttpServletRequest;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;


/**
 * 登录日志AOP切面
 */
@Slf4j
@Aspect
@Component
@Order(100) // 设置较低优先级，确保在事务切面之后执行
public class LoginLogAspect {

    // 防止重复记录的ThreadLocal
    private static final ThreadLocal<String> CURRENT_LOGIN_LOG = new ThreadLocal<>();

    @Autowired
    private LoginLogApplicationService loginLogApplicationService;

    @Autowired
    private LoginLogConfig loginLogConfig;



    @Around("@annotation(loginLog)")
    public Object around(ProceedingJoinPoint joinPoint, LoginLog loginLog) throws Throwable {
        // 检查是否启用登录日志
        if (!loginLogConfig.isEnabled() || !loginLogConfig.isAopEnabled()) {
            return joinPoint.proceed();
        }

        // 防止重复记录检查
        String methodSignature = joinPoint.getSignature().toString();
        if (methodSignature.equals(CURRENT_LOGIN_LOG.get())) {
            log.debug("检测到重复记录，跳过: {}", methodSignature);
            return joinPoint.proceed();
        }

        long startTime = System.currentTimeMillis();

        // 设置当前正在处理的方法
        CURRENT_LOGIN_LOG.set(methodSignature);

        try {
            // 提取方法参数信息
            LoginContext context = extractLoginContext(joinPoint, loginLog);

            try {
                // 执行原方法
                Object result = joinPoint.proceed();

                // 记录成功日志
                if (loginLog.recordSuccess() && loginLogConfig.isRecordSuccess()) {
                    recordSuccessLog(context, result, loginLog);
                }

                log.info("登录成功 - 描述: {}, 方法: {}, 耗时: {}ms, 平台: {}, 类型: {}",
                        context.getDescription(),
                        joinPoint.getSignature().getName(),
                        System.currentTimeMillis() - startTime,
                        context.getPlatform(),
                        context.getLoginType());

                return result;

            } catch (Exception e) {
                // 记录失败日志
                if (loginLog.recordFailure() && loginLogConfig.isRecordFailure()) {
                    recordFailureLog(context, e);
                }

                String description = !loginLog.description().isEmpty() ? loginLog.description() : joinPoint.getSignature().getName();
                log.warn("登录失败 - 描述: {}, 方法: {}, 耗时: {}ms, 平台: {}, 类型: {}, 错误: {}",
                        description,
                        joinPoint.getSignature().getName(),
                        System.currentTimeMillis() - startTime,
                        context.getPlatform(),
                        context.getLoginType(),
                        e.getMessage());

                throw e;
            }
        } finally {
            // 清理ThreadLocal，防止内存泄漏
            CURRENT_LOGIN_LOG.remove();
        }
    }

    /**
     * 提取登录上下文信息
     */
    private LoginContext extractLoginContext(ProceedingJoinPoint joinPoint, LoginLog loginLog) {
        LoginContext context = new LoginContext();
        Object[] args = joinPoint.getArgs();

        // 获取客户端IP（优先从请求对象中获取）
        String clientIp = getClientIpFromRequestObjects(args);
        if ("unknown".equals(clientIp)) {
            // 如果从请求对象中获取不到，尝试从HttpServletRequest获取
            clientIp = getClientIpFromArgs(args);
            if ("unknown".equals(clientIp)) {
                // 最后尝试从RequestContextHolder获取
                clientIp = getClientIp();
            }
        }
        context.setClientIp(clientIp);

        // 设置描述信息
        context.setDescription(!loginLog.description().isEmpty() ? loginLog.description() : joinPoint.getSignature().getName());

        if (loginLog.autoInfer()) {
            // 自动推断登录类型和平台类型
            inferFromMethodArgs(args, context);
        } else {
            // 使用注解指定的类型
            context.setLoginType(loginLog.loginType());
            context.setPlatform(loginLog.platform());
        }

        return context;
    }

    /**
     * 从方法参数中推断登录信息
     */
    private void inferFromMethodArgs(Object[] args, LoginContext context) {
        for (Object arg : args) {
            if (arg instanceof UnifiedLoginRequest) {
                UnifiedLoginRequest request = (UnifiedLoginRequest) arg;
                context.setLoginType(request.getLoginType());
                context.setPlatform(request.getPlatform());
                context.setDeviceInfo(request.getDeviceInfo());
                context.setUserAgent(request.getUserAgent());

                // 如果是统一登录且deviceInfo为空，尝试从HTTP请求头获取设备信息
                if (request.getLoginType() == LoginType.UNIFIED && request.getDeviceInfo() == null) {
                    AccountDeviceDTO deviceInfo = getDeviceInfoFromHttpHeaders();
                    if (deviceInfo != null) {
                        context.setDeviceInfo(deviceInfo);
                        log.debug("从HTTP请求头获取到设备信息: {}", deviceInfo.getDeviceId());
                    }
                }
                break;
            } else if (arg instanceof PasswordLoginRequest) {
                PasswordLoginRequest request = (PasswordLoginRequest) arg;
                context.setLoginType(LoginType.PASSWORD);
                context.setPlatform(request.getPlatform());
                context.setDeviceInfo(request.getDeviceInfo());
                break;
            } else if (arg instanceof VerifySmsCodeAndBindPhoneRequest) {
                VerifySmsCodeAndBindPhoneRequest request = (VerifySmsCodeAndBindPhoneRequest) arg;
                context.setLoginType(LoginType.SMS_CODE);
                context.setPlatform(request.getSource());
                context.setDeviceInfo(request.getDeviceInfo());
                break;
            } else if (arg instanceof VerifyEmailCodeAndLoginRequest) {
                VerifyEmailCodeAndLoginRequest request = (VerifyEmailCodeAndLoginRequest) arg;
                context.setLoginType(LoginType.EMAIL_CODE);
                context.setPlatform(request.getPlatform());
                context.setDeviceInfo(request.getDeviceInfo());
                break;
            }
        }

        // 设置默认值
        if (context.getLoginType() == null) {
            context.setLoginType(LoginType.PASSWORD);
        }
        if (context.getPlatform() == null) {
            context.setPlatform(PlatformType.CUSTOMER);
        }
    }

    /**
     * 记录成功日志
     */
    private void recordSuccessLog(LoginContext context, Object result, LoginLog loginLog) {
        try {
            // 根据条件判断是否记录
            if (loginLog.condition() == LoginLogCondition.REAL_LOGIN_SUCCESS_ONLY) {
                if (!isRealLoginSuccess(result)) {
                    log.debug("方法执行成功但未真正登录，不记录成功日志: {}", result.getClass().getSimpleName());
                    return;
                }
            }
            // LoginLogCondition.ALWAYS 情况下直接记录

            Long accountId = extractAccountId(result, context.getPlatform());


            loginLogApplicationService.recordLoginSuccess(
                    accountId,
                    context.getLoginType(),
                    context.getPlatform(),
                    context.getDeviceInfo(),
                    context.getClientIp(),
                    context.getUserAgent()
            );
        } catch (Exception e) {
            log.error("AOP记录登录成功日志失败", e);
        }
    }

    /**
     * 记录失败日志
     */
    private void recordFailureLog(LoginContext context, Exception exception) {
        try {
            loginLogApplicationService.recordLoginFailure(
                    null, // 失败时通常无法获取accountId
                    context.getLoginType(),
                    context.getPlatform(),
                    context.getDeviceInfo(),
                    context.getClientIp(),
                    context.getUserAgent()
            );
        } catch (Exception e) {
            log.error("AOP记录登录失败日志失败", e);
        }
    }

    /**
     * 判断是否真正登录成功
     */
    private boolean isRealLoginSuccess(Object result) {
        if (result == null) {
            return false;
        }

        // 如果是UnifiedLoginResponse，检查是否需要继续流程
        if (result.getClass().getSimpleName().equals("UnifiedLoginResponse")) {
            try {
                Object needContinue = result.getClass().getMethod("getNeedContinue").invoke(result);
                if (needContinue instanceof Boolean && (Boolean) needContinue) {
                    // 需要继续流程，说明未真正登录成功
                    log.debug("检测到needContinue=true，不记录登录成功日志");
                    return false;
                }

                // 检查是否有LoginResponse且包含有效的token
                Object loginResponse = result.getClass().getMethod("getLoginResponse").invoke(result);
                if (loginResponse != null) {
                    // 进一步检查LoginResponse是否包含有效的token
                    try {
                        Object userId = loginResponse.getClass().getMethod("getUserId").invoke(loginResponse);
                        Object tokenInfo = loginResponse.getClass().getMethod("getToken").invoke(loginResponse);

                        boolean hasUserId = userId != null;
                        boolean hasValidToken = false;

                        if (tokenInfo != null) {
                            Object accessToken = tokenInfo.getClass().getMethod("getAccessToken").invoke(tokenInfo);
                            hasValidToken = accessToken != null && !accessToken.toString().isEmpty();
                        }

                        log.debug("检查LoginResponse: hasValidToken={}, hasUserId={}", hasValidToken, hasUserId);
                        return hasValidToken && hasUserId;
                    } catch (Exception e) {
                        log.debug("检查LoginResponse内容失败，认为有效: {}", e.getMessage());
                        return true; // 如果有LoginResponse但无法检查内容，认为有效
                    }
                }

                log.debug("没有找到LoginResponse，不记录登录成功日志");
                return false;
            } catch (Exception e) {
                log.debug("检查UnifiedLoginResponse失败: {}", e.getMessage());
                return false;
            }
        }

        // 如果是LoginResponse，检查是否包含有效token
        if (result.getClass().getSimpleName().equals("LoginResponse")) {
            try {
                Object userId = result.getClass().getMethod("getUserId").invoke(result);
                Object tokenInfo = result.getClass().getMethod("getToken").invoke(result);

                boolean hasUserId = userId != null;
                boolean hasValidToken = false;

                if (tokenInfo != null) {
                    Object accessToken = tokenInfo.getClass().getMethod("getAccessToken").invoke(tokenInfo);
                    hasValidToken = accessToken != null && !accessToken.toString().isEmpty();
                }

                log.debug("直接LoginResponse检查: hasValidToken={}, hasUserId={}", hasValidToken, hasUserId);
                return hasValidToken && hasUserId;
            } catch (Exception e) {
                log.debug("检查LoginResponse内容失败，认为有效: {}", e.getMessage());
                return true; // 如果是LoginResponse但无法检查内容，认为有效
            }
        }

        // 其他情况，检查是否包含LoginResponse
        try {
            Object loginResponse = result.getClass().getMethod("getLoginResponse").invoke(result);
            if (loginResponse != null) {
                // 递归检查LoginResponse
                return isRealLoginSuccess(loginResponse);
            }
        } catch (Exception e) {
            log.debug("检查其他类型的LoginResponse失败: {}", e.getMessage());
        }

        log.debug("未能识别为有效登录结果: {}", result.getClass().getSimpleName());
        return false;
    }

    /**
     * 从返回结果中提取账户ID
     */
    private Long extractAccountId(Object result, PlatformType platform) {
        if (result == null) {
            return null;
        }

        // 如果是直接的LoginResponse
        if (result.getClass().getSimpleName().equals("LoginResponse")) {
            try {
                Object userId = result.getClass().getMethod("getUserId").invoke(result);
                if (userId instanceof Long) {
                    return (Long) userId;
                }
            } catch (Exception e) {
                log.debug("从直接LoginResponse提取账户ID失败", e);
            }
        }

        // 尝试从其他类型中提取
        try {
            Object loginResponse = result.getClass().getMethod("getLoginResponse").invoke(result);
            if (loginResponse != null) {
                Object userId = loginResponse.getClass().getMethod("getUserId").invoke(loginResponse);
                if (userId instanceof Long) {
                    return (Long) userId;
                }
            }
        } catch (Exception e) {
            log.debug("从嵌套对象提取账户ID失败", e);
        }

        return null;
    }

    /**
     * 从请求对象中获取客户端IP地址
     * @param args 方法参数数组
     * @return 客户端IP地址，获取不到返回"unknown"
     */
    private String getClientIpFromRequestObjects(Object[] args) {
        if (args == null || args.length == 0) {
            return "unknown";
        }

        // 遍历方法参数，查找包含clientIp字段的请求对象
        for (Object arg : args) {
            if (arg instanceof UnifiedLoginRequest) {
                UnifiedLoginRequest request = (UnifiedLoginRequest) arg;
                String clientIp = request.getClientIp();
                if (!StringUtils.isEmpty(clientIp)) {
                    log.debug("从UnifiedLoginRequest获取到IP: {}", clientIp);
                    return clientIp;
                }
            }
            // 其他请求类型暂时没有clientIp字段，但可以从设备信息中获取
            else if (arg instanceof PasswordLoginRequest) {
                PasswordLoginRequest request = (PasswordLoginRequest) arg;
                if (request.getDeviceInfo() != null && !StringUtils.isEmpty(request.getDeviceInfo().getIpAddress())) {
                    String clientIp = request.getDeviceInfo().getIpAddress();
                    log.debug("从PasswordLoginRequest.deviceInfo获取到IP: {}", clientIp);
                    return clientIp;
                }
            }
            else if (arg instanceof VerifySmsCodeAndBindPhoneRequest) {
                VerifySmsCodeAndBindPhoneRequest request = (VerifySmsCodeAndBindPhoneRequest) arg;
                if (request.getDeviceInfo() != null && !StringUtils.isEmpty(request.getDeviceInfo().getIpAddress())) {
                    String clientIp = request.getDeviceInfo().getIpAddress();
                    log.debug("从VerifySmsCodeAndBindPhoneRequest.deviceInfo获取到IP: {}", clientIp);
                    return clientIp;
                }
            }
            else if (arg instanceof VerifyEmailCodeAndLoginRequest) {
                VerifyEmailCodeAndLoginRequest request = (VerifyEmailCodeAndLoginRequest) arg;
                if (request.getDeviceInfo() != null && !StringUtils.isEmpty(request.getDeviceInfo().getIpAddress())) {
                    String clientIp = request.getDeviceInfo().getIpAddress();
                    log.debug("从VerifyEmailCodeAndLoginRequest.deviceInfo获取到IP: {}", clientIp);
                    return clientIp;
                }
            }
        }

        return "unknown";
    }

    /**
     * 从方法参数中获取客户端IP地址
     * @param args 方法参数数组
     * @return 客户端IP地址，获取不到返回"unknown"
     */
    private String getClientIpFromArgs(Object[] args) {
        if (args == null || args.length == 0) {
            return "unknown";
        }

        // 遍历方法参数，查找HttpServletRequest
        for (Object arg : args) {
            if (arg instanceof HttpServletRequest) {
                HttpServletRequest request = (HttpServletRequest) arg;
                return getClientIpFromRequest(request);
            }
        }

        return "unknown";
    }

    /**
     * 获取客户端IP地址（从RequestContextHolder）
     */
    private String getClientIp() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                String clientIp = getClientIpFromRequest(request);
                log.debug("从RequestContextHolder获取到IP: {}", clientIp);
                return clientIp;
            } else {
                log.debug("RequestContextHolder.getRequestAttributes() 返回null");
            }
        } catch (Exception e) {
            log.warn("从RequestContextHolder获取客户端IP失败", e);
        }
        return "unknown";
    }

    /**
     * 从HttpServletRequest中获取客户端IP
     */
    private String getClientIpFromRequest(HttpServletRequest request) {
        String clientIp = request.getHeader("X-Forwarded-For");
        if (clientIp == null || clientIp.isEmpty() || "unknown".equalsIgnoreCase(clientIp)) {
            clientIp = request.getHeader("Proxy-Client-IP");
        }
        if (clientIp == null || clientIp.isEmpty() || "unknown".equalsIgnoreCase(clientIp)) {
            clientIp = request.getHeader("WL-Proxy-Client-IP");
        }
        if (clientIp == null || clientIp.isEmpty() || "unknown".equalsIgnoreCase(clientIp)) {
            clientIp = request.getHeader("HTTP_CLIENT_IP");
        }
        if (clientIp == null || clientIp.isEmpty() || "unknown".equalsIgnoreCase(clientIp)) {
            clientIp = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (clientIp == null || clientIp.isEmpty() || "unknown".equalsIgnoreCase(clientIp)) {
            clientIp = request.getRemoteAddr();
        }

        // 如果是多个代理，第一个IP为客户端真实IP
        if (clientIp != null && clientIp.contains(",")) {
            clientIp = clientIp.split(",")[0];
        }
        return clientIp;
    }



    /**
     * 从HTTP请求头中获取设备信息
     * @return 设备信息DTO，如果获取失败则返回null
     */
    private AccountDeviceDTO getDeviceInfoFromHttpHeaders() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();

                // 从请求头获取设备信息
                String deviceId = request.getHeader("deviceid");
                String deviceName = request.getHeader("devicename");
                String deviceType = request.getHeader("devicetype");
                String userAgent = request.getHeader("User-Agent");

                if (!StringUtils.isEmpty(deviceId)) {
                    return AccountDeviceDTO.builder()
                        .deviceId(deviceId)
                        .deviceName(deviceName)
                        .deviceType(deviceType)
                        .userAgent(userAgent)
                        .build();
                }
            }
        } catch (Exception e) {
            log.debug("从HTTP请求头获取设备信息失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 登录上下文信息
     */
    @Data
    private static class LoginContext {
        private LoginType loginType;
        private PlatformType platform;
        private AccountDeviceDTO deviceInfo;
        private String clientIp;
        private String userAgent;
        private String description;

    }
}
