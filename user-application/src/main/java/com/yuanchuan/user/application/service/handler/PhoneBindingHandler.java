package com.yuanchuan.user.application.service.handler;

import com.yuanchuan.authentication.api.dto.AuthAccountDeviceApiDTO;
import com.yuanchuan.authentication.api.request.PhoneBindingRequest;
import com.yuanchuan.authentication.api.response.UserLoginEmailBindingVerifyResponse;
import com.yuanchuan.authentication.api.service.AuthenticationService;
import com.yuanchuan.authentication.context.enums.BindingPhoneVerifyStatus;
import com.yuanchuan.common.enums.users.UsersErrorCode;
import com.yuanchuan.common.enums.users.login.LoginLogCondition;
import com.yuanchuan.common.enums.users.login.LoginType;
import com.yuanchuan.common.exception.BusinessException;
import com.yuanchuan.common.utils.DataMaskingUtil;
import com.yuanchuan.user.api.dto.AuthUserResponse;
import com.yuanchuan.user.api.dto.UserInfoDTO;
import com.yuanchuan.user.api.request.CreateNewAccountWithEmailRequest;
import com.yuanchuan.user.api.request.UnifiedLoginRequest;
import com.yuanchuan.user.api.request.UnifiedLoginResponse;
import com.yuanchuan.user.api.response.LoginResponse;
import com.yuanchuan.user.api.response.PhoneExistsResponse;
import com.yuanchuan.user.api.service.VerificationService;
import com.yuanchuan.user.application.annotation.LoginLog;
import com.yuanchuan.user.application.service.assembler.AccountDeviceDTOAssembler;
import com.yuanchuan.user.application.service.assembler.UserPersonDTOAssembler;
import com.yuanchuan.user.application.service.impl.UserLoginServiceImpl;
import com.yuanchuan.user.domain.model.UserPerson;
import com.yuanchuan.user.domain.service.UserPersonDomainService;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 手机号绑定处理器
 */
@Component
public class PhoneBindingHandler implements LoginHandler {

    @Autowired
    private UserLoginServiceImpl userLoginService;

    @Autowired
    private VerificationService verificationService;

    @Autowired
    private UserPersonDomainService userPersonDomainService;

    @Autowired
    private UserPersonDTOAssembler userPersonDTOAssembler;

    @DubboReference(version = "1.0", group = "${dubbo.consumer.group.authentication}")
    private AuthenticationService authenticationService;

    @Override
    @LoginLog(loginType = LoginType.UNIFIED, autoInfer = true,
             description = "手机号绑定流程",
             condition = LoginLogCondition.REAL_LOGIN_SUCCESS_ONLY)
    public UnifiedLoginResponse handle(UnifiedLoginRequest request) {
        // 验证参数
        if (StringUtils.isEmpty(request.getPhone())) {
            throw new BusinessException(UsersErrorCode.PHONE_EMPTY.getCode(), UsersErrorCode.PHONE_EMPTY.getMsg());
        }
        if (StringUtils.isEmpty(request.getCode())) {
            throw new BusinessException(UsersErrorCode.VERIFICATION_CODE_EMPTY.getCode(), UsersErrorCode.VERIFICATION_CODE_EMPTY.getMsg());
        }

        // 验证手机号验证码
        boolean verified = verificationService.verifySmsCode(request.getPhone(), request.getCode(), request.getBusinessType());
        if (!verified) {
            throw new BusinessException(UsersErrorCode.SMS_CODE_CHECK_ERROR.getCode(), UsersErrorCode.SMS_CODE_CHECK_ERROR.getMsg());
        }

        // 使用 AuthenticationService 验证临时令牌
        PhoneBindingRequest bindingRequest = new PhoneBindingRequest();
        bindingRequest.setPhone(request.getPhone());
        bindingRequest.setEmail(request.getEmail());
        bindingRequest.setCode(request.getCode());
        bindingRequest.setBindingPhoneVerifyStatus(request.getFlowStatus());
        boolean verifyPhoneFlag = authenticationService.verifyPhoneBinding(bindingRequest);
        if (!verifyPhoneFlag) {
            throw new BusinessException(UsersErrorCode.TEMPORARY_TOKEN_INVALID.getCode(), UsersErrorCode.TEMPORARY_TOKEN_INVALID.getMsg());
        }

        // 检查手机号是否已存在账号
        boolean phoneExists = userPersonDomainService.checkPhoneExists(request.getPhone(), request.getPlatform().getCode());

        //UserPerson userPerson = null;
        if (phoneExists) {
            //userPerson = userPersonDomainService.findByPhoneAndSource(request.getPhone(), request.getPlatform().getCode());
            // 手机号已存在账号，需要用户决策是否沿用
            // 使用 AuthenticationService 更新临时令牌状态

            bindingRequest.setBindingPhoneVerifyStatus(BindingPhoneVerifyStatus.BINDING_PHONE_VERIFIED);
            authenticationService.phoneBindingStatusFlow(bindingRequest);

            UserPerson userPerson = userPersonDomainService.findByPhoneAndSource(request.getPhone(), request.getPlatform().getCode());

            UserInfoDTO userInfoDTO = userPersonDTOAssembler.toUserInfoDTO(userPerson);
            // 对敏感信息进行脱敏处理
            if (userInfoDTO != null) {
                userInfoDTO.setPhone(DataMaskingUtil.maskPhone(userInfoDTO.getPhone()));
                userInfoDTO.setEmail(DataMaskingUtil.maskEmail(userInfoDTO.getEmail()));
            }
            // 获取已存在账号的信息
            PhoneExistsResponse phoneExistsResponse = new PhoneExistsResponse();
            phoneExistsResponse.setExists(true);

            phoneExistsResponse.setUserInfo(userInfoDTO);
            return UnifiedLoginResponse.builder()
                    .flowStatus(BindingPhoneVerifyStatus.PHONE_EXISTS)
                    .needContinue(true)
                    .nextStepHint("该手机号已存在账号，是否沿用?")
                    .phoneExistsResponse(phoneExistsResponse)
                    .build();
        } else {
            // 手机号未注册，直接创建新账号并登录
            // 创建新账号请求
            CreateNewAccountWithEmailRequest createRequest = new CreateNewAccountWithEmailRequest();
            createRequest.setEmail(request.getEmail());
            createRequest.setPhone(request.getPhone());
            createRequest.setPlatform(request.getPlatform());
            createRequest.setAccountUseFlag(false); // 创建新账号
            createRequest.setFlowStatus(request.getFlowStatus());
            //直接从临时令牌中获取DeviceInfo
            UserLoginEmailBindingVerifyResponse tokenVerifyInfo = authenticationService.getTokenVerifyInfo(request.getEmail());
            createRequest.setDeviceInfo(AccountDeviceDTOAssembler.authDeviceConvertToDTO(tokenVerifyInfo.getAuthAccountDeviceDTO()));

            AuthAccountDeviceApiDTO authAccountDeviceApiDTO = tokenVerifyInfo.getAuthAccountDeviceDTO();
            if(authAccountDeviceApiDTO != null) {
                AuthUserResponse authUserResponse =  new AuthUserResponse();
                BeanUtils.copyProperties(authAccountDeviceApiDTO, authUserResponse);
                createRequest.setAuthUserResponse(authUserResponse);
            }

            // 调用创建新账号服务
            LoginResponse loginResponse = userLoginService.createNewAccountWithEmail(createRequest, request.getClientIp());

            // 使用 AuthenticationService 删除临时令牌
            authenticationService.verifyDeletePhoneBinding(bindingRequest);

            return UnifiedLoginResponse.builder()
                    .loginResponse(loginResponse)
                    .flowStatus(BindingPhoneVerifyStatus.R_COMPLETED)
                    .needContinue(false)
                    .build();
        }


    }

    @Override
    public boolean supports(LoginType loginType, BindingPhoneVerifyStatus status) {
        return loginType == LoginType.UNIFIED &&
                (status == BindingPhoneVerifyStatus.R_EMAIL_VERIFIED ||
                        status == BindingPhoneVerifyStatus.R_THIRD_PARTY_AUTHORIZED ||
                        status == BindingPhoneVerifyStatus.R_COMPLETED);
    }
}
