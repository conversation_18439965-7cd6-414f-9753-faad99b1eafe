package com.yuanchuan.user.application.annotation;

import com.yuanchuan.common.enums.users.login.LoginLogCondition;
import com.yuanchuan.common.enums.users.login.LoginType;
import com.yuanchuan.common.enums.users.login.PlatformType;

import java.lang.annotation.*;

/**
 * 登录日志记录注解
 * 用于标记需要记录登录日志的方法
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface LoginLog {

    /**
     * 登录类型
     * 如果不指定，将从方法参数中自动推断
     */
    LoginType loginType() default LoginType.PASSWORD;

    /**
     * 平台类型
     * 如果不指定，将从方法参数中自动推断
     */
    PlatformType platform() default PlatformType.CUSTOMER;

    /**
     * 是否自动推断登录类型和平台类型
     * 默认为true，会从方法参数中自动推断
     */
    boolean autoInfer() default true;

    /**
     * 描述信息
     */
    String description() default "";

    /**
     * 是否记录成功日志
     */
    boolean recordSuccess() default true;

    /**
     * 是否记录失败日志
     */
    boolean recordFailure() default true;

    /**
     * 登录日志记录条件
     */
    LoginLogCondition condition() default LoginLogCondition.REAL_LOGIN_SUCCESS_ONLY;
}
