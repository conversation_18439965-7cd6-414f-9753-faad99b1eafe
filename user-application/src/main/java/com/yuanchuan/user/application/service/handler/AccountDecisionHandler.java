package com.yuanchuan.user.application.service.handler;

import com.yuanchuan.authentication.api.dto.AuthThirdUserApiDTO;
import com.yuanchuan.authentication.api.request.PhoneBindingRequest;
import com.yuanchuan.authentication.api.response.UserLoginEmailBindingVerifyResponse;
import com.yuanchuan.authentication.api.service.AuthenticationService;
import com.yuanchuan.authentication.context.enums.BindingPhoneVerifyStatus;
import com.yuanchuan.common.enums.users.UsersErrorCode;
import com.yuanchuan.common.enums.users.login.LoginLogCondition;
import com.yuanchuan.common.enums.users.login.LoginType;
import com.yuanchuan.common.exception.BusinessException;
import com.yuanchuan.user.api.request.UnifiedLoginRequest;
import com.yuanchuan.user.api.request.UnifiedLoginResponse;
import com.yuanchuan.user.api.response.LoginResponse;
import com.yuanchuan.user.application.annotation.LoginLog;
import com.yuanchuan.user.application.service.assembler.AccountDeviceDTOAssembler;
import com.yuanchuan.user.application.service.assembler.UserPersonDTOAssembler;
import com.yuanchuan.user.application.service.impl.UserLoginServiceImpl;
import com.yuanchuan.user.context.enums.BindingStatusEnum;
import com.yuanchuan.user.context.enums.RegistrationTypeEnum;
import com.yuanchuan.user.domain.model.UserPerson;
import com.yuanchuan.user.domain.model.UserRegisterDomain;
import com.yuanchuan.user.domain.model.UserThirdPartyAuth;
import com.yuanchuan.user.domain.model.UserThirdPartyBinding;
import com.yuanchuan.user.domain.service.UserPersonDomainService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 账号决策处理器
 * 处理用户决策是否沿用已有账号
 */
@Slf4j
@Component
public class AccountDecisionHandler implements LoginHandler {

    @Autowired
    private UserLoginServiceImpl userLoginService;

    @Autowired
    private UserPersonDomainService userPersonDomainService;

    @Autowired
    private UserPersonDTOAssembler userPersonDTOAssembler;

    @DubboReference(version = "1.0", group = "${dubbo.consumer.group.authentication}")
    private AuthenticationService authenticationService;

    @Override
    @LoginLog(loginType = LoginType.UNIFIED, autoInfer = true,
            description = "账户决策流程",
            condition = LoginLogCondition.REAL_LOGIN_SUCCESS_ONLY)
    public UnifiedLoginResponse handle(UnifiedLoginRequest request) {
        // 验证参数
        if (request.getAccountUseFlag() == null) {
            throw new BusinessException(UsersErrorCode.ACCOUNT_USE_FLAG_EMPTY.getCode(), UsersErrorCode.ACCOUNT_USE_FLAG_EMPTY.getMsg());
        }

        // 使用 AuthenticationService 获取临时令牌中的信息
        PhoneBindingRequest bindingRequest = new PhoneBindingRequest();
        //bindingRequest.setDeviceId(request.getDeviceInfo() != null ? request.getDeviceInfo().getDeviceId() : "");
        bindingRequest.setBindingPhoneVerifyStatus(request.getFlowStatus());
        bindingRequest.setEmail(request.getEmail());
        boolean verifyPhoneFlag = authenticationService.verifyPhoneBinding(bindingRequest);
        if (!verifyPhoneFlag) {
            throw new BusinessException(UsersErrorCode.TEMPORARY_TOKEN_INVALID.getCode(), UsersErrorCode.TEMPORARY_TOKEN_INVALID.getMsg());
        }

        // 创建新账号请求
        UserRegisterDomain userRegisterDomain = new UserRegisterDomain();
        userRegisterDomain.setPhone(request.getPhone());
        userRegisterDomain.setEmail(request.getEmail());
        userRegisterDomain.setSource(request.getPlatform());
        userRegisterDomain.setRegistrationType(RegistrationTypeEnum.EMAIL_BINDING);

        //直接从临时令牌中获取DeviceInfo
        UserLoginEmailBindingVerifyResponse tokenVerifyInfo = authenticationService.getTokenVerifyInfo(request.getEmail());
        userRegisterDomain.setAccountDevice(AccountDeviceDTOAssembler.authDeviceConvertToDomain(tokenVerifyInfo.getAuthAccountDeviceDTO()));

        AuthThirdUserApiDTO authThirdUserApiDTO = tokenVerifyInfo.getAuthThirdUserDTO();
        if (authThirdUserApiDTO != null) {
            UserThirdPartyBinding userThirdPartyBinding = new UserThirdPartyBinding();
            userThirdPartyBinding.setPlatform(authThirdUserApiDTO.getPlatform());
            userThirdPartyBinding.setExternalUserId(authThirdUserApiDTO.getSub());
            userThirdPartyBinding.setNickName(authThirdUserApiDTO.getNickname());
            userThirdPartyBinding.setAvatar(authThirdUserApiDTO.getAvatar());
            userThirdPartyBinding.setGender(authThirdUserApiDTO.getGender());
            userThirdPartyBinding.setBindingStatus(BindingStatusEnum.BINDING.getCode());
            userThirdPartyBinding.setBindTime(new Date());
            userThirdPartyBinding.setCreatedBy("system");
            userThirdPartyBinding.setCreatedAt(new Date());
            userThirdPartyBinding.setUpdatedAt(new Date());
            userThirdPartyBinding.setUpdatedBy("system");

            UserThirdPartyAuth userThirdPartyAuth = new UserThirdPartyAuth();
            userThirdPartyAuth.setPlatform(authThirdUserApiDTO.getPlatform());
            userThirdPartyAuth.setAccessToken(authThirdUserApiDTO.getAccessToken());
            userThirdPartyAuth.setRefreshToken(authThirdUserApiDTO.getRefreshToken());
            userThirdPartyAuth.setTokenType(authThirdUserApiDTO.getTokenType());
            userThirdPartyAuth.setScope(authThirdUserApiDTO.getScope());
            userThirdPartyAuth.setExpiresAt(authThirdUserApiDTO.getExpiresAt());
            userThirdPartyAuth.setRefreshExpiresAt(authThirdUserApiDTO.getRefreshExpiresAt());
            //userThirdPartyAuth.setStatus(AccountStatus.ACTIVE.getDescription());
            userThirdPartyAuth.setLastRefreshTime(new Date());
            userThirdPartyAuth.setRefreshCount(1);
            userThirdPartyAuth.setCreatedBy("system");
            userThirdPartyAuth.setCreatedAt(new Date());
            userThirdPartyAuth.setUpdatedAt(new Date());
            userThirdPartyAuth.setUpdatedBy("system");

            userRegisterDomain.setUserThirdPartyAuth(userThirdPartyAuth);
            userRegisterDomain.setUserThirdPartyBinding(userThirdPartyBinding);
            userRegisterDomain.setRegistrationType(RegistrationTypeEnum.THIRD_BINDING);
        }

        UserPerson userPerson;
        if (request.getAccountUseFlag()) {
            userPerson = userPersonDomainService.confirmUseExistingAccount(userRegisterDomain);
        } else {
            userPerson = userPersonDomainService.createNewAccountWithEmail(userRegisterDomain);
        }
        // 构建包含token的登录响应
        LoginResponse loginResponse = userPersonDTOAssembler.buildLoginResponse(userPerson, request.getPlatform(), request.getDeviceInfo());

        // 使用 AuthenticationService 删除临时令牌
        authenticationService.verifyDeletePhoneBinding(bindingRequest);

        return UnifiedLoginResponse.builder()
                .loginResponse(loginResponse)
                .needContinue(false)
                .build();
    }

    @Override
    public boolean supports(LoginType loginType, BindingPhoneVerifyStatus status) {
        return loginType == LoginType.UNIFIED && status == BindingPhoneVerifyStatus.PHONE_EXISTS;
    }
}
