package com.yuanchuan.user.application.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.yuanchuan.authentication.api.dto.AuthAccountDeviceApiDTO;
import com.yuanchuan.authentication.api.request.PhoneBindingRequest;
import com.yuanchuan.authentication.api.service.AuthenticationService;
import com.yuanchuan.authentication.context.enums.BindingPhoneVerifyStatus;
import com.yuanchuan.common.constant.user.UserLoginRedisKeys;
import com.yuanchuan.common.enums.users.UsersErrorCode;
import com.yuanchuan.common.enums.users.login.LoginType;
import com.yuanchuan.common.enums.users.login.PlatformType;
import com.yuanchuan.common.exception.BusinessException;
import com.yuanchuan.common.utils.*;
import com.yuanchuan.user.api.dto.AccountDeviceDTO;
import com.yuanchuan.user.api.dto.AuthUserResponse;
import com.yuanchuan.user.api.dto.NewDeviceVerificationState;
import com.yuanchuan.user.api.request.*;
import com.yuanchuan.user.api.response.LoginResponse;
import com.yuanchuan.user.api.response.PasswordLoginResponse;
import com.yuanchuan.user.api.response.VerifySmsCodeAndLoginResponse;
import com.yuanchuan.user.api.service.VerificationService;
import com.yuanchuan.user.application.service.SecondaryVerificationApplicationService;
import com.yuanchuan.user.application.service.assembler.AccountDeviceDTOAssembler;
import com.yuanchuan.user.application.service.assembler.UserPersonDTOAssembler;
import com.yuanchuan.user.context.enums.BindingStatusEnum;
import com.yuanchuan.user.context.enums.RegisterSourceEnum;
import com.yuanchuan.user.context.enums.RegistrationTypeEnum;
import com.yuanchuan.user.context.enums.SmsAndEmailBusinessType;
import com.yuanchuan.user.domain.model.*;
import com.yuanchuan.user.domain.repository.AccountDeviceRepository;
import com.yuanchuan.user.domain.service.UserPersonDomainService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 用户登录服务实现类
 * 负责处理各种登录方式的具体实现
 */
@Slf4j
@Service
public class UserLoginServiceImpl {

    @Autowired
    private VerificationService verificationService;

    @Autowired
    private UserPersonDomainService userPersonDomainService;

    @DubboReference(version = "1.0", group = "${dubbo.consumer.group.authentication}")
    private AuthenticationService authenticationService;

    @Autowired
    private UserPersonDTOAssembler userPersonDTOAssembler;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private AccountDeviceRepository accountDeviceRepository;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private SecondaryVerificationApplicationService secondaryVerificationApplicationService;


    /**
     * 绑定用户设备（带异步重试机制）
     * 如果设备ID发生变化，则新增设备
     *
     * @param userPerson 用户基本信息
     * @param deviceInfo 设备信息
     */
    private void bindUserDevice(UserPerson userPerson, AccountDeviceDTO deviceInfo) {
        if (userPerson == null || deviceInfo == null || deviceInfo.getDeviceId() == null) {
            // 如果用户信息或设备信息为空，则不进行设备绑定
            return;
        }

        try {
            // 将DTO转换为领域对象
            AccountDevice accountDevice = AccountDeviceDTOAssembler.convertToDomain(deviceInfo);
            accountDevice.setAccountId(userPerson.getCustomerAccount().getId());
            accountDevice.setSource(userPerson.getSource());
            // 调用领域服务绑定设备
            userPersonDomainService.bindDevice(accountDevice);

            // 绑定成功后缓存设备状态
            cacheDeviceLogin(userPerson.getCustomerAccount().getId(), deviceInfo.getDeviceId());

            log.info("用户设备绑定成功，用户ID: {}, 设备ID: {}", userPerson.getId(), deviceInfo.getDeviceId());
        } catch (Exception e) {
            // 设备绑定失败不应影响登录流程，启动异步重试
            log.error("用户设备绑定失败，将异步重试，用户ID: {}, 设备ID: {}, 错误信息: {}",
                    userPerson.getId(), deviceInfo.getDeviceId(), e.getMessage(), e);

            // 异步重试设备绑定
            asyncRetryDeviceBinding(userPerson, deviceInfo);

            // 临时缓存设备状态，避免下次立即触发二次验证
            cacheDeviceLoginTemporary(userPerson.getCustomerAccount().getId(), deviceInfo.getDeviceId());
        }
    }

    /**
     * 异步重试设备绑定
     *
     * @param userPerson 用户信息
     * @param deviceInfo 设备信息
     */
    @Async
    public void asyncRetryDeviceBinding(UserPerson userPerson, AccountDeviceDTO deviceInfo) {
        final int maxRetries = 3;
        final long[] retryDelays = {5000, 15000, 60000}; // 5秒、1分、5分钟

        for (int attempt = 0; attempt < maxRetries; attempt++) {
            try {
                // 等待一段时间后重试
                if (attempt > 0) {
                    Thread.sleep(retryDelays[attempt - 1]);
                }

                log.info("开始第{}次重试设备绑定，用户ID: {}, 设备ID: {}",
                        attempt + 1, userPerson.getId(), deviceInfo.getDeviceId());

                // 重新尝试绑定设备
                AccountDevice accountDevice = AccountDeviceDTOAssembler.convertToDomain(deviceInfo);
                accountDevice.setAccountId(userPerson.getCustomerAccount().getId());
                accountDevice.setSource(userPerson.getSource());
                userPersonDomainService.bindDevice(accountDevice);

                // 绑定成功，更新缓存
                cacheDeviceLogin(userPerson.getCustomerAccount().getId(), deviceInfo.getDeviceId());

                log.info("设备绑定重试成功，用户ID: {}, 设备ID: {}, 重试次数: {}",
                        userPerson.getId(), deviceInfo.getDeviceId(), attempt + 1);
                return; // 成功后退出

            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("设备绑定重试被中断，用户ID: {}, 设备ID: {}",
                        userPerson.getId(), deviceInfo.getDeviceId());
                return;
            } catch (Exception e) {
                log.error("第{}次设备绑定重试失败，用户ID: {}, 设备ID: {}, 错误: {}",
                        attempt + 1, userPerson.getId(), deviceInfo.getDeviceId(), e.getMessage());

                // 如果是最后一次重试，记录最终失败
                if (attempt == maxRetries - 1) {
                    log.error("设备绑定最终失败，已经重试{}次，用户ID: {}, 设备ID: {}",
                            maxRetries, userPerson.getId(), deviceInfo.getDeviceId(), e);
                }
            }
        }
    }

    /**
     * 临时缓存设备状态（较短过期时间）
     *
     * @param accountId 账户ID
     * @param deviceId  设备ID
     */
    private void cacheDeviceLoginTemporary(Long accountId, String deviceId) {
        try {
            String cacheKey = UserLoginRedisKeys.getDeviceLoginCacheKey(accountId, deviceId);
            // 设置较短的过期时间，比如1小时
            redisUtil.set(cacheKey, "1", 1, TimeUnit.HOURS);
            log.debug("临时缓存设备状态，账户ID: {}, 设备ID: {}", accountId, deviceId);
        } catch (Exception e) {
            log.error("临时缓存设备状态失败，账户ID: {}, 设备ID: {}", accountId, deviceId, e);
        }
    }

    /**
     * 短信验证码登录
     *
     * @param request  登录请求
     * @param clientIp 客户端IP
     * @return 登录响应
     */
    @Transactional(rollbackFor = Exception.class)
    public VerifySmsCodeAndLoginResponse verifySmsCodeAndLogin(VerifySmsCodeAndBindPhoneRequest request, String clientIp) {
        try {
            log.info("开始处理短信验证码登录: phone={}, businessType={}",
                    request.getPhone(), request.getBusinessType());

            // 1. 准备设备信息
            AccountDeviceDTO deviceInfo = prepareDeviceInfo(request.getDeviceInfo(), clientIp);

            // 2. 统一处理二次验证场景
            if (request.getBusinessType() == SmsAndEmailBusinessType.SECONDARY_PHONE_VERIFICATION) {
                Object secondaryResponse = handleUniversalSecondaryVerification(request, deviceInfo, clientIp, LoginType.SMS_CODE);
                if (secondaryResponse != null) {
                    return (VerifySmsCodeAndLoginResponse) secondaryResponse;
                }
            }

            // 3. 验证短信验证码
            validateSmsCode(request.getPhone(), request.getCode(), request.getBusinessType());

            // 4. 处理登录或注册
            return processSmsLoginOrRegister(request, deviceInfo, clientIp);

        } catch (Exception e) {
            handleLoginException(e, "SMS", "verifySmsCodeAndLogin", request.getPhone(), request.getCode());
            return null; // 不会执行到这里
        }
    }

    /**
     * 邮箱验证码登录
     *
     * @param request  登录请求
     * @param clientIp 客户端IP
     * @return 登录响应
     */
    @Transactional(rollbackFor = Exception.class)
    public UnifiedLoginResponse verifyEmailCodeAndLogin(VerifyEmailCodeAndLoginRequest request, String clientIp) {
        try {
            log.info("开始处理邮箱验证码登录: email={}, businessType={}",
                    request.getEmail(), request.getBusinessType());

            // 1. 准备设备信息
            AccountDeviceDTO deviceInfo = prepareEmailDeviceInfo(request.getDeviceInfo(), clientIp);

            // 2. 统一处理二次验证场景
            if (request.getBusinessType() == SmsAndEmailBusinessType.SECONDARY_EMAIL_VERIFICATION) {
                Object secondaryResponse = handleUniversalSecondaryVerification(request, deviceInfo, clientIp, LoginType.EMAIL_CODE);
                if (secondaryResponse != null) {
                    return (UnifiedLoginResponse) secondaryResponse;
                }
            }


            // 3. 验证邮箱格式和验证码
            validateEmailFormatAndCode(request.getEmail(), request.getCode(), request.getBusinessType());

            // 4. 处理登录或注册
            return processEmailLoginOrRegister(request, deviceInfo, clientIp);

        } catch (Exception e) {
            handleLoginException(e, "EMAIL", "verifyEmailCodeAndLogin", request.getEmail(), request.getCode());
            return null; // 不会执行到这里
        }
    }

    /**
     * 创建新账号（邮箱注册）
     *
     * @param request  创建账号请求
     * @param clientIp 客户端IP
     * @return 登录响应
     */
    @Transactional(rollbackFor = Exception.class)
    public LoginResponse createNewAccountWithEmail(CreateNewAccountWithEmailRequest request, String clientIp) {
        try {
            log.info("创建新账号请求: {}", JSONObject.toJSONString(request));

            // 处理设备信息
            AccountDeviceDTO deviceInfo = request.getDeviceInfo();
            if (deviceInfo == null) {
                deviceInfo = new AccountDeviceDTO();
            }
            deviceInfo.setIpAddress(clientIp);
            deviceInfo.setLoginTime(LocalDateTime.now());

            // 创建用户
            UserRegisterDomain userRegisterDomain = new UserRegisterDomain();
            userRegisterDomain.setEmail(request.getEmail());
            userRegisterDomain.setPhone(request.getPhone());
            userRegisterDomain.setSource(request.getPlatform());
            userRegisterDomain.setAccountDevice(AccountDeviceDTOAssembler.convertToDomain(deviceInfo));

            if (request.getFlowStatus() == null) {
                userRegisterDomain.setRegistrationType(RegistrationTypeEnum.EMAIL_BINDING);
            }
            // 注册用户
            if (request.getFlowStatus() != null && request.getFlowStatus().getCode() == BindingPhoneVerifyStatus.R_EMAIL_VERIFIED.getCode()) {
                userRegisterDomain.setRegistrationType(RegistrationTypeEnum.EMAIL_BINDING);
            }
            if (request.getFlowStatus() != null && request.getFlowStatus().getCode() == BindingPhoneVerifyStatus.R_THIRD_PARTY_AUTHORIZED.getCode()) {
                userRegisterDomain.setRegistrationType(RegistrationTypeEnum.THIRD_BINDING);
            }


            // 三方
            AuthUserResponse authUserResponse = request.getAuthUserResponse();
            if (authUserResponse != null) {
                UserThirdPartyBinding userThirdPartyBinding = new UserThirdPartyBinding();
                userThirdPartyBinding.setPlatform(authUserResponse.getPlatform());
                userThirdPartyBinding.setExternalUserId(authUserResponse.getSub());
                userThirdPartyBinding.setNickName(authUserResponse.getNickname());
                userThirdPartyBinding.setAvatar(authUserResponse.getAvatar());
                userThirdPartyBinding.setGender(authUserResponse.getGender());
                userThirdPartyBinding.setBindingStatus(BindingStatusEnum.BINDING.getCode());
                userThirdPartyBinding.setBindTime(new Date());
                userThirdPartyBinding.setCreatedBy("system");
                userThirdPartyBinding.setCreatedAt(new Date());
                userThirdPartyBinding.setUpdatedAt(new Date());
                userThirdPartyBinding.setUpdatedBy("system");

                UserThirdPartyAuth userThirdPartyAuth = new UserThirdPartyAuth();
                userThirdPartyAuth.setPlatform(authUserResponse.getPlatform());
                userThirdPartyAuth.setAccessToken(authUserResponse.getAccessToken());
                userThirdPartyAuth.setRefreshToken(authUserResponse.getRefreshToken());
                userThirdPartyAuth.setTokenType(authUserResponse.getTokenType());
                userThirdPartyAuth.setScope(authUserResponse.getScope());
                userThirdPartyAuth.setExpiresAt(authUserResponse.getExpiresAt());
                userThirdPartyAuth.setRefreshExpiresAt(authUserResponse.getRefreshExpiresAt());
                //userThirdPartyAuth.setStatus(AccountStatus.ACTIVE.getDescription());
                userThirdPartyAuth.setLastRefreshTime(new Date());
                userThirdPartyAuth.setRefreshCount(1);
                userThirdPartyAuth.setCreatedBy("system");
                userThirdPartyAuth.setCreatedAt(new Date());
                userThirdPartyAuth.setUpdatedAt(new Date());
                userThirdPartyAuth.setUpdatedBy("system");

                userRegisterDomain.setUserThirdPartyAuth(userThirdPartyAuth);
                userRegisterDomain.setUserThirdPartyBinding(userThirdPartyBinding);
            }

            UserPerson userPerson = userPersonDomainService.register(userRegisterDomain);

            // 构建登录响应
            return userPersonDTOAssembler.buildLoginResponse(userPerson, request.getPlatform(), deviceInfo);
        } catch (BusinessException e) {
            log.error("创建新账号失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("创建新账号失败", e);
            throw new BusinessException(UsersErrorCode.ACCOUNT_CREATE_FAILED.getCode(), UsersErrorCode.ACCOUNT_CREATE_FAILED.getMsg());
        }
    }

    /**
     * 账号密码登录
     *
     * @param request  登录请求
     * @param clientIp 客户端IP
     * @return 密码登录响应（包含二次验证信息）
     */
    @Transactional(rollbackFor = Exception.class)
    public PasswordLoginResponse passwordLogin(PasswordLoginRequest request, String clientIp) {
        try {
            log.info("账号密码登录请求: {}", JSONObject.toJSONString(request));

            // 处理设备信息
            AccountDeviceDTO deviceInfo = request.getDeviceInfo();
            if (deviceInfo == null) {
                deviceInfo = new AccountDeviceDTO();
            }
            deviceInfo.setIpAddress(clientIp);
            deviceInfo.setLoginTime(LocalDateTime.now());

            // 创建领域对象
            UserRegisterDomain userRegisterDomain = new UserRegisterDomain();
            userRegisterDomain.setSource(request.getPlatform());
            userRegisterDomain.setAccountDevice(AccountDeviceDTOAssembler.convertToDomain(deviceInfo));
            userRegisterDomain.setPassWord(request.getPassword());

            // 判断账号类型（手机号或邮箱）
            String account = request.getAccount();
            // 台湾手机号格式
            if (account.matches("^09\\d{8}$")) {
                userRegisterDomain.setPhone(account);
                // 邮箱格式（使用更严格的验证）
            } else if (EmailValidationUtil.isValidEmail(account)) {
                userRegisterDomain.setEmail(account);
            } else if (request.getPlatform() != null && request.getPlatform() == PlatformType.ADMIN) {
                userRegisterDomain.setAccountName(account);
            } else {
                // 如果是邮箱格式错误，返回具体的邮箱错误信息
                if (account.contains("@")) {
                    throw new BusinessException(UsersErrorCode.EMAIL_FORMAT_INVALID.getCode(), UsersErrorCode.EMAIL_FORMAT_INVALID.getMsg());
                } else {
                    throw new BusinessException(UsersErrorCode.ACCOUNT_FORMAT_ERROR.getCode(), UsersErrorCode.ACCOUNT_FORMAT_ERROR.getMsg());
                }
            }

            // 调用领域服务验证密码
            UserPerson userPerson = userPersonDomainService.verifyPassword(userRegisterDomain);
            if (Objects.isNull(userPerson)) {
                throw new BusinessException(UsersErrorCode.USER_INFO_EXCEPTION.getCode(), UsersErrorCode.USER_INFO_EXCEPTION.getMsg());
            }

            CustomerAccount customerAccount = null;
            if (request.getPlatform() == PlatformType.ADMIN) {
                BusinessAccount businessAccount = userPerson.getBusinessAccount();
                if (Objects.isNull(businessAccount)) {
                    throw new BusinessException(UsersErrorCode.USER_ACCOUNT_NOT_EXIST.getCode(), UsersErrorCode.USER_ACCOUNT_NOT_EXIST.getMsg());
                }
            }else{
                customerAccount = userPerson.getCustomerAccount();
                if (Objects.isNull(customerAccount)) {
                    throw new BusinessException(UsersErrorCode.USER_ACCOUNT_NOT_EXIST.getCode(), UsersErrorCode.USER_ACCOUNT_NOT_EXIST.getMsg());
                }
            }



            // 检查是否有新设备验证状态（可能是从其他登录方式过来的二次验证）
            NewDeviceVerificationState newDeviceState = getNewDeviceState(userPerson.getId(), deviceInfo.getDeviceId());
            if (newDeviceState != null && !newDeviceState.getOriginalLoginMethod().equals(LoginType.PASSWORD.getName())) {
                // 这是二次验证，绑定新设备并清除状态
                bindUserDevice(userPerson, deviceInfo);
                // 使用分布式锁保护状态清除
                //processSecondaryVerificationWithLock(userPerson.getId(), deviceInfo.getDeviceId(), () -> {
                //    return null;
                //});
                clearNewDeviceState(userPerson.getId(), deviceInfo.getDeviceId());

                log.info("密码二次验证成功，绑定新设备: userId={}, deviceId={}", userPerson.getId(), deviceInfo.getDeviceId());

                // 构建正常登录响应
                LoginResponse loginResponse = userPersonDTOAssembler.buildLoginResponse(userPerson, request.getPlatform(), deviceInfo);
                PasswordLoginResponse response = new PasswordLoginResponse();
                response.setLoginResponse(loginResponse);
                response.setNeedSecondaryVerification(false);
                return response;
            } else {
                // 只有C端（CUSTOMER）平台才需要二次验证，管理员平台直接登录
                if (request.getPlatform() == PlatformType.CUSTOMER) {
                    // 检测是否为新设备
                    boolean isNewDevice = checkIsNewDevice(customerAccount.getId(), deviceInfo.getDeviceId());
                    if (isNewDevice) {
                        // C端新设备登录，需要二次验证
                        return handlePasswordNewDeviceLogin(userPerson, request, deviceInfo, clientIp);
                    }
                }

                // 管理员平台或C端已知设备，正常登录
                //bindUserDevice(userPerson, deviceInfo);

                // 构建正常登录响应
                LoginResponse loginResponse = userPersonDTOAssembler.buildLoginResponse(userPerson, request.getPlatform(), deviceInfo);
                PasswordLoginResponse response = new PasswordLoginResponse();
                response.setLoginResponse(loginResponse);
                response.setNeedSecondaryVerification(false);
                return response;
            }
        } catch (Exception e) {
            handleLoginException(e, "PASSWORD", "passwordLogin", request.getAccount());
            return null; // 不会执行到这里
        }
    }

    // ==================== 二次验证相关方法 ====================

    /**
     * 获取新设备验证状态
     *
     * @param userId   用户ID
     * @param deviceId 设备ID
     * @return 新设备验证状态，不存在或已过期返回null
     */
    private NewDeviceVerificationState getNewDeviceState(Long userId, String deviceId) {
        String key = UserLoginRedisKeys.getNewDeviceVerificationKey(userId, deviceId);
        String stateJson = redisUtil.get(key);
        if (StringUtils.isNotBlank(stateJson)) {
            try {
                return JSON.parseObject(stateJson, NewDeviceVerificationState.class);
            } catch (Exception e) {
                log.error("解析新设备验证状态失败，用户ID: {}, 设备ID: {}", userId, deviceId, e);
                // 解析失败时清除损坏的数据
                redisUtil.delete(key);
            }
        }
        return null;
    }

    /**
     * 保存新设备验证状态
     *
     * @param state 新设备验证状态
     */
    private void saveNewDeviceState(NewDeviceVerificationState state) {
        String key = UserLoginRedisKeys.getNewDeviceVerificationKey(state.getUserId(), state.getDeviceId());
        String stateJson = JSON.toJSONString(state);
        // 使用统一的过期时间常量
        redisUtil.set(key, stateJson, UserLoginRedisKeys.NEW_DEVICE_VERIFICATION_EXPIRE_MINUTES, TimeUnit.MINUTES);

        log.info("保存新设备验证状态，用户ID: {}, 设备ID: {}, 原始登录方式: {}",
                state.getUserId(), state.getDeviceId(), state.getOriginalLoginMethod());
    }

    /**
     * 清除新设备验证状态
     *
     * @param userId   用户ID
     * @param deviceId 设备ID
     */
    private void clearNewDeviceState(Long userId, String deviceId) {
        String key = UserLoginRedisKeys.getNewDeviceVerificationKey(userId, deviceId);
        redisUtil.delete(key);

        log.info("清除新设备验证状态，用户ID: {}, 设备ID: {}", userId, deviceId);
    }

    /**
     * 检查是否为新设备（优化版：使用Redis缓存检查）
     */
    private boolean checkIsNewDevice(Long accountId, String deviceId) {
        if (StringUtils.isBlank(deviceId)) {
            throw new BusinessException(UsersErrorCode.DEVICE_ID_REQUIRED.getCode(), UsersErrorCode.DEVICE_ID_REQUIRED.getMsg());
        }


        try {
            // 先检查Redis缓存
            String cacheKey = UserLoginRedisKeys.getDeviceLoginCacheKey(accountId, deviceId);
            if (redisUtil.hasKey(cacheKey)) {
                log.debug("从缓存中找到设备记录，账户ID: {}, 设备ID: {}", accountId, deviceId);
                return false; // 缓存中存在，不是新设备
            }

            // 缓存中不存在，查询数据库
            Optional<AccountDevice> existingDevice = accountDeviceRepository.findByAccountIdAndDeviceId(accountId, deviceId);
            boolean isNewDevice = !existingDevice.isPresent();

            if (!isNewDevice) {
                // 如果数据库中存在，更新缓存
                cacheDeviceLogin(accountId, deviceId);
                log.debug("从数据库中找到设备记录并缓存，账户ID: {}, 设备ID: {}", accountId, deviceId);
            }

            return isNewDevice;
        } catch (Exception e) {
            log.error("检查设备失败，用户accountId: {}, 设备ID: {}", accountId, deviceId, e);
            return true; // 发生异常时，保守地认为是新设备
        }

        //try {
        //    // 使用USER_TOKEN_KEY_PREFIX检查设备是否已登录（有Token即为已知设备）
        //    String tokenKey = UserLoginRedisKeys.USER_TOKEN_KEY_PREFIX + userId + ":" + deviceId;
        //    boolean hasToken = redisUtil.hasKey(tokenKey);
        //
        //    // 如果有Token，说明设备已登录过，不是新设备
        //    return !hasToken;
        //} catch (Exception e) {
        //    log.error("检查设备Token失败，用户ID: {}, 设备ID: {}", userId, deviceId, e);
        //    return true; // 发生异帰时，保守地认为是新设备
        //}
    }

    /**
     * 缓存设备登录状态
     *
     * @param accountId 账户ID
     * @param deviceId  设备ID
     */
    private void cacheDeviceLogin(Long accountId, String deviceId) {
        if (StringUtils.isBlank(deviceId)) {
            return;
        }

        try {
            String cacheKey = UserLoginRedisKeys.getDeviceLoginCacheKey(accountId, deviceId);
            // 存储一个简单的标记值
            redisUtil.set(cacheKey, "1", UserLoginRedisKeys.DEVICE_LOGIN_CACHE_EXPIRE_DAYS, TimeUnit.DAYS);
            log.debug("缓存设备登录状态，账户ID: {}, 设备ID: {}", accountId, deviceId);
        } catch (Exception e) {
            log.error("缓存设备登录状态失败，账户ID: {}, 设备ID: {}", accountId, deviceId, e);
        }
    }

    /**
     * 检查是否已发送安全通知
     *
     * @param userId   用户ID
     * @param deviceId 设备ID
     * @return true-已发送，false-未发送
     */
    private boolean hasSecurityNotificationSent(Long userId, String deviceId) {
        if (StringUtils.isBlank(deviceId)) {
            return false;
        }

        try {
            String notificationKey = UserLoginRedisKeys.getSecurityNotificationKey(userId, deviceId);
            return redisUtil.hasKey(notificationKey);
        } catch (Exception e) {
            log.error("检查安全通知状态失败，用户ID: {}, 设备ID: {}", userId, deviceId, e);
            return false;
        }
    }

    /**
     * 记录安全通知发送状态
     *
     * @param userId   用户ID
     * @param deviceId 设备ID
     */
    private void markSecurityNotificationSent(Long userId, String deviceId) {
        if (StringUtils.isBlank(deviceId)) {
            return;
        }

        try {
            String notificationKey = UserLoginRedisKeys.getSecurityNotificationKey(userId, deviceId);
            String timestamp = String.valueOf(System.currentTimeMillis());
            redisUtil.set(notificationKey, timestamp, UserLoginRedisKeys.SECURITY_NOTIFICATION_EXPIRE_HOURS, TimeUnit.HOURS);
            log.info("记录安全通知发送状态，用户ID: {}, 设备ID: {}", userId, deviceId);
        } catch (Exception e) {
            log.error("记录安全通知发送状态失败，用户ID: {}, 设备ID: {}", userId, deviceId, e);
        }
    }

    /**
     * 判断是否为仅手机号用户
     */
    private boolean isPhoneOnlyUser(UserPerson userPerson) {
        boolean hasEmail = StringUtils.isNotBlank(userPerson.getEmail()) &&
                !"".equals(userPerson.getEmail().trim());
        CustomerAccount customerAccount = userPerson.getCustomerAccount();
        boolean hasPassword = StringUtils.isNotBlank(customerAccount.getPassword()) &&
                !"".equals(customerAccount.getPassword().trim());

        return !hasEmail && !hasPassword;
    }

    /**
     * 处理新设备登录
     */
    private VerifySmsCodeAndLoginResponse handleNewDeviceLogin(UserPerson userPerson,
                                                               VerifySmsCodeAndBindPhoneRequest request,
                                                               AccountDeviceDTO deviceInfo,
                                                               String clientIp) {

        if (isPhoneOnlyUser(userPerson)) {
            // 仅手机号用户：直接登录 + 发安全通知
            bindUserDevice(userPerson, deviceInfo);

            // 发送安全通知短信
            //sendSecurityNotification(userPerson.getPhone(), deviceInfo, clientIp);

            // 构建完整的登录响应（显示安全提示）
            LoginResponse loginResponse = buildCompleteLoginResponse(userPerson, request, deviceInfo);

            VerifySmsCodeAndLoginResponse response = new VerifySmsCodeAndLoginResponse();
            response.setLoginResponse(loginResponse);
            return response;
        } else {
            // 非仅手机号用户：手机号登录 → 邮箱验证码二次验证
            return triggerEmailSecondaryVerification(userPerson, request, deviceInfo, clientIp);
        }
    }

    /**
     * 触发邮箱二次验证（手机号登录 → 邮箱验证码）
     */
    private VerifySmsCodeAndLoginResponse triggerEmailSecondaryVerification(UserPerson userPerson,
                                                                            VerifySmsCodeAndBindPhoneRequest request,
                                                                            AccountDeviceDTO deviceInfo, String clientIp) {
        // 存储新设备验证状态（前置确定验证目标）
        NewDeviceVerificationState state = new NewDeviceVerificationState();
        state.setUserId(userPerson.getId());
        state.setDeviceId(deviceInfo.getDeviceId());
        state.setOriginalLoginMethod(LoginType.SMS_CODE.getName());
        state.setTargetEmail(userPerson.getEmail()); // 前置确定邮箱地址
        state.setTargetPhone(userPerson.getPhone()); // 保存手机号作为备用
        state.setCreateTime(LocalDateTime.now());
        state.setExpireTime(LocalDateTime.now().plusMinutes(UserLoginRedisKeys.NEW_DEVICE_VERIFICATION_EXPIRE_MINUTES));

        //保存新设备验证状态
        saveNewDeviceState(state);

        // 发送邮箱验证码到前置确定的邮箱
        //verificationService.sendEmailCode(state.getTargetEmail(), SmsAndEmailBusinessType.SECONDARY_EMAIL_VERIFICATION);

        // 构建二次验证响应，使用加密token替代明文userId
        String secondaryVerificationToken = SecondaryVerificationTokenUtil.generateSecondaryVerificationToken(
                userPerson.getId(), deviceInfo.getDeviceId());

        LoginResponse loginResponse = new LoginResponse();
        loginResponse.setSecondaryVerificationToken(secondaryVerificationToken);

        // 需要二次验证令牌
        secondaryVerificationApplicationService.triggerPhoneSecondaryVerification(userPerson, request, deviceInfo, clientIp);

        // 确定可用的验证方式
        List<LoginResponse.TwoVerificationResponse> twoVerificationResponses = determineAvailableVerificationTypes(userPerson, LoginType.SMS_CODE);
        loginResponse.setTwoVerificationResponses(twoVerificationResponses);
        loginResponse.setIsCrossPlatformUser(false);

        VerifySmsCodeAndLoginResponse response = new VerifySmsCodeAndLoginResponse();
        response.setLoginResponse(loginResponse);

        return response;
    }


    /**
     * 触发手机号二次验证（邮箱登录 → 手机号验证码）
     */
    private LoginResponse triggerPhoneSecondaryVerification(UserPerson userPerson,
                                                            VerifyEmailCodeAndLoginRequest request,
                                                            AccountDeviceDTO deviceInfo, String clientIp) {
        // 存储新设备验证状态
        NewDeviceVerificationState state = new NewDeviceVerificationState();
        state.setUserId(userPerson.getId());
        state.setDeviceId(deviceInfo.getDeviceId());
        state.setOriginalLoginMethod(LoginType.EMAIL_CODE.getName());
        state.setTargetPhone(userPerson.getPhone()); // 前置确定手机号
        state.setTargetEmail(userPerson.getEmail()); // 保存邮箱作为备用
        state.setCreateTime(LocalDateTime.now());
        state.setExpireTime(LocalDateTime.now().plusMinutes(UserLoginRedisKeys.NEW_DEVICE_VERIFICATION_EXPIRE_MINUTES));

        saveNewDeviceState(state);

        // 发送手机验证码到前置确定的手机号
        //verificationService.sendSmsCode(state.getTargetPhone(), SmsAndEmailBusinessType.SECONDARY_PHONE_VERIFICATION);

        // 构建二次验证响应，使用加密token替代明文userId
        String secondaryVerificationToken = SecondaryVerificationTokenUtil.generateSecondaryVerificationToken(
                userPerson.getId(), deviceInfo.getDeviceId());

        secondaryVerificationApplicationService.triggerEmailSecondaryVerification(userPerson, request, deviceInfo, clientIp);

        LoginResponse loginResponse = new LoginResponse();
        loginResponse.setSecondaryVerificationToken(secondaryVerificationToken);
        // 确定可用的验证方式
        List<LoginResponse.TwoVerificationResponse> twoVerificationResponses = determineAvailableVerificationTypes(userPerson, LoginType.EMAIL_CODE);
        loginResponse.setTwoVerificationResponses(twoVerificationResponses);
        return loginResponse;
    }

    /**
     * 发送安全通知短信
     *
     * @param phone      手机号
     * @param deviceInfo 设备信息
     * @param clientIp   客户端IP
     */
    private void sendSecurityNotification(String phone, AccountDeviceDTO deviceInfo, String clientIp) {
        // 检查是否已发送过安全通知，避免重复发送
        // 注意：这里使用phone作为userId的替代，因为在这个上下文中我们只有phone
        // 实际上应该传入userId，这里为了保持接口不变，使用phone的hashCode作为代理
        Long pseudoUserId = (long) phone.hashCode();

        if (hasSecurityNotificationSent(pseudoUserId, deviceInfo.getDeviceId())) {
            log.debug("安全通知已发送，跳过重复发送，手机号: {}, 设备ID: {}", phone, deviceInfo.getDeviceId());
            return;
        }

        try {
            // TODO: 实现安全通知短信发送
            // 内容：检测到您的账号在「xx时，xx分」「xx市」「xx设备」登录，为确保您的账号安全，请到个人中心绑定邮箱或设置密码后重新登录验证

            // 这里可以调用短信服务发送安全通知
            // smsService.sendSecurityNotification(phone, deviceInfo, clientIp);

            log.info("发送安全通知短信到: {}, 设备: {}, IP: {}", phone, deviceInfo.getDeviceId(), clientIp);

            // 记录安全通知发送状态
            markSecurityNotificationSent(pseudoUserId, deviceInfo.getDeviceId());

        } catch (Exception e) {
            log.error("发送安全通知失败，手机号: {}, 设备ID: {}, IP: {}", phone, deviceInfo.getDeviceId(), clientIp, e);
        }
    }

    // ==================== 密码登录二次验证相关方法 ====================

    /**
     * 处理密码登录新设备二次验证
     *
     * @param userPerson 用户信息
     * @param request    密码登录请求
     * @param deviceInfo 设备信息
     * @param clientIp   客户端IP
     * @return 密码登录响应
     */
    private PasswordLoginResponse handlePasswordNewDeviceLogin(UserPerson userPerson,
                                                               PasswordLoginRequest request,
                                                               AccountDeviceDTO deviceInfo,
                                                               String clientIp) {

        // 密码登录的用户肯定有手机号，直接进行手机号二次验证
        return triggerPasswordSecondaryVerification(userPerson, request, deviceInfo, clientIp);
    }

    /**
     * 触发密码登录的二次验证（密码登录 → 手机号验证码）
     *
     * @param userPerson 用户信息
     * @param request    密码登录请求
     * @param deviceInfo 设备信息
     * @return 密码登录响应
     */
    private PasswordLoginResponse triggerPasswordSecondaryVerification(UserPerson userPerson,
                                                                       PasswordLoginRequest request,
                                                                       AccountDeviceDTO deviceInfo,
                                                                       String clientIp) {
        // 检查用户是否有手机号（理论上密码登录用户一定有手机号）
        if (StringUtils.isBlank(userPerson.getPhone())) {
            throw new BusinessException(UsersErrorCode.SECONDARY_VERIFICATION_PHONE_NOT_EXIST.getCode(), UsersErrorCode.SECONDARY_VERIFICATION_PHONE_NOT_EXIST.getMsg());
        }

        // 存储新设备验证状态
        NewDeviceVerificationState state = new NewDeviceVerificationState();
        state.setUserId(userPerson.getId());
        state.setDeviceId(deviceInfo.getDeviceId());
        state.setOriginalLoginMethod(LoginType.PASSWORD.getName());
        state.setTargetPhone(userPerson.getPhone()); // 密码登录用户肯定有手机号
        state.setTargetEmail(userPerson.getEmail()); // 保存邮箱（可能为null）
        state.setOriginalIpAddress(clientIp); // 记录原始IP地址
        state.setCreateTime(LocalDateTime.now());
        state.setExpireTime(LocalDateTime.now().plusMinutes(UserLoginRedisKeys.NEW_DEVICE_VERIFICATION_EXPIRE_MINUTES));

        saveNewDeviceState(state);

        // 发送手机验证码进行二次验证
        //verificationService.sendSmsCode(state.getTargetPhone(), SmsAndEmailBusinessType.SECONDARY_PHONE_VERIFICATION);


        // 需要二次验证令牌
        secondaryVerificationApplicationService.triggerPasswordSecondaryVerification(userPerson, request, deviceInfo, clientIp);

        // 构建二次验证响应，使用加密token替代明文userId
        String secondaryVerificationToken = SecondaryVerificationTokenUtil.generateSecondaryVerificationToken(
                userPerson.getId(), deviceInfo.getDeviceId());

        LoginResponse loginResponse = new LoginResponse();
        loginResponse.setSecondaryVerificationToken(secondaryVerificationToken);

        // 确定可用的验证方式
        List<LoginResponse.TwoVerificationResponse> twoVerificationResponses = determineAvailableVerificationTypes(userPerson, LoginType.PASSWORD);
        loginResponse.setTwoVerificationResponses(twoVerificationResponses);
        loginResponse.setIsCrossPlatformUser(false);

        PasswordLoginResponse response = new PasswordLoginResponse();
        response.setLoginResponse(loginResponse);
        response.setNeedSecondaryVerification(true);
        return response;
    }

    // ==================== 邮箱登录二次验证相关方法 ====================


    /**
     * 处理邮箱新设备登录
     */
    private LoginResponse handleEmailNewDeviceLogin(UserPerson userPerson,
                                                    VerifyEmailCodeAndLoginRequest request,
                                                    AccountDeviceDTO deviceInfo,
                                                    String clientIp) {

        CustomerAccount customerAccount = userPerson.getCustomerAccount();

        if (isPhoneOnlyUser(userPerson)) {
            // 仅手机号用户：直接登录 + 发安全通知
            bindUserDevice(userPerson, deviceInfo);

            // TODO: 发送安全通知短信
            sendSecurityNotification(userPerson.getPhone(), deviceInfo, clientIp);

            // 构建登录响应
            LoginResponse loginResponse = userPersonDTOAssembler.buildLoginResponse(userPerson, request.getPlatform(), deviceInfo);

            return loginResponse;
        } else {
            // 非仅手机号用户：邮箱登录 → 手机号验证码二次验证
            return triggerPhoneSecondaryVerification(userPerson, request, deviceInfo, clientIp);
        }
    }


    /**
     * 构建完整的登录响应（包含跨平台检测和安全提示）
     */
    private LoginResponse buildCompleteLoginResponse(UserPerson userPerson,
                                                     VerifySmsCodeAndBindPhoneRequest request,
                                                     AccountDeviceDTO deviceInfo) {
        // 构建基础登录响应
        LoginResponse loginResponse = userPersonDTOAssembler.buildLoginResponse(userPerson, request.getSource(), deviceInfo);

        // 检查跨平台用户
        boolean isCrossPlatformUser = false;
        if (request.getRegisterSource() == RegisterSourceEnum.APP) {
            CustomerAccount customerAccount = userPerson.getCustomerAccount();
            if (customerAccount != null) {
                String userRegisterSource = customerAccount.getRegisterSource();
                boolean isFirstAppLogin = customerAccount.getFirstAppLoginAt() == null;
                boolean isNonAppUser = StringUtils.isNotBlank(userRegisterSource) &&
                        !RegisterSourceEnum.APP.getName().equalsIgnoreCase(userRegisterSource);

                isCrossPlatformUser = isNonAppUser && isFirstAppLogin;

                if (isFirstAppLogin) {
                    customerAccount.setFirstAppLoginAt(LocalDateTime.now());
                    userPersonDomainService.updateCustomerAccount(customerAccount);
                }
            }
        }

        // 设置响应属性
        loginResponse.setIsCrossPlatformUser(isCrossPlatformUser);

        return loginResponse;
    }

    /**
     * 确定用户可用的验证方式
     *
     * @param userPerson          用户账户信息
     * @param originalLoginMethod 原始登录方式（PHONE 或 EMAIL）
     * @return 可用的验证方式列表，按优先级排序
     */
    private List<LoginResponse.TwoVerificationResponse> determineAvailableVerificationTypes(UserPerson userPerson, LoginType originalLoginMethod) {
        List<LoginResponse.TwoVerificationResponse> twoVerificationResponseList = new ArrayList<>();

        if (userPerson == null) {
            return twoVerificationResponseList;
        }
        CustomerAccount customerAccount = userPerson.getCustomerAccount();
        if (customerAccount == null) {
            return twoVerificationResponseList;
        }


        // 根据原始登录方式确定主要验证方式
        if (LoginType.SMS_CODE == originalLoginMethod) {
            // 手机号登录 → 优先邮箱验证
            if (StringUtils.isNotBlank(userPerson.getEmail())) {
                LoginResponse.TwoVerificationResponse twoVerificationResponse = new LoginResponse.TwoVerificationResponse();
                twoVerificationResponse.setVerificationType(LoginType.EMAIL_CODE.getName());
                //twoVerificationResponse.setMaskedTarget(DataMaskingUtil.maskEmail(userPerson.getEmail()));
                twoVerificationResponse.setMaskedTarget(userPerson.getEmail());
                twoVerificationResponseList.add(twoVerificationResponse);
            }
            // 如果有密码，也可以选择密码验证
            if (StringUtils.isNotBlank(customerAccount.getPassword())) {
                LoginResponse.TwoVerificationResponse twoVerificationResponse = new LoginResponse.TwoVerificationResponse();
                twoVerificationResponse.setVerificationType(LoginType.PASSWORD.getName());
                twoVerificationResponse.setMaskedTarget(userPerson.getPhone());
                twoVerificationResponseList.add(twoVerificationResponse);
            }
        } else if (LoginType.EMAIL_CODE == originalLoginMethod) {
            // 邮箱登录 → 优先手机号验证
            if (StringUtils.isNotBlank(userPerson.getPhone())) {
                LoginResponse.TwoVerificationResponse twoVerificationResponse = new LoginResponse.TwoVerificationResponse();
                twoVerificationResponse.setVerificationType(LoginType.SMS_CODE.getName());
                twoVerificationResponse.setMaskedTarget(userPerson.getPhone());
                twoVerificationResponseList.add(twoVerificationResponse);
            }
            // 如果有密码，也可以选择密码验证
            if (StringUtils.isNotBlank(customerAccount.getPassword())) {
                LoginResponse.TwoVerificationResponse twoVerificationResponse = new LoginResponse.TwoVerificationResponse();
                twoVerificationResponse.setVerificationType(LoginType.PASSWORD.getName());
                twoVerificationResponse.setMaskedTarget(userPerson.getPhone());
                twoVerificationResponseList.add(twoVerificationResponse);
            }
        } else if (LoginType.PASSWORD == originalLoginMethod) {
            // 密码登录 → 优先手机号验证
            if (StringUtils.isNotBlank(userPerson.getPhone())) {
                LoginResponse.TwoVerificationResponse twoVerificationResponse = new LoginResponse.TwoVerificationResponse();
                twoVerificationResponse.setVerificationType(LoginType.SMS_CODE.getName());
                twoVerificationResponse.setMaskedTarget(userPerson.getPhone());
                twoVerificationResponseList.add(twoVerificationResponse);
            }
            // 如果有邮箱，也可以选择邮箱验证
            if (StringUtils.isNotBlank(userPerson.getEmail())) {
                LoginResponse.TwoVerificationResponse twoVerificationResponse = new LoginResponse.TwoVerificationResponse();
                twoVerificationResponse.setVerificationType(LoginType.EMAIL_CODE.getName());
                twoVerificationResponse.setMaskedTarget(userPerson.getPhone());
                twoVerificationResponseList.add(twoVerificationResponse);
            }
        }

        // 如果没有可用的验证方式，返回空列表（不应该发生）
        if (CollectionUtils.isEmpty(twoVerificationResponseList)) {
            log.warn("用户{}(原始登录方式:{})没有可用的二次验证方式",
                    customerAccount.getId(), originalLoginMethod);
        }

        return twoVerificationResponseList;
    }

    /**
     * 检查是否在二次验证状态中（根据用户id和设备ID）
     *
     * @param userId   用户iD
     * @param deviceId 设备ID
     * @return 是否在二次验证状态中
     */
    public boolean isInSecondaryVerificationState(Long userId, String phoneOrEmail, String deviceId) {
        if (Objects.isNull(userId) || StringUtils.isBlank(deviceId)) {
            return false;
        }

        NewDeviceVerificationState newDeviceState = getNewDeviceState(userId, deviceId);
        if (Objects.isNull(newDeviceState)) {
            return false;
        }
        //发现当前用户有二次验证缓存，并且传过来的手机号或者邮箱与缓存一致，则返回 true
        String targetPhone = newDeviceState.getTargetPhone();
        String targetEmail = newDeviceState.getTargetEmail();
        return phoneOrEmail.equals(targetPhone) || phoneOrEmail.equals(targetEmail);
    }

    // ==================== 分布式锁相关方法 ====================

    /**
     * 使用 Redisson 分布式锁保护二次验证状态操作
     *
     * @param userId   用户ID
     * @param deviceId 设备ID
     * @param action   需要执行的操作
     * @param <T>      返回值类型
     * @return 操作结果
     */
    private <T> T processSecondaryVerificationWithLock(Long userId, String deviceId, java.util.function.Supplier<T> action) {
        String lockKey = "secondary_verification_lock:" + userId + ":" + deviceId;
        RLock lock = redissonClient.getLock(lockKey);

        try {
            // 尝试获取锁，最多等待3秒，锁定30秒后自动释放
            if (lock.tryLock(3, 30, TimeUnit.SECONDS)) {
                try {
                    return action.get();
                } finally {
                    // 只有当前线程持有锁时才释放
                    if (lock.isHeldByCurrentThread()) {
                        lock.unlock();
                    }
                }
            } else {
                throw new BusinessException(UsersErrorCode.OPERATION_IN_PROGRESS.getCode(),
                        UsersErrorCode.OPERATION_IN_PROGRESS.getMsg());
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new BusinessException(UsersErrorCode.SYSTEM_ERROR.getCode(), UsersErrorCode.SYSTEM_ERROR.getMsg());
        }
    }

    // ==================== 统一异常处理方法 ====================

    /**
     * 统一的登录异常处理
     *
     * @param e         异常对象
     * @param loginType 登录类型
     * @param operation 操作描述
     * @param params    相关参数（会进行脱敏处理）
     */
    private void handleLoginException(Exception e, String loginType, String operation, Object... params) {
        // 记录详细日志（脱敏处理）
        log.error("{}失败: operation={}, params={}", loginType, operation, maskSensitiveData(params), e);

        // 返回统一的错误响应
        if (e instanceof BusinessException) {
            throw (BusinessException) e;
        } else {
            // 根据登录类型返回对应的错误码
            UsersErrorCode errorCode = getLoginErrorCode(loginType);
            throw new BusinessException(errorCode.getCode(), errorCode.getMsg());
        }
    }

    /**
     * 根据登录类型获取对应的错误码
     *
     * @param loginType 登录类型
     * @return 错误码
     */
    private UsersErrorCode getLoginErrorCode(String loginType) {
        switch (loginType.toUpperCase()) {
            case "SMS":
            case "SMS_CODE":
                return UsersErrorCode.SMS_LOGIN_FAILED;
            case "EMAIL":
            case "EMAIL_CODE":
                return UsersErrorCode.EMAIL_LOGIN_FAILED;
            case "PASSWORD":
                return UsersErrorCode.PASSWORD_LOGIN_FAILED;
            default:
                return UsersErrorCode.LOGIN_FAILED;
        }
    }

    /**
     * 脱敏处理敏感数据
     *
     * @param params 参数数组
     * @return 脱敏后的参数数组
     */
    private Object[] maskSensitiveData(Object... params) {
        if (params == null || params.length == 0) {
            return params;
        }

        Object[] maskedParams = new Object[params.length];
        for (int i = 0; i < params.length; i++) {
            Object param = params[i];
            if (param instanceof String) {
                String str = (String) param;
                // 手机号脱敏
                if (str.matches("^09\\d{8}$")) {
                    maskedParams[i] = DataMaskingUtil.maskPhone(str);
                }
                // 邮箱脱敏
                else if (str.contains("@")) {
                    maskedParams[i] = DataMaskingUtil.maskEmail(str);
                }
                // 其他字符串保持原样
                else {
                    maskedParams[i] = param;
                }
            } else {
                maskedParams[i] = param;
            }
        }
        return maskedParams;
    }

    // ==================== IP地址验证相关方法 ====================

    /**
     * 验证IP地址一致性
     *
     * @param originalIp 原始IP地址
     * @param currentIp  当前IP地址
     * @param userId     用户ID（用于日志记录）
     */
    private void validateIpAddressConsistency(String originalIp, String currentIp, Long userId) {
        // 如果原始IP为空，跳过验证（向后兼容）
        if (StringUtils.isBlank(originalIp)) {
            log.debug("IP地址验证跳过，原始IP为空: userId={}", userId);
            return;
        }

        // 如果当前IP为空，记录警告但不阻断登录
        if (StringUtils.isBlank(currentIp)) {
            log.warn("IP地址验证警告，当前IP为空: userId={}, originalIp={}",
                    userId, IpAddressUtil.maskIpAddress(originalIp));
            return;
        }

        // 计算IP地址变化的风险等级
        IpAddressUtil.IpChangeRiskLevel riskLevel = IpAddressUtil.calculateIpChangeRisk(originalIp, currentIp);

        // 根据风险等级进行不同处理
        switch (riskLevel) {
            case NONE:
                // IP地址相同，无风险
                log.debug("IP地址验证通过，地址相同: userId={}, ip={}",
                        userId, IpAddressUtil.maskIpAddress(currentIp));
                break;

            case LOW:
                // 低风险，记录日志但不阻断
                log.info("IP地址发生低风险变化: userId={}, originalIp={}, currentIp={}, riskLevel={}",
                        userId, IpAddressUtil.maskIpAddress(originalIp),
                        IpAddressUtil.maskIpAddress(currentIp), riskLevel.getDescription());
                break;

            case MEDIUM:
                // 中等风险，记录警告日志
                log.warn("IP地址发生中等风险变化: userId={}, originalIp={}, currentIp={}, riskLevel={}",
                        userId, IpAddressUtil.maskIpAddress(originalIp),
                        IpAddressUtil.maskIpAddress(currentIp), riskLevel.getDescription());
                break;

            case HIGH:
                // 高风险，记录警告日志（可以考虑增加额外验证）
                log.warn("IP地址发生高风险变化: userId={}, originalIp={}, currentIp={}, riskLevel={}",
                        userId, IpAddressUtil.maskIpAddress(originalIp),
                        IpAddressUtil.maskIpAddress(currentIp), riskLevel.getDescription());

                // 可以在这里增加额外的安全措施，比如：
                // 1. 发送安全通知
                // 2. 记录安全事件
                // 3. 要求额外验证（暂时不实现，避免影响用户体验）

                // 可选：发送安全通知
                sendIpChangeSecurityNotification(userId, originalIp, currentIp);
                break;

            default:
                log.warn("未知的IP风险等级: userId={}, riskLevel={}", userId, riskLevel);
                break;
        }
    }

    /**
     * 发送IP地址变化的安全通知（可选功能）
     *
     * @param userId     用户ID
     * @param originalIp 原始IP地址
     * @param currentIp  当前IP地址
     */
    private void sendIpChangeSecurityNotification(Long userId, String originalIp, String currentIp) {
        try {
            // 这里可以实现安全通知逻辑，比如：
            // 1. 发送短信通知
            // 2. 发送邮件通知
            // 3. 站内消息通知
            // 4. 推送通知

            log.info("发送IP地址变化安全通知: userId={}, originalIp={}, currentIp={}",
                    userId, IpAddressUtil.maskIpAddress(originalIp), IpAddressUtil.maskIpAddress(currentIp));

            // 暂时只记录日志，实际项目中可以集成具体的通知服务

        } catch (Exception e) {
            log.error("发送IP地址变化安全通知失败: userId={}", userId, e);
        }
    }

    // ==================== 短信登录相关私有方法 ====================

    /**
     * 准备设备信息
     */
    private AccountDeviceDTO prepareDeviceInfo(AccountDeviceDTO deviceInfo, String clientIp) {
        if (deviceInfo == null) {
            deviceInfo = new AccountDeviceDTO();
        }
        deviceInfo.setIpAddress(clientIp);
        deviceInfo.setLoginTime(LocalDateTime.now());
        return deviceInfo;
    }


    /**
     * 从二次验证token中获取用户信息
     */
    private UserPerson getUserFromSecondaryToken(String secondaryVerificationToken, AccountDeviceDTO deviceInfo, SmsAndEmailBusinessType businessType) {


        // 解析token获取用户信息
        if (StringUtils.isBlank(secondaryVerificationToken)) {
            throw new BusinessException(UsersErrorCode.SECONDARY_VERIFICATION_TOKEN_INVALID.getCode(),
                    UsersErrorCode.SECONDARY_VERIFICATION_TOKEN_INVALID.getMsg());
        }
        SecondaryVerificationTokenUtil.SecondaryVerificationInfo tokenInfo =
                SecondaryVerificationTokenUtil.parseSecondaryVerificationToken(secondaryVerificationToken);
        Long userId = tokenInfo.getUserId();

        // 获取用户信息 检查用户是否存在
        UserPerson userPerson = userPersonDomainService.findById(userId);

        // 检查用户是否存在
        if (userPerson == null) {
            // 用户不存在，不是二次验证场景
            throw new BusinessException(UsersErrorCode.SECONDARY_VERIFICATION_USER_INFO_ERROR.getCode(),
                    UsersErrorCode.SECONDARY_VERIFICATION_USER_INFO_ERROR.getMsg());
        }
        // 检查是否有新设备验证状态（可能是从手机号，邮箱或密码登录过来的二次验证）
        NewDeviceVerificationState newDeviceState = getNewDeviceState(userPerson.getId(), deviceInfo.getDeviceId());
        if (newDeviceState == null) {
            throw new BusinessException(UsersErrorCode.SECONDARY_VERIFICATION_USER_INFO_ERROR.getCode(),
                    UsersErrorCode.SECONDARY_VERIFICATION_USER_INFO_ERROR.getMsg());
        }
        if (businessType == SmsAndEmailBusinessType.SECONDARY_PHONE_VERIFICATION) {
            //手机号二次验证 初始是邮箱登录或者密码登录
            if (newDeviceState.getOriginalLoginMethod().equals(LoginType.SMS_CODE.getName())) {
                throw new BusinessException(UsersErrorCode.SECONDARY_VERIFICATION_USER_INFO_ERROR.getCode(),
                        UsersErrorCode.SECONDARY_VERIFICATION_USER_INFO_ERROR.getMsg());
            }
        }
        if (businessType == SmsAndEmailBusinessType.SECONDARY_EMAIL_VERIFICATION) {
            //邮箱二次验证 初始 是手机登录或者密码登录
            if (newDeviceState.getOriginalLoginMethod().equals(LoginType.EMAIL_CODE.getName())) {
                throw new BusinessException(UsersErrorCode.SECONDARY_VERIFICATION_USER_INFO_ERROR.getCode(),
                        UsersErrorCode.SECONDARY_VERIFICATION_USER_INFO_ERROR.getMsg());
            }
        }

        // 验证设备ID一致性
        if (!Objects.equals(tokenInfo.getDeviceId(), deviceInfo.getDeviceId())) {
            throw new BusinessException(UsersErrorCode.SECONDARY_VERIFICATION_TOKEN_INVALID.getCode(),
                    UsersErrorCode.SECONDARY_VERIFICATION_TOKEN_INVALID.getMsg());
        }


        return userPerson;
    }

    /**
     * 从手机号获取用户并检查是否有二次验证状态
     */
    private UserPerson getUserFromPhoneAndCheckState(VerifySmsCodeAndBindPhoneRequest request, AccountDeviceDTO deviceInfo) {
        // 检查用户是否存在
        UserPerson userPerson = userPersonDomainService.findByPhoneAndSource(request.getPhone(), request.getSource().getCode());
        if (userPerson == null) {
            return null; // 用户不存在，不是二次验证场景
        }

        // 检查是否有新设备验证状态（可能是从邮箱或密码登录过来的二次验证）
        NewDeviceVerificationState newDeviceState = getNewDeviceState(userPerson.getId(), deviceInfo.getDeviceId());
        if (newDeviceState != null && !newDeviceState.getOriginalLoginMethod().equals(LoginType.SMS_CODE.getName())) {
            return userPerson; // 是二次验证场景
        }

        return null; // 不是二次验证场景
    }

    /**
     * 验证短信验证码
     */
    private void validateSmsCode(String phone, String code, SmsAndEmailBusinessType businessType) {
        boolean verified = verificationService.verifySmsCode(phone, code, businessType);
        if (!verified) {
            throw new BusinessException(UsersErrorCode.SMS_CODE_CHECK_ERROR.getCode(),
                    UsersErrorCode.SMS_CODE_CHECK_ERROR.getMsg());
        }
    }

    /**
     * 处理短信登录或注册
     */
    private VerifySmsCodeAndLoginResponse processSmsLoginOrRegister(
            VerifySmsCodeAndBindPhoneRequest request, AccountDeviceDTO deviceInfo, String clientIp) {

        // 检查用户是否存在
        UserPerson userPerson = userPersonDomainService.findByPhoneAndSource(request.getPhone(), request.getSource().getCode());
        boolean isExistingUser = Objects.nonNull(userPerson) && userPerson.getId() != null;

        if (isExistingUser) {
            // 已存在用户，处理登录
            return handleExistingUserSmsLogin(userPerson, request, deviceInfo, clientIp);
        } else {
            // 新用户，处理注册
            return handleNewUserSmsRegister(request, deviceInfo, clientIp);
        }
    }

    /**
     * 处理已存在用户的短信登录
     */
    private VerifySmsCodeAndLoginResponse handleExistingUserSmsLogin(
            UserPerson userPerson, VerifySmsCodeAndBindPhoneRequest request,
            AccountDeviceDTO deviceInfo, String clientIp) {

        CustomerAccount customerAccount = userPerson.getCustomerAccount();
        if (Objects.isNull(customerAccount)) {
            throw new BusinessException(UsersErrorCode.USER_ACCOUNT_NOT_EXIST.getCode(),
                    UsersErrorCode.USER_ACCOUNT_NOT_EXIST.getMsg());
        }

        // 检测是否为新设备（二次验证已在上层统一处理）
        boolean isNewDevice = checkIsNewDevice(customerAccount.getId(), deviceInfo.getDeviceId());
        if (isNewDevice) {
            // 新设备登录，需要二次验证
            return handleNewDeviceLogin(userPerson, request, deviceInfo, clientIp);
        } else {
            return buildSuccessfulSmsLoginResponse(userPerson, request, deviceInfo);
        }
    }


    /**
     * 处理新用户短信注册
     */
    private VerifySmsCodeAndLoginResponse handleNewUserSmsRegister(
            VerifySmsCodeAndBindPhoneRequest request, AccountDeviceDTO deviceInfo, String clientIp) {

        // 检查是否在二次验证状态中（防止换绑攻击）
        if (request.getBusinessType() == SmsAndEmailBusinessType.SECONDARY_PHONE_VERIFICATION) {
            if (isInSecondaryVerificationState(request.getUserId(), request.getPhone(), deviceInfo.getDeviceId())) {
                throw new BusinessException(UsersErrorCode.SECONDARY_VERIFICATION_ACCOUNT_CHANGE_FORBIDDEN.getCode(),
                        UsersErrorCode.SECONDARY_VERIFICATION_ACCOUNT_CHANGE_FORBIDDEN.getMsg());
            }
        }

        // 用户不存在，正常注册流程
        UserRegisterDomain userRegisterDomain = new UserRegisterDomain();
        userRegisterDomain.setPhone(request.getPhone());
        userRegisterDomain.setSource(request.getSource());
        userRegisterDomain.setAccountDevice(AccountDeviceDTOAssembler.convertToDomain(deviceInfo));
        userRegisterDomain.setRegistrationType(RegistrationTypeEnum.PHONE_REGISTRATION);
        userRegisterDomain.setRegisterSource(request.getRegisterSource());

        // 创建用户
        UserPerson userPerson = userPersonDomainService.register(userRegisterDomain);
        LoginResponse loginResponse = userPersonDTOAssembler.buildLoginResponse(userPerson, request.getSource(), deviceInfo);

        VerifySmsCodeAndLoginResponse response = new VerifySmsCodeAndLoginResponse();
        response.setLoginResponse(loginResponse);
        return response;

    }

    /**
     * 处理绑定清理逻辑
     */
    private void handleBindingCleanup(VerifySmsCodeAndBindPhoneRequest request, AccountDeviceDTO deviceInfo) {
        // 手机号未绑定邮箱/第三方 ,直接登录 无误验证后续令牌状态，可直接删除
        if (RegistrationTypeEnum.EMAIL_BINDING.equals(request.getRegistrationType()) ||
                RegistrationTypeEnum.THIRD_BINDING.equals(request.getRegistrationType())) {
            PhoneBindingRequest bindingRequest = new PhoneBindingRequest();
            bindingRequest.setToken(request.getTemporaryToken());
            bindingRequest.setDeviceId(deviceInfo.getDeviceId());
            authenticationService.verifyDeletePhoneBinding(bindingRequest);
        }
    }

    /**
     * 构建成功的短信登录响应
     */
    private VerifySmsCodeAndLoginResponse buildSuccessfulSmsLoginResponse(
            UserPerson userPerson, VerifySmsCodeAndBindPhoneRequest request, AccountDeviceDTO deviceInfo) {

        VerifySmsCodeAndLoginResponse response = new VerifySmsCodeAndLoginResponse();
        LoginResponse loginResponse = buildCompleteLoginResponse(userPerson, request, deviceInfo);
        response.setLoginResponse(loginResponse);

        // 检查是否存在已绑定的账号
        //if (RegistrationTypeEnum.EMAIL_BINDING.equals(request.getRegistrationType()) ||
        //    RegistrationTypeEnum.THIRD_BINDING.equals(request.getRegistrationType())) {
        //    PhoneExistsResponse phoneExistsResponse = new PhoneExistsResponse();
        //    phoneExistsResponse.setExists(true);
        //}

        return response;
    }

    // ==================== 邮箱登录相关私有方法 ====================

    /**
     * 准备邮箱登录设备信息
     */
    private AccountDeviceDTO prepareEmailDeviceInfo(AccountDeviceDTO deviceInfo, String clientIp) {
        if (deviceInfo == null) {
            deviceInfo = new AccountDeviceDTO();
        }
        deviceInfo.setIpAddress(clientIp);
        deviceInfo.setLoginTime(LocalDateTime.now());
        return deviceInfo;
    }


    /**
     * 从邮箱二次验证token中获取用户信息
     */
    private UserPerson getUserFromEmailSecondaryToken(
            VerifyEmailCodeAndLoginRequest request, AccountDeviceDTO deviceInfo) {


        // 解析token获取用户信息
        String secondaryVerificationToken = request.getSecondaryVerificationToken();
        if (StringUtils.isBlank(secondaryVerificationToken)) {
            throw new BusinessException(UsersErrorCode.SECONDARY_VERIFICATION_TOKEN_INVALID.getCode(),
                    UsersErrorCode.SECONDARY_VERIFICATION_TOKEN_INVALID.getMsg());
        }

        SecondaryVerificationTokenUtil.SecondaryVerificationInfo tokenInfo =
                SecondaryVerificationTokenUtil.parseSecondaryVerificationToken(secondaryVerificationToken);
        Long userId = tokenInfo.getUserId();
        String tokenDeviceId = tokenInfo.getDeviceId();


        // 获取用户信息 检查用户是否存在
        UserPerson userPerson = userPersonDomainService.findById(userId);
        if (userPerson == null) {
            // 用户不存在，不是二次验证场景
            throw new BusinessException(UsersErrorCode.SECONDARY_VERIFICATION_USER_INFO_ERROR.getCode(),
                    UsersErrorCode.SECONDARY_VERIFICATION_USER_INFO_ERROR.getMsg());
        }


        // 检查是否有新设备验证状态（可能是从邮箱或密码登录过来的二次验证）
        NewDeviceVerificationState newDeviceState = getNewDeviceState(userPerson.getId(), deviceInfo.getDeviceId());
        if (newDeviceState == null) {
            throw new BusinessException(UsersErrorCode.SECONDARY_VERIFICATION_USER_INFO_ERROR.getCode(),
                    UsersErrorCode.SECONDARY_VERIFICATION_USER_INFO_ERROR.getMsg());
        }
        //邮箱二次验证 初始 是手机登录或者密码登录
        if (newDeviceState.getOriginalLoginMethod().equals(LoginType.EMAIL_CODE.getName())) {
            throw new BusinessException(UsersErrorCode.SECONDARY_VERIFICATION_USER_INFO_ERROR.getCode(),
                    UsersErrorCode.SECONDARY_VERIFICATION_USER_INFO_ERROR.getMsg());
        }


        // 验证设备ID一致性
        if (!Objects.equals(tokenDeviceId, deviceInfo.getDeviceId())) {
            throw new BusinessException(UsersErrorCode.SECONDARY_VERIFICATION_TOKEN_INVALID.getCode(),
                    UsersErrorCode.SECONDARY_VERIFICATION_TOKEN_INVALID.getMsg());
        }

        return userPerson;
    }

    /**
     * 从邮箱获取用户并检查是否有二次验证状态
     */
    private UserPerson getUserFromEmailAndCheckState(
            VerifyEmailCodeAndLoginRequest request, AccountDeviceDTO deviceInfo) {

        // 检查用户是否存在
        UserPerson userPerson = userPersonDomainService.findByEmailAndSource(request.getEmail(), request.getPlatform().getCode());
        if (userPerson == null) {
            return null; // 用户不存在，不是二次验证场景
        }

        // 检查是否有新设备验证状态（可能是从手机号或密码登录过来的二次验证）
        NewDeviceVerificationState newDeviceState = getNewDeviceState(userPerson.getId(), deviceInfo.getDeviceId());
        if (newDeviceState != null && !newDeviceState.getOriginalLoginMethod().equals(LoginType.EMAIL_CODE.getName())) {
            return userPerson; // 是二次验证场景
        }

        return null; // 不是二次验证场景
    }

    /**
     * 处理邮箱跨登录二次验证
     */
    private UnifiedLoginResponse handleEmailCrossLoginSecondaryVerification(
            UserPerson userPerson, VerifyEmailCodeAndLoginRequest request, AccountDeviceDTO deviceInfo) {

        // 直接绑定设备并清除状态
        bindUserDevice(userPerson, deviceInfo);
        processSecondaryVerificationWithLock(userPerson.getId(), deviceInfo.getDeviceId(), () -> {
            clearNewDeviceState(userPerson.getId(), deviceInfo.getDeviceId());
            return null;
        });

        log.info("邮箱二次验证成功，绑定新设备: userId={}, deviceId={}",
                userPerson.getId(), deviceInfo.getDeviceId());

        // 构建成功响应
        LoginResponse loginResponse = userPersonDTOAssembler.buildLoginResponse(userPerson, request.getPlatform(), deviceInfo);
        return UnifiedLoginResponse.builder()
                .loginResponse(loginResponse)
                .needContinue(false)
                .build();
    }

    /**
     * 验证邮箱格式和验证码
     */
    private void validateEmailFormatAndCode(String email, String code, SmsAndEmailBusinessType businessType) {
        // 验证邮箱格式
        if (!EmailValidationUtil.isValidEmail(email)) {
            throw new BusinessException(UsersErrorCode.EMAIL_FORMAT_INVALID.getCode(),
                    UsersErrorCode.EMAIL_FORMAT_INVALID.getMsg());
        }

        // 验证邮箱验证码
        boolean verified = verificationService.verifyEmailCode(email, code, businessType);
        if (!verified) {
            throw new BusinessException(UsersErrorCode.EMAIL_CODE_CHECK_ERROR.getCode(),
                    UsersErrorCode.EMAIL_CODE_CHECK_ERROR.getMsg());
        }
    }

    /**
     * 处理邮箱登录或注册
     */
    private UnifiedLoginResponse processEmailLoginOrRegister(
            VerifyEmailCodeAndLoginRequest request, AccountDeviceDTO deviceInfo, String clientIp) {

        // 检查用户是否存在
        UserPerson userPerson = userPersonDomainService.findByEmailAndSource(request.getEmail(), request.getPlatform().getCode());
        boolean isExistingUser = Objects.nonNull(userPerson) && userPerson.getId() != null;

        if (isExistingUser) {
            // 已存在用户，处理登录
            return handleExistingUserEmailLogin(userPerson, request, deviceInfo, clientIp);
        } else {
            // 新用户，处理注册（邮箱未注册需要绑定手机号）
            return handleNewUserEmailRegister(request, deviceInfo, clientIp);
        }
    }

    /**
     * 处理已存在用户的邮箱登录
     */
    private UnifiedLoginResponse handleExistingUserEmailLogin(
            UserPerson userPerson, VerifyEmailCodeAndLoginRequest request,
            AccountDeviceDTO deviceInfo, String clientIp) {

        CustomerAccount customerAccount = userPerson.getCustomerAccount();
        if (Objects.isNull(customerAccount)) {
            throw new BusinessException(UsersErrorCode.USER_ACCOUNT_NOT_EXIST.getCode(),
                    UsersErrorCode.USER_ACCOUNT_NOT_EXIST.getMsg());
        }

        // 检测是否为新设备（二次验证已在上层统一处理）
        boolean isNewDevice = checkIsNewDevice(customerAccount.getId(), deviceInfo.getDeviceId());
        if (isNewDevice) {
            // 新设备登录，需要二次验证
            LoginResponse loginResponse = handleEmailNewDeviceLogin(userPerson, request, deviceInfo, clientIp);
            return UnifiedLoginResponse.builder()
                    .loginResponse(loginResponse)
                    .needContinue(false)
                    .build();
        } else {
            // 已有设备，正常登录（不需要重复绑定，注册时已绑定）
            LoginResponse loginResponse = userPersonDTOAssembler.buildLoginResponse(userPerson, request.getPlatform(), deviceInfo);
            return UnifiedLoginResponse.builder()
                    .loginResponse(loginResponse)
                    .needContinue(false)
                    .build();
        }
    }

    /**
     * 处理新用户邮箱注册（需要绑定手机号）
     */
    private UnifiedLoginResponse handleNewUserEmailRegister(
            VerifyEmailCodeAndLoginRequest request, AccountDeviceDTO deviceInfo, String clientIp) {

        // 二次验证场景下的换绑攻击检查
        if (request.getBusinessType() == SmsAndEmailBusinessType.SECONDARY_EMAIL_VERIFICATION) {
            // 二次验证场景下，如果邮箱未注册，说明是换绑攻击
            throw new BusinessException(UsersErrorCode.SECONDARY_VERIFICATION_EMAIL_CHANGED.getCode(),
                    UsersErrorCode.SECONDARY_VERIFICATION_EMAIL_CHANGED.getMsg());
        }

        // 正常场景：邮箱未注册，需要绑定手机号
        createPhoneBindingToken(request, clientIp);

        // 构建邮箱未注册的响应，需要绑定手机号
        return UnifiedLoginResponse.builder()
                .flowStatus(BindingPhoneVerifyStatus.R_EMAIL_VERIFIED)
                .needContinue(true)
                .nextStepHint("请绑定手机号")
                .build();
    }

    /**
     * 创建手机号绑定临时令牌
     */
    private void createPhoneBindingToken(VerifyEmailCodeAndLoginRequest request, String clientIp) {
        PhoneBindingRequest bindingRequest = new PhoneBindingRequest();
        bindingRequest.setEmail(request.getEmail());
        bindingRequest.setIpAddress(clientIp);
        bindingRequest.setUserAgent(request.getUserAgent());
        bindingRequest.setPlatform(request.getPlatform());
        bindingRequest.setBindingPhoneVerifyStatus(BindingPhoneVerifyStatus.INIT);

        AuthAccountDeviceApiDTO authAccountDeviceDTO = new AuthAccountDeviceApiDTO();
        if (Objects.nonNull(request.getDeviceInfo()) && !StringUtils.isBlank(request.getDeviceInfo().getDeviceId())) {
            BeanUtils.copyProperties(request.getDeviceInfo(), authAccountDeviceDTO);
        }
        bindingRequest.setAuthAccountDeviceDTO(authAccountDeviceDTO);

        authenticationService.createPhoneBinding(bindingRequest);
    }

    // ==================== 通用二次验证处理 ====================

    /**
     * 通用的二次验证处理方法
     * 支持短信和邮箱登录的二次验证场景
     *
     * @param request    登录请求（可以是 VerifySmsCodeAndBindPhoneRequest 或 VerifyEmailCodeAndLoginRequest）
     * @param deviceInfo 设备信息
     * @param clientIp   客户端IP
     * @param loginType  登录类型（"SMS" 或 "EMAIL"）
     * @return 如果是二次验证则返回响应，否则返回null
     */
    private Object handleUniversalSecondaryVerification(Object request, AccountDeviceDTO deviceInfo, String clientIp, LoginType loginType) {

        UserPerson userPerson = null;
        SmsAndEmailBusinessType businessType = null;
        String secondaryVerificationToken = null;
        HandleSmsSecondaryVerificationRequest verificationRequest = new HandleSmsSecondaryVerificationRequest();
        // 根据登录类型获取业务类型和用户信息
        if (LoginType.SMS_CODE == loginType) {
            VerifySmsCodeAndBindPhoneRequest smsRequest = (VerifySmsCodeAndBindPhoneRequest) request;
            businessType = smsRequest.getBusinessType();
            secondaryVerificationToken = smsRequest.getSecondaryVerificationToken();
            verificationRequest.setPlatform(smsRequest.getPlatform());
            verificationRequest.setPhone(smsRequest.getPhone());
            verificationRequest.setCode(smsRequest.getCode());
            verificationRequest.setBusinessType(businessType);
        } else if (LoginType.EMAIL_CODE == loginType) {
            VerifyEmailCodeAndLoginRequest emailRequest = (VerifyEmailCodeAndLoginRequest) request;
            businessType = emailRequest.getBusinessType();
            secondaryVerificationToken = emailRequest.getSecondaryVerificationToken();
            verificationRequest.setPlatform(emailRequest.getPlatform());
            verificationRequest.setEmail(emailRequest.getEmail());
            verificationRequest.setCode(emailRequest.getCode());
            verificationRequest.setBusinessType(businessType);
        }

        if (StringUtils.isNotBlank(secondaryVerificationToken)) {
            userPerson = getUserFromSecondaryToken(secondaryVerificationToken, deviceInfo, businessType);
            // 委托给二次验证应用服务处理

            LoginResponse loginResponse = secondaryVerificationApplicationService.handleSecondaryVerification(
                    verificationRequest, userPerson, deviceInfo, clientIp);

            // 根据登录类型返回对应的响应类型
            if (LoginType.SMS_CODE == loginType) {
                // 短信登录返回 VerifySmsCodeAndLoginResponse
                VerifySmsCodeAndLoginResponse smsResponse = new VerifySmsCodeAndLoginResponse();
                smsResponse.setLoginResponse(loginResponse);
                return smsResponse;
            } else if (LoginType.EMAIL_CODE == loginType) {
                // 邮箱登录返回 UnifiedLoginResponse
                return UnifiedLoginResponse.builder()
                        .loginResponse(loginResponse)
                        .needContinue(false)
                        .build();
            }
        }


        return null; // 不是二次验证场景
    }

}
