package com.yuanchuan.user.application.service.handler;

import com.yuanchuan.authentication.api.service.AuthenticationService;
import com.yuanchuan.authentication.context.enums.BindingPhoneVerifyStatus;
import com.yuanchuan.common.enums.users.UsersErrorCode;
import com.yuanchuan.common.enums.users.login.LoginType;
import com.yuanchuan.common.exception.BusinessException;
import com.yuanchuan.user.api.request.UnifiedLoginRequest;
import com.yuanchuan.user.api.request.UnifiedLoginResponse;
import com.yuanchuan.user.api.request.VerifyEmailCodeAndLoginRequest;
import com.yuanchuan.user.api.service.VerificationService;
import com.yuanchuan.user.application.annotation.LoginLog;
import com.yuanchuan.user.application.service.impl.UserLoginServiceImpl;
import com.yuanchuan.user.domain.service.UserPersonDomainService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 邮箱验证码登录处理器
 */
@Slf4j
@Component
public class EmailLoginHandler implements LoginHandler {

    @Autowired
    private UserLoginServiceImpl userLoginService;

    @Autowired
    private VerificationService verificationService;

    @Autowired
    private UserPersonDomainService userPersonDomainService;

    @DubboReference(version = "1.0", group = "${dubbo.consumer.group.authentication}")
    private AuthenticationService authenticationService;

    @Override
    @LoginLog(loginType = LoginType.EMAIL_CODE, autoInfer = true, description = "C端邮箱验证码登录")
    public UnifiedLoginResponse handle(UnifiedLoginRequest request) {
        // 验证参数
        if (StringUtils.isEmpty(request.getEmail())) {
            throw new BusinessException(UsersErrorCode.EMAIL_EMPTY.getCode(), UsersErrorCode.EMAIL_EMPTY.getMsg());
        }
        if (StringUtils.isEmpty(request.getCode())) {
            throw new BusinessException(UsersErrorCode.VERIFICATION_CODE_EMPTY.getCode(), UsersErrorCode.VERIFICATION_CODE_EMPTY.getMsg());
        }


        // 邮箱已注册，直接登录
        // 构建邮箱验证码登录请求
        VerifyEmailCodeAndLoginRequest emailRequest = new VerifyEmailCodeAndLoginRequest();
        emailRequest.setEmail(request.getEmail());
        emailRequest.setCode(request.getCode());
        emailRequest.setDeviceInfo(request.getDeviceInfo());
        emailRequest.setPlatform(request.getPlatform());
        emailRequest.setBusinessType(request.getBusinessType());
        emailRequest.setSecondaryVerificationToken(request.getSecondaryVerificationToken());


        // 调用邮箱验证码登录服务
        // 构建统一登录响应
        return userLoginService.verifyEmailCodeAndLogin(emailRequest, request.getClientIp());
    }

    @Override
    public boolean supports(LoginType loginType, BindingPhoneVerifyStatus status) {
        return loginType == LoginType.EMAIL_CODE && status == BindingPhoneVerifyStatus.INIT;
    }


}
