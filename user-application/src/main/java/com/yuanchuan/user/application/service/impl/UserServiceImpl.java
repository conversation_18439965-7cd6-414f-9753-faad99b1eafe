package com.yuanchuan.user.application.service.impl;

import com.yuanchuan.authentication.api.dto.AuthThirdUserApiDTO;
import com.yuanchuan.authentication.api.response.UserLoginEmailBindingVerifyResponse;
import com.yuanchuan.authentication.api.service.AuthenticationService;
import com.yuanchuan.authentication.content.model.UserContext;
import com.yuanchuan.authentication.content.utils.AuthUserContextUtils;
import com.yuanchuan.common.enums.users.UsersErrorCode;
import com.yuanchuan.common.exception.BusinessException;
import com.yuanchuan.common.utils.DateUtils;
import com.yuanchuan.user.api.dto.*;
import com.yuanchuan.user.api.service.UserService;
import com.yuanchuan.user.api.service.VerificationService;
import com.yuanchuan.user.domain.model.TaiwanRegion;
import com.yuanchuan.user.domain.model.UserPerson;
import com.yuanchuan.user.domain.model.UserThirdPartyBinding;
import com.yuanchuan.user.domain.repository.TaiwanRegionRepository;
import com.yuanchuan.user.domain.service.PasswordPolicyService;
import com.yuanchuan.user.domain.service.ThirdPartyBindingService;
import com.yuanchuan.user.domain.service.UserPersonDomainService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户信息服务实现类
 */
@Slf4j
@Service
@DubboService(version = "1.0", group = "${dubbo.group}", delay = -1, retries = -1, timeout = 600000)
public class UserServiceImpl implements UserService {

    @DubboReference(version = "1.0", group = "${dubbo.consumer.group.authentication}")
    private AuthenticationService authenticationService;

    @Autowired
    private UserPersonDomainService userPersonDomainService;

    @Autowired
    private PasswordPolicyService passwordPolicyService;

    @Autowired
    private VerificationService verificationService;

    @Autowired
    private ThirdPartyBindingService thirdPartyBindingService;

    @Autowired
    private TaiwanRegionRepository taiwanRegionRepository;

    @Override
    public UserResponse getUserProfile() {
        Long userId = AuthUserContextUtils.getCurrentUserId();
        if(userId == null) {
            log.info("UserServiceImpl#getUserProfile userId is null");
            throw new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(),UsersErrorCode.USER_NOT_EXIST.getMsg());
        }

        UserPerson userPerson = userPersonDomainService.findById(userId);
        UserResponse response = new UserResponse();
        response.setId(userPerson.getId());
        response.setNickname(userPerson.getCustomerAccount().getNickName());
        response.setAvatar(userPerson.getCustomerAccount().getAvatar());
        response.setGender(userPerson.getCustomerAccount().getGender());
        response.setPhone(userPerson.getPhone());
        response.setEmail(userPerson.getEmail());
        if(userPerson.getCustomerAccount().getBirthday() != null) {
            response.setBirthday(DateUtils.localDateToDate(userPerson.getCustomerAccount().getBirthday()));
        }

        boolean isPwd = userPerson.getCustomerAccount().getPassword()!= null ? true : false;
        response.setPwdFlag(isPwd);

        response.setAddress(userPerson.getCustomerAccount().getAddress());
        return response;
    }

    @Override
    public void updateNickname(UserProfileUpdateDTO.NicknameUpdateRequest request) {
        Long userId = AuthUserContextUtils.getCurrentUserId();
        if(userId == null) {
            log.info("UserServiceImpl#updateNickname userId is null");
            throw new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(),UsersErrorCode.USER_NOT_EXIST.getMsg());
        }

        // 调用领域服务更新昵称
        userPersonDomainService.updateNickname(userId, request.getNickname());
    }

    @Override
    public void updateAvatar(UserProfileUpdateDTO.AvatarUpdateRequest request) {
        Long userId = AuthUserContextUtils.getCurrentUserId();
        if(userId == null) {
            log.info("UserServiceImpl#updateAvatar userId is null");
            throw new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(),UsersErrorCode.USER_NOT_EXIST.getMsg());
        }


        userPersonDomainService.updateAvatar(userId, request.getAvatrUrl());
    }

    @Override
    public void updateGender( UserProfileUpdateDTO.GenderUpdateRequest request) {

        Long userId = AuthUserContextUtils.getCurrentUserId();
        if(userId == null) {
            log.info("UserServiceImpl#updateGender userId is null");
            throw new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(),UsersErrorCode.USER_NOT_EXIST.getMsg());
        }

        // 调用领域服务更新性别
        userPersonDomainService.updateGender(userId, request.getGender());
    }

    @Override
    public void updateBirthday(UserProfileUpdateDTO.BirthdayUpdateRequest request) {

        Long userId = AuthUserContextUtils.getCurrentUserId();
        if(userId == null) {
            log.info("UserServiceImpl#updateBirthday userId is null");
            throw new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(),UsersErrorCode.USER_NOT_EXIST.getMsg());
        }
        // 调用领域服务更新生日
        userPersonDomainService.updateBirthday(userId, DateUtils.dateToLocalDate(request.getBirthday()));
    }

    @Override
    public void updateAddress(UserProfileUpdateDTO.AddressUpdateRequest request) {

        Long userId = AuthUserContextUtils.getCurrentUserId();
        if(userId == null) {
            log.info("UserServiceImpl#updateAddress userId is null");
            throw new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(),UsersErrorCode.USER_NOT_EXIST.getMsg());
        }

        // 转换经纬度
        BigDecimal longitude = null;
        BigDecimal latitude = null;
        if (request.getLongitude() != null && !request.getLongitude().isEmpty()) {
            longitude = new BigDecimal(request.getLongitude());
        }
        if (request.getLatitude() != null && !request.getLatitude().isEmpty()) {
            latitude = new BigDecimal(request.getLatitude());
        }

        // 调用领域服务更新常居地
        userPersonDomainService.updateAddress(userId, request.getAddress(),
                request.getProvinceCode(), request.getCityCode(), request.getRegionCode(),
                request.getProvinceName(), request.getCityName(), request.getRegionName(),
                longitude, latitude);
    }

    @Override
    public void verifyPhone(UserAccountUpdateDTO.PhoneVerifyRequest request) {
        Long userId = AuthUserContextUtils.getCurrentUserId();
        if (userId == null) {
            log.info("UserServiceImpl#verifyPhone userId is null");
            throw new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg());
        }

        // 参数校验
        if (request == null || request.getPhone() == null || request.getPhone().isEmpty()) {
            throw new IllegalArgumentException("手机号不能为空");
        }

        // 调用领域服务验证手机号
        boolean verified = userPersonDomainService.verifyPhone(userId, request.getPhone());
        if (!verified) {
            throw new BusinessException(UsersErrorCode.PHONE_NOT_MATCH.getCode(), UsersErrorCode.PHONE_NOT_MATCH.getMsg());
        }
    }

    @Override
    public void updatePhone(UserAccountUpdateDTO.PhoneUpdateRequest request) {
        UserContext userContext = AuthUserContextUtils.getCurrentUser();
        if (userContext == null) {
            log.info("UserServiceImpl#updatePhone userId is null");
            throw new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg());
        }

        // 验证短信验证码
        boolean verified = verificationService.verifySmsCode(request.getNewPhone(), request.getCode(),request.getBusinessType());
        if (!verified) {
            throw new BusinessException(UsersErrorCode.SMS_CODE_CHECK_ERROR.getCode(), UsersErrorCode.SMS_CODE_CHECK_ERROR.getMsg());
        }

        // 检查设备换绑次数限制
        if (request.getDeviceId() != null && !request.getDeviceId().isEmpty() &&
                !userPersonDomainService.checkDevicePhoneUpdateLimit(request.getDeviceId())) {
            throw new BusinessException(UsersErrorCode.DEVICE_LIMIT_EXCEEDED.getCode(), "每個裝置每日最多可更換綁定手機號碼 3 次");
        }

        // 调用领域服务换绑手机号
        boolean updated = userPersonDomainService.updatePhoneAndSource(userContext.getUserId(), request.getNewPhone(), userContext.getDeviceId(), userContext.getPlatform());
        if (!updated) {
            throw new BusinessException(UsersErrorCode.PHONE_UPDATE_ERROR.getCode(), UsersErrorCode.PHONE_UPDATE_ERROR.getMsg());
        }

    }

    @Override
    public void bindEmail(UserAccountUpdateDTO.EmailBindRequest request) {
        UserContext userContext = AuthUserContextUtils.getCurrentUser();
        if (userContext == null) {
            log.info("UserServiceImpl#bindEmail userId is null");
            throw new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg());
        }
        // 如果提供了验证码，则验证邮箱验证码
        if (request.getCode() != null && !request.getCode().isEmpty()) {
            boolean verified = verificationService.verifyEmailCode(request.getEmail(), request.getCode(),request.getBusinessType());
            if (!verified) {
                throw new BusinessException(UsersErrorCode.EMAIL_UPDATE_ERROR.getCode(), UsersErrorCode.EMAIL_UPDATE_ERROR.getMsg());
            }
        }

        // 调用领域服务绑定邮箱
        boolean bound = userPersonDomainService.bindEmailAndSource(userContext.getUserId(), request.getEmail(),userContext.getPlatform());
        if (!bound) {
            throw new BusinessException(UsersErrorCode.EMAIL_UPDATE_ERROR.getCode(), UsersErrorCode.EMAIL_UPDATE_ERROR.getMsg());
        }
    }

    @Override
    public void verifyOriginalEmail(UserAccountUpdateDTO.EmailVerifyRequest request){
        Long userId = AuthUserContextUtils.getCurrentUserId();
        if (userId == null) {
            log.info("UserServiceImpl#verifyOriginalEmail userId is null");
            throw new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg());
        }

        // 调用领域服务验证原邮箱
        boolean emailVerified = userPersonDomainService.verifyOriginalEmail(userId, request.getEmail());
        if (!emailVerified) {
            throw new BusinessException(UsersErrorCode.EMAIL_NOT_MATCH.getCode(), UsersErrorCode.EMAIL_NOT_MATCH.getMsg());
        }
    }

    @Override
    public void setPassword(UserAccountUpdateDTO.PasswordSetRequest request) {
        UserContext userContext =  AuthUserContextUtils.getCurrentUser();
        if (userContext == null) {
            log.info("UserServiceImpl#setPassword userContext is null");
            throw new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg());
        }

        Long userId =userContext.getUserId();
        if (userId == null) {
            log.info("UserServiceImpl#setPassword userId is null");
            throw new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg());
        }

        // 参数校验
        if (request == null || request.getNewPassword() == null || request.getNewPassword().isEmpty()) {
            throw new IllegalArgumentException("密码不能为空");
        }

        // 密码策略验证
        String invalidReason = passwordPolicyService.getInvalidReason(request.getNewPassword());
        if (invalidReason != null) {
            throw new BusinessException(UsersErrorCode.PASSWORD_POLICY_ERROR.getCode(), invalidReason);
        }

        // 调用领域服务设置密码
        boolean set = userPersonDomainService.setPassword(userId, request.getNewPassword());
        if (!set) {
            throw new BusinessException(UsersErrorCode.PASSWORD_UPDATE_ERROR.getCode(), UsersErrorCode.PASSWORD_UPDATE_ERROR.getMsg());
        }

        // 删除用户 token 信息
        authenticationService.removeToken(userId, userContext.getDeviceId());
    }

    @Override
    public void verifyOriginalPassword(UserAccountUpdateDTO.PasswordUpdateRequest request) {

        Long userId = AuthUserContextUtils.getCurrentUserId();
        if (userId == null) {
            log.info("UserServiceImpl#updatePassword userId is null");
            throw new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg());
        }

        // 参数校验
        if (request == null || request.getOldPassword() == null || request.getOldPassword().isEmpty()) {
            throw new IllegalArgumentException("原密码不能为空");
        }

        // 调用领域服务修改密码
        boolean updated = userPersonDomainService.verifyOriginalPassword(userId, request.getOldPassword());
        if (!updated) {
            throw new BusinessException(UsersErrorCode.PASSWORD_VERIFY_ERROR.getCode(), UsersErrorCode.PASSWORD_VERIFY_ERROR.getMsg());
        }
    }

    @Override
    public ThirdPartyBindingDTO.ThirdPartyBindingListResponse getThirdPartyBindingList() {
        try {
            // 从上下文中获取用户信息
            Long userId = AuthUserContextUtils.getCurrentUserId();
            if (userId == null) {
                log.info("UserServiceImpl#getThirdPartyBindingList userId is null");
                throw new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg());
            }

            // 调用领域服务获取绑定列表
            List<UserThirdPartyBinding> bindingList = thirdPartyBindingService.getBindingList(userId);

            // 转换为DTO
            List<ThirdPartyBindingDTO.ThirdPartyBindingInfo> bindingInfoList = bindingList.stream()
                    .map(binding -> {
                        ThirdPartyBindingDTO.ThirdPartyBindingInfo info = new ThirdPartyBindingDTO.ThirdPartyBindingInfo();
                        info.setId(binding.getId());
                        info.setPlatform(binding.getPlatform());
                        return info;
                    })
                    .collect(java.util.stream.Collectors.toList());

            // 构建响应
            ThirdPartyBindingDTO.ThirdPartyBindingListResponse response = new ThirdPartyBindingDTO.ThirdPartyBindingListResponse();
            response.setBindingList(bindingInfoList);

            return response;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取第三方绑定列表异常", e);
            throw new BusinessException(UsersErrorCode.THIRD_PARTY_BIND_ERROR.getCode(), UsersErrorCode.THIRD_PARTY_BIND_ERROR.getMsg());
        }
    }

    @Override
    public void unbindThirdParty(ThirdPartyBindingDTO.UnbindThirdPartyRequest request) {
        // 参数校验
        if (request == null || request.getBindingId() == null) {
            throw new IllegalArgumentException("绑定ID不能为空");
        }

        // 从上下文中获取用户信息
        Long userId = AuthUserContextUtils.getCurrentUserId();
        if (userId == null) {
            log.info("UserServiceImpl#unbindThirdParty userId is null");
            throw new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg());
        }

        // 调用领域服务解绑第三方账号
        boolean result = thirdPartyBindingService.unbindThirdParty(userId, request.getBindingId());
        if (!result) {
            throw new BusinessException(UsersErrorCode.UNBIND_FAILED.getCode(), UsersErrorCode.UNBIND_FAILED.getMsg());
        }
    }

    @Override
    public void bindThirdParty(ThirdPartyBindingDTO.BindThirdPartyRequest request) {

        // 参数校验
        if (request == null) {
            throw new IllegalArgumentException("请求参数不能为空");
        }
        if (request.getSub() == null || request.getSub().isEmpty()) {
            throw new IllegalArgumentException("第三方平台用户唯一标识不能为空");
        }


        // 从上下文中获取用户信息
        UserContext userContext = AuthUserContextUtils.getCurrentUser();
        if (userContext == null) {
            log.info("UserServiceImpl#bindThirdParty userId is null");
            throw new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg());
        }

            //直接从临时令牌中获取DeviceInfo
            UserLoginEmailBindingVerifyResponse tokenVerifyInfo = authenticationService.getTokenVerifyInfo(request.getSub());
            AuthThirdUserApiDTO authThirdUserApiDTO = tokenVerifyInfo.getAuthThirdUserDTO();

        // 调用领域服务绑定第三方账号
        boolean result = thirdPartyBindingService.bindThirdParty(
                userContext.getUserId(),
                authThirdUserApiDTO.getPlatform(),
                authThirdUserApiDTO.getSub(),
                authThirdUserApiDTO.getNickname(),
                authThirdUserApiDTO.getAvatar(),
                authThirdUserApiDTO.getGender(),
                authThirdUserApiDTO.getAccessToken(),
                authThirdUserApiDTO.getRefreshToken(),
                authThirdUserApiDTO.getExpiresAt(),
                authThirdUserApiDTO.getTokenType(),
                authThirdUserApiDTO.getScope(),
                authThirdUserApiDTO.getRefreshExpiresAt()
        );
    }

    @Override
    public AddressLookupDTO.AddressResponse getAddressByCoordinate(AddressLookupDTO.CoordinateRequest request) {
        if (request == null || request.getLongitude() == null || request.getLatitude() == null) {
            throw new IllegalArgumentException("经纬度不能为空");
        }

        log.info("UserServiceImpl#getAddressByCoordinate longitude:{}, latitude:{}",
                request.getLongitude(), request.getLatitude());

        // 转换经纬度为BigDecimal
        BigDecimal longitude = new BigDecimal(request.getLongitude());
        BigDecimal latitude = new BigDecimal(request.getLatitude());

        // 调用仓储接口查询最近的行政区划
        TaiwanRegion region = taiwanRegionRepository.findByCoordinate(longitude, latitude)
                .orElseThrow(() -> new BusinessException(UsersErrorCode.ADDRESS_NOT_FOUND.getCode(), UsersErrorCode.ADDRESS_NOT_FOUND.getMsg()));

        // 构建地址响应
        return buildAddressResponse(region);
    }

    @Override
    public List<AddressLookupDTO.AddressResponse> getAddressByName(AddressLookupDTO.NameRequest request) {
        if (request == null || request.getName() == null || request.getName().isEmpty()) {
            throw new IllegalArgumentException("搜索名称不能为空");
        }

        log.info("UserServiceImpl#getAddressByName name:{}", request.getName());

        // 调用仓储接口根据名称查询行政区划
        List<TaiwanRegion> region = taiwanRegionRepository.findByName(request.getName())
                .orElseThrow(() -> new BusinessException(UsersErrorCode.ADDRESS_NOT_FOUND.getCode(), UsersErrorCode.ADDRESS_NOT_FOUND.getMsg()));


        // 构建返回的地址响应列表
        List<AddressLookupDTO.AddressResponse> responses = region.stream()
                .map(this::buildAddressResponse)
                .collect(Collectors.toList());


        return responses;
    }

    @Override
    public void verifyNewEmail(UserAccountUpdateDTO.EmailVerifyRequest request) {
        UserContext userContext = AuthUserContextUtils.getCurrentUser();
        if (userContext.getUserId() == null) {
            log.info("UserServiceImpl#verifyNewEmail userId is null");
            throw new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg());
        }

        // 调用领域服务验证原邮箱
        boolean emailVerified = userPersonDomainService.verifyNewEmail(userContext.getUserId(),userContext.getPlatform(), request.getEmail());
        if (!emailVerified) {
            throw new BusinessException(UsersErrorCode.EMAIL_ALREADY_REGISTERED.getCode(), UsersErrorCode.EMAIL_ALREADY_REGISTERED.getMsg());
        }
    }


    /**
     * 构建地址响应
     *
     * @param region 行政区划
     * @return 地址响应
     */
    private AddressLookupDTO.AddressResponse buildAddressResponse(TaiwanRegion region) {
        if (region == null) {
            return null;
        }

        // 获取省市区信息
        String provinceName = null;
        String cityName = null;
        String regionName = null;
        String provinceCode = null;
        String cityCode = null;
        String regionCode = null;

        // 根据层级确定省市区
        if (region.getLevel() == 4) { // 区/县级
            regionName = region.getName();
            regionCode = region.getCode();

            // 查询市级
            TaiwanRegion city = taiwanRegionRepository.findParentById(region.getId()).orElse(null);
            if (city != null) {
                cityName = city.getName();
                cityCode = city.getCode();

                // 查询省级
                TaiwanRegion province = taiwanRegionRepository.findParentById(city.getId()).orElse(null);
                if (province != null) {
                    provinceName = province.getName();
                    provinceCode = province.getCode();
                }
            }
        } else if (region.getLevel() == 3) { // 市级
            cityName = region.getName();
            cityCode = region.getCode();

            // 查询省级
            TaiwanRegion province = taiwanRegionRepository.findParentById(region.getId()).orElse(null);
            if (province != null) {
                provinceName = province.getName();
                provinceCode = province.getCode();
            }
        } else if (region.getLevel() == 2) { // 省级
            provinceName = region.getName();
            provinceCode = region.getCode();
        }

        // 构建完整地址
        StringBuilder addressBuilder = new StringBuilder();
        if (provinceName != null) {
            addressBuilder.append(provinceName);
        }
        if (cityName != null) {
            addressBuilder.append(cityName);
        }
        if (regionName != null) {
            addressBuilder.append(regionName);
        }

        // 如果没有构建出地址，则使用区域名称
        String address = addressBuilder.length() > 0 ? addressBuilder.toString() : region.getName();

        // 构建响应
        return AddressLookupDTO.AddressResponse.builder()
                .address(address)
                .provinceCode(provinceCode)
                .cityCode(cityCode)
                .regionCode(regionCode)
                .provinceName(provinceName)
                .cityName(cityName)
                .regionName(regionName)
                .longitude(region.getCenterLongitude() != null ? region.getCenterLongitude().toString() : null)
                .latitude(region.getCenterLatitude() != null ? region.getCenterLatitude().toString() : null)
                .build();
    }
}
