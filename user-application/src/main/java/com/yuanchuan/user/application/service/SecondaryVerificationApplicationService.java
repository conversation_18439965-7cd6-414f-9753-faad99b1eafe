package com.yuanchuan.user.application.service;

import com.yuanchuan.user.api.dto.AccountDeviceDTO;
import com.yuanchuan.user.api.dto.NewDeviceVerificationState;
import com.yuanchuan.user.api.request.HandleSmsSecondaryVerificationRequest;
import com.yuanchuan.user.api.request.PasswordLoginRequest;
import com.yuanchuan.user.api.request.VerifyEmailCodeAndLoginRequest;
import com.yuanchuan.user.api.request.VerifySmsCodeAndBindPhoneRequest;
import com.yuanchuan.user.api.response.LoginResponse;
import com.yuanchuan.user.domain.model.UserPerson;

/**
 * 二次验证应用服务接口
 * 
 * 应用服务负责：
 * - 协调领域服务完成业务用例
 * - 处理应用层的业务流程
 * - 不包含核心业务逻辑（由领域服务处理）
 * - 事务边界管理
 * 
 * 二次验证应用服务专门处理用户在新设备登录时的安全验证流程，
 * 包括验证状态管理、验证流程控制、安全检查等应用层逻辑。
 * 
 * <AUTHOR>
 * @date 2025/1/20
 * @version 1.0
 */
public interface SecondaryVerificationApplicationService {

    /**
     * 处理登录的二次验证应用用例
     * 
     * 当用户在新设备上使用短信验证码登录时，如果检测到需要二次验证，
     * 则通过此应用服务协调相关领域服务完成验证流程。
     * 
     * 应用流程：
     * 1. 验证token有效性
     * 2. 获取验证状态
     * 3. 调用领域服务进行业务验证
     * 4. 更新设备绑定状态
     * 5. 清理验证状态
     * 
     * @param request 短信验证码登录请求
     * @param userPerson 用户领域对象
     * @param deviceInfo 设备信息DTO
     * @param clientIp 客户端IP地址
     * @return 登录响应结果
     */
    LoginResponse handleSecondaryVerification(
            HandleSmsSecondaryVerificationRequest request,
            UserPerson userPerson,
            AccountDeviceDTO deviceInfo,
            String clientIp);



    /**
     * 触发手机号二次验证应用用例
     * 
     * 当用户通过邮箱登录但需要手机号二次验证时，
     * 创建验证状态并发送验证码。
     * 
     * @param userPerson 用户领域对象
     * @param request 邮箱登录请求
     * @param deviceInfo 设备信息DTO
     * @param clientIp 客户端IP地址
     * @return 需要二次验证的响应
     */
    void triggerPhoneSecondaryVerification(
            UserPerson userPerson,
            VerifySmsCodeAndBindPhoneRequest request,
            AccountDeviceDTO deviceInfo,
            String clientIp);

    /**
     * 触发邮箱二次验证应用用例
     * 
     * 当用户通过手机号登录但需要邮箱二次验证时，
     * 创建验证状态并发送验证码。
     * 
     * @param userPerson 用户领域对象
     * @param request 短信登录请求
     * @param deviceInfo 设备信息DTO
     * @param clientIp 客户端IP地址
     * @return 需要二次验证的响应
     */
    void triggerEmailSecondaryVerification(
            UserPerson userPerson,
            VerifyEmailCodeAndLoginRequest request,
            AccountDeviceDTO deviceInfo,
            String clientIp);

    /**
     * 触发密码登录的二次验证应用用例
     * 
     * 当用户通过密码登录但需要二次验证时，
     * 创建验证状态并发送验证码。
     * 
     * @param userPerson 用户领域对象
     * @param request 密码登录请求
     * @param deviceInfo 设备信息DTO
     * @param clientIp 客户端IP地址
     * @return 需要二次验证的响应
     */
    void triggerPasswordSecondaryVerification(
            UserPerson userPerson,
            PasswordLoginRequest request,
            AccountDeviceDTO deviceInfo,
            String clientIp);

    /**
     * 获取新设备验证状态
     * 
     * 从基础设施层获取验证状态信息
     * 
     * @param userId 用户ID
     * @param deviceId 设备ID
     * @return 验证状态，不存在或已过期返回null
     */
    NewDeviceVerificationState getNewDeviceState(Long userId, String deviceId);

    /**
     * 保存新设备验证状态
     * 
     * 将验证状态保存到基础设施层
     * 
     * @param state 新设备验证状态
     */
    void saveNewDeviceState(NewDeviceVerificationState state);

    /**
     * 清除新设备验证状态
     * 
     * 从基础设施层清除验证状态
     * 
     * @param userId 用户ID
     * @param deviceId 设备ID
     */
    void clearNewDeviceState(Long userId, String deviceId);

    /**
     * 检查用户是否处于二次验证状态
     * 
     * 业务查询方法，用于判断用户当前的验证状态
     * 
     * @param userId 用户ID
     * @param deviceId 设备ID
     * @return true表示处于二次验证状态，false表示不在
     */
    boolean isInSecondaryVerificationState(Long userId, String deviceId);
}
