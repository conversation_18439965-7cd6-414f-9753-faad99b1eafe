<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>user-parent</artifactId>
        <groupId>com.yuanchuan.user</groupId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>user-application</artifactId>

    <properties>

    </properties>

    <dependencies>
        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.yuanchuan.user</groupId>
            <artifactId>user-api</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.yuanchuan.user</groupId>
            <artifactId>user-commom</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.yuanchuan.user</groupId>
            <artifactId>user-domain</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.yuanchuan.authentication</groupId>
            <artifactId>authentication-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

        <!-- Bean Validation -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

    </dependencies>
</project>
